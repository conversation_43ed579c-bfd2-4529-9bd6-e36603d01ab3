<template>
  <view class="scss-demo-page">
    <!-- 页面标题 -->
    <view class="header">
      <text class="title">SCSS功能演示</text>
      <text class="subtitle">展示变量、混合器和工具类的使用</text>
    </view>

    <!-- 按钮演示 -->
    <view class="section">
      <text class="section-title">按钮样式演示</text>
      <view class="button-group">
        <button class="btn-primary">主要按钮</button>
        <button class="btn-secondary">次要按钮</button>
        <button class="btn-outline">边框按钮</button>
        <button class="btn-large">大按钮</button>
      </view>
    </view>

    <!-- 卡片演示 -->
    <view class="section">
      <text class="section-title">卡片样式演示</text>
      <view class="card-demo">
        <text class="card-title">这是一个卡片标题</text>
        <text class="card-content">这是卡片内容，使用了SCSS混合器来创建统一的卡片样式。</text>
        <view class="card-footer">
          <text class="card-date">2024-01-01</text>
          <view class="card-tag">标签</view>
        </view>
      </view>
    </view>

    <!-- 渐变演示 -->
    <view class="section">
      <text class="section-title">渐变效果演示</text>
      <view class="gradient-demo">
        <view class="gradient-bg">
          <text class="gradient-text">渐变背景</text>
        </view>
        <text class="gradient-title">渐变文字效果</text>
      </view>
    </view>

    <!-- 动画演示 -->
    <view class="section">
      <text class="section-title">动画效果演示</text>
      <view class="animation-demo">
        <view class="fade-item" @click="triggerAnimation">点击触发淡入动画</view>
        <view class="slide-item" @click="triggerSlide">点击触发滑入动画</view>
      </view>
    </view>

    <!-- 响应式演示 -->
    <view class="section">
      <text class="section-title">响应式设计演示</text>
      <view class="responsive-demo">
        <text class="responsive-text">这段文字在不同屏幕尺寸下会有不同的字体大小</text>
      </view>
    </view>

    <!-- 工具类组合演示 -->
    <view class="section">
      <text class="section-title">工具类组合演示</text>
      <view class="utility-demo">
        <view class="flex-between bg-white p-3 rounded-lg mb-2">
          <text class="text-lg font-bold text-primary">标题</text>
          <text class="text-sm text-gray">时间</text>
        </view>
        <view class="flex-center bg-gray p-4 rounded">
          <text class="text-base text-dark">居中内容</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ScssDemo',
  data() {
    return {
      animationTriggered: false
    }
  },
  methods: {
    triggerAnimation() {
      // 触发动画的逻辑
      console.log('触发淡入动画')
    },
    triggerSlide() {
      // 触发滑入动画的逻辑
      console.log('触发滑入动画')
    }
  }
}
</script>

<style lang="scss" scoped>
// 引入变量和混合器
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.scss-demo-page {
  min-height: 100vh;
  background-color: $gray-light;
  padding: $spacing-3;
}

.header {
  @include flex-column-center;
  @include card-style($spacing-4, 'lg');
  margin-bottom: $spacing-3;
  @include gradient-bg($primary-color, $secondary-color, 'to bottom right');
  
  .title {
    font-size: $text-2xl;
    font-weight: bold;
    color: white;
    margin-bottom: $spacing-1;
  }
  
  .subtitle {
    font-size: $text-base;
    color: rgba(255, 255, 255, 0.9);
  }
}

.section {
  @include card-style($spacing-3, 'md');
  margin-bottom: $spacing-3;
  
  .section-title {
    font-size: $text-lg;
    font-weight: bold;
    color: $primary-color;
    margin-bottom: $spacing-2;
    display: block;
  }
}

// 按钮样式演示
.button-group {
  @include flex-between;
  flex-wrap: wrap;
  gap: $spacing-2;
  
  button {
    flex: 1;
    min-width: 120rpx;
  }
}

.btn-primary {
  @include button-style($primary-color, white, 'normal');
}

.btn-secondary {
  @include button-style($secondary-color, white, 'normal');
}

.btn-outline {
  @include button-style(transparent, $primary-color, 'normal');
  border: 1rpx solid $primary-color;
}

.btn-large {
  @include button-style($primary-color, white, 'large');
}

// 卡片演示
.card-demo {
  @include card-style($spacing-3, 'sm');
  background-color: $background-color;
  
  .card-title {
    font-size: $text-lg;
    font-weight: bold;
    color: $text-color;
    margin-bottom: $spacing-2;
    display: block;
  }
  
  .card-content {
    font-size: $text-base;
    color: $gray-medium;
    line-height: 1.6;
    margin-bottom: $spacing-3;
    display: block;
  }
  
  .card-footer {
    @include flex-between;
    
    .card-date {
      font-size: $text-sm;
      color: $gray-medium;
    }
    
    .card-tag {
      background-color: $primary-color;
      color: white;
      font-size: $text-xs;
      padding: $spacing-1 $spacing-2;
      border-radius: $border-radius-sm;
    }
  }
}

// 渐变演示
.gradient-demo {
  @include flex-column;
  gap: $spacing-3;
  
  .gradient-bg {
    @include gradient-bg($primary-color, $secondary-color);
    @include flex-center;
    height: 120rpx;
    border-radius: $border-radius-lg;
    
    .gradient-text {
      color: white;
      font-size: $text-lg;
      font-weight: bold;
    }
  }
  
  .gradient-title {
    @include text-gradient($primary-color, $secondary-color);
    font-size: $text-xl;
    font-weight: bold;
    text-align: center;
  }
}

// 动画演示
.animation-demo {
  @include flex-column;
  gap: $spacing-2;
  
  .fade-item,
  .slide-item {
    @include flex-center;
    background-color: $primary-color;
    color: white;
    padding: $spacing-3;
    border-radius: $border-radius-lg;
    cursor: pointer;
    transition: $transition-normal;
    
    &:active {
      @include fade-in(0.3s);
      transform: scale(0.98);
    }
  }
  
  .slide-item:active {
    @include slide-up(0.3s);
  }
}

// 响应式演示
.responsive-demo {
  .responsive-text {
    font-size: $text-base;
    color: $text-color;
    line-height: 1.6;
    display: block;
    
    // 在不同屏幕尺寸下应用不同样式
    @include respond-to('md') {
      font-size: $text-lg;
    }
    
    @include respond-to('lg') {
      font-size: $text-xl;
    }
  }
}

// 工具类组合演示
.utility-demo {
  // 这里主要使用工具类，不需要额外的SCSS代码
  // 展示了工具类的强大组合能力
}
</style>
