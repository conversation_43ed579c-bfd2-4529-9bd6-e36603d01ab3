<template>
  <view class="search-page">
    <!-- 搜索头部 -->
    <view class="search-header">
      <view class="search-box">
        <input 
          v-model="searchKeyword" 
          placeholder="搜索政策文件、问题、新闻、政策解读"
          class="search-input"
          @confirm="performSearch()"
          focus
        />
        <view @click="performSearch()" class="search-btn">
          <image src="/static/icons/search.svg" class="search-icon" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 搜索类型选择 -->
    <view class="search-types">
      <view 
        v-for="type in searchTypes" 
        :key="type.value"
        @click="switchSearchType(type.value)"
        class="type-btn"
        :class="{ active: currentType === type.value }"
      >
        {{ type.label }}
      </view>
    </view>

    <!-- 搜索结果 -->
    <view class="search-results" v-if="hasSearched">
      <!-- 结果统计 -->
      <view class="result-summary" v-if="totalResults > 0">
        <text>找到 {{ totalResults }} 条相关结果</text>
      </view>

      <!-- 政策文件结果 -->
      <view v-if="currentType === 'policy' && searchResults.policies && searchResults.policies.length > 0" class="result-section">
        <view class="section-title">
          <text class="icon">📋</text>
          <text>政策文件</text>
        </view>
        <view class="result-list">
          <view 
            v-for="item in searchResults.policies" 
            :key="item.id"
            class="result-item"
            @click="gotoDetail('policy', item.id)"
          >
            <view class="item-title" v-html="highlightKeyword(item.title)"></view>
            <view class="item-content">{{ truncateText(item.content, 100) }}</view>
            <view class="item-meta">
              <text class="category">{{ item.category }}</text>
              <view class="meta-right">
                <text class="date">👍{{ item.like_count || 0 }}</text>
                <text class="date">👁️{{ item.view_count || 0 }}</text>
                <text class="date">{{ formatDate(item.publish_date) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 新闻结果 -->
      <view v-if="currentType === 'news' && searchResults.news && searchResults.news.length > 0" class="result-section">
        <view class="section-title">
          <text class="icon">📰</text>
          <text>要闻动态</text>
        </view>
        <view class="result-list">
          <view 
            v-for="item in searchResults.news" 
            :key="item.id"
            class="result-item"
            @click="gotoDetail('news', item.id)"
          >
            <view class="item-title" v-html="highlightKeyword(item.title)"></view>
            <view class="item-content">{{ truncateText(item.content, 100) }}</view>
            <view class="item-meta">
              <text class="category">{{ item.category }}</text>
              <view class="meta-right">
                <text class="date">👍{{ item.like_count || 0 }}</text>
                <text class="date">👁️{{ item.view_count || 0 }}</text>
                <text class="date">{{ formatDate(item.publish_date) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- FAQ结果 -->
      <view v-if="currentType === 'faq' && searchResults.faq && searchResults.faq.length > 0" class="result-section">
        <view class="section-title">
          <text class="icon">❓</text>
          <text>热门问题</text>
        </view>
        <view class="result-list">
          <view 
            v-for="item in searchResults.faq" 
            :key="item.id"
            class="result-item"
            @click="gotoDetail('faq', item.id)"
          >
            <view class="item-title" v-html="highlightKeyword(item.question)"></view>
            <view class="item-content">{{ truncateText(item.answer, 100) }}</view>
            <view class="item-meta">
              <text class="category">{{ item.category }}</text>
              <view class="meta-right">
                <text class="date">👍{{ item.like_count || 0 }}</text>
                <text class="date">👁️{{ item.view_count || 0 }}</text>
                <text class="date">{{ formatDate(item.answer_date) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 政策解读结果 -->
      <view v-if="currentType === 'interpretation' && searchResults.interpretations && searchResults.interpretations.length > 0" class="result-section">
        <view class="section-title">
          <text class="icon">🎥</text>
          <text>政策解读</text>
        </view>
        <view class="interpretation-grid">
          <view 
            class="interpretation-item" 
            v-for="(item, index) in searchResults.interpretations" 
            :key="item.id"
            @click="gotoDetail('interpretation', item.id)"
          >
            <view class="interpretation-cover">
              <image 
                v-if="getVideoThumbnail(item) && !item.thumbnail_error" 
                :src="getVideoThumbnail(item)" 
                class="interpretation-image"
                mode="aspectFill"
                @error="onVideoThumbnailError(item)"
              />
              <view 
                v-else 
                class="interpretation-image"
                :class="'interpretation-bg-' + (index % 6 + 1)"
              ></view>
              <view v-if="item.video_url" class="play-icon">
                <text>▶</text>
              </view>
              <view v-else class="text-icon">
                <text>📄</text>
              </view>
            </view>
            <view class="interpretation-title" v-html="highlightKeyword(item.title)"></view>
          </view>
        </view>
      </view>

      <!-- 无结果提示 -->
      <view v-if="totalResults === 0" class="no-results">
        <text class="no-results-icon">🔍</text>
        <text class="no-results-text">没有找到相关内容</text>
        <text class="no-results-tip">请尝试使用其他关键词</text>
      </view>
      
      <!-- 搜索结果的加载更多 -->
      <view class="search-load-more" v-if="totalResults > 0">
        <!-- 加载更多按钮 -->
        <view class="load-more" v-if="hasMoreData && !isLoading" @click="loadMoreSearch">
          <text class="load-more-text">加载更多搜索结果</text>
          <text class="load-more-icon">↓</text>
        </view>
        
        <!-- 加载中状态 -->
        <view class="loading-more" v-if="isLoading">
          <text class="loading-more-text">正在搜索...</text>
          <text class="loading-more-icon">⏳</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view class="no-more" v-if="!hasMoreData && totalResults > 0">
          <text class="no-more-text">搜索结果已全部显示</text>
          <text class="no-more-icon">🔍</text>
        </view>
      </view>
    </view>

    <!-- 类型列表展示 -->
    <view class="category-list" v-if="!hasSearched">
      <view class="list-header">
        <text class="icon">{{ getTypeIcon(currentType) }}</text>
        <text>{{ getTypeTitle(currentType) }}</text>
      </view>
      <view class="category-items" v-if="categoryData.length > 0">
        <view 
          v-for="item in categoryData" 
          :key="item.id"
          class="category-item"
          @click="gotoDetail(currentType, item.id)"
        >
          <view class="item-title">{{ item.title || item.question }}</view>
          <view class="item-content">{{ truncateText(item.content || item.answer, 120) }}</view>
          <view class="item-meta">
            <text class="category" v-if="item.category">{{ item.category }}</text>
            <view class="meta-right">
                <text class="date">👍{{ item.like_count || 0 }}</text>
                <text class="date">👁️{{ item.view_count || 0 }}</text>
                <text class="date">{{ formatDate(item.publish_date || item.answer_date) }}</text>
            </view>
          </view>
        </view>
        
        <!-- 加载更多按钮 -->
        <view class="load-more" v-if="hasMoreData && !isLoading" @click="loadMore">
          <text class="load-more-text">加载更多</text>
          <text class="load-more-icon">↓</text>
        </view>
        
        <!-- 加载中状态 -->
        <view class="loading-more" v-if="isLoading">
          <text class="loading-more-text">正在加载...</text>
          <text class="loading-more-icon">⏳</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view class="no-more" v-if="!hasMoreData && categoryData.length > 0">
          <text class="no-more-text">没有更多数据了</text>
          <text class="no-more-icon">📝</text>
        </view>
      </view>
      <!-- 加载中状态 -->
      <view class="loading-state" v-else>
        <view class="loading-icon">⏳</view>
        <view class="loading-text">正在加载{{ getTypeTitle(currentType) }}...</view>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      searchKeyword: '',
      currentType: 'policy',
      hasSearched: false,
      searchResults: {},
      totalResults: 0,
      categoryData: [], // 类型数据
      
      // 分页相关状态
      currentPage: 1,
      hasMoreData: true,
      isLoading: false,
      totalPages: 1,

      searchTypes: [
        { label: '政策', value: 'policy' },
        { label: '新闻', value: 'news' },
        { label: '问题', value: 'faq' },
        { label: '解读', value: 'interpretation' }
      ]
    }
  },

  onLoad(options) {
    try {
      if (options && options.type) {
        const validTypes = this.searchTypes.map(t => t.value)
        if (validTypes.includes(options.type)) {
          this.currentType = options.type
        }
      }

      // 页面加载时自动加载当前类型的数据
      console.log('搜索页面加载完成，当前类型:', this.currentType)
      this.loadCategoryData(this.currentType).catch(err => {
        console.error('分类数据加载失败:', err)
      })
    } catch (err) {
      console.error('onLoad错误:', err)
    }
  },

  methods: {
    async performSearch() {
      if (!this.searchKeyword.trim()) {
        // 当搜索词为空时，重置为分类列表视图
        this.hasSearched = false
        this.searchResults = {}
        this.totalResults = 0
        // 加载当前类型的分类数据
        await this.loadCategoryData(this.currentType, false)
        return
      }

      // 如果是新搜索（不是加载更多），重置页码
      if (!this.isLoading) {
        this.currentPage = 1
        this.hasMoreData = true
      }

      this.hasSearched = true
      this.isLoading = true

      try {
        const params = {
          q: this.searchKeyword,  // 根据API文档使用q参数
          type: 'all',
          page: this.currentPage,
          per_page: 20
        }

        console.log('搜索参数:', params)
        const res = await api.search(params)
        console.log('搜索响应:', res)
        
        // 根据API文档，搜索结果在data.items中，需要按类型分组
        const items = res.data?.items || []
        console.log('搜索原始数据:', items)
        
        const newGroupedResults = this.groupSearchResults(items)
        console.log('分组后的新搜索结果:', newGroupedResults)
        
        if (this.currentPage === 1) {
          // 第一页，替换结果
          this.searchResults = newGroupedResults
        } else {
          // 后续页，合并结果
          this.mergeSearchResults(newGroupedResults)
        }
        
        console.log('最终搜索结果:', this.searchResults)
        
        // 更新分页信息
        this.totalResults = res.data?.total || 0
        this.totalPages = res.data?.pages || 1
        this.hasMoreData = this.currentPage < this.totalPages
        
        if (this.currentPage === 1) {
          // 首次搜索后，默认选中第一个tab
          this.currentType = 'policy';
        }
        
        console.log('搜索结果总数:', this.totalResults, '当前页:', this.currentPage, '总页数:', this.totalPages)

      } catch (error) {
        console.error('搜索失败:', error)
        uni.showToast({
          title: '搜索失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },

    groupSearchResults(items) {
      const grouped = {
        policies: [],
        news: [],
        faq: [],
        interpretations: []
      }
      
      items.forEach(item => {
        switch (item.type) {
          case 'policy':
            grouped.policies.push({
              id: item.id,
              title: item.title,
              content: item.content,
              category: item.category || '政策文件',
              publish_date: item.publish_date || item.created_at
            })
            break
          case 'news':
            grouped.news.push({
              id: item.id,
              title: item.title,
              content: item.content,
              category: item.category || '要闻动态',
              publish_date: item.publish_date || item.created_at
            })
            break
          case 'faq':
            grouped.faq.push({
              id: item.id,
              question: item.title,
              answer: item.content,
              category: item.category || '常见问题',
              answer_date: item.publish_date || item.created_at
            })
            break
          case 'interpretation':
            grouped.interpretations.push({
              id: item.id,
              title: item.title,
              content: item.content,
              category: item.category || '政策解读',
              publish_date: item.publish_date || item.created_at,
              video_url: item.video_url
            })
            break
        }
      })
      
      return grouped
    },

    async switchSearchType(type) {
      this.currentType = type
      
      // 只有在非搜索结果视图下，切换tab才重新加载分类列表
      if (!this.hasSearched) {
        this.hasSearched = false
        await this.loadCategoryData(type, false)
      }
    },

    async loadCategoryData(type, isLoadMore = false) {
      console.log('开始加载分类数据，类型:', type, '是否加载更多:', isLoadMore)
      
      if (!isLoadMore) {
        // 重置分页状态
        this.currentPage = 1
        this.hasMoreData = true
        this.categoryData = []
      }
      
      this.isLoading = true
      
      try {
        let response = null
        const params = { 
          page: this.currentPage, 
          per_page: 10 
        }
        
        switch (type) {
          case 'policy':
            console.log('正在请求政策数据...', params)
            response = await api.getPolicies(params)
            console.log('政策数据响应:', response)
            break
          case 'news':
            console.log('正在请求新闻数据...', params)
            response = await api.getNews(params)
            console.log('新闻数据响应:', response)
            break
          case 'faq':
            console.log('正在请求FAQ数据...', params)
            response = await api.getFaqs(params)
            console.log('FAQ数据响应:', response)
            break
          case 'interpretation':
            console.log('正在请求政策解读数据...', params)
            response = await api.getInterpretations(params)
            console.log('政策解读数据响应:', response)
            break
        }
        
        if (response && response.data) {
          const newData = response.data.items || response.data.data || []
          console.log('解析后的新数据:', newData)
          
          // 更新分页信息
          this.totalPages = response.data.pages || 1
          this.hasMoreData = this.currentPage < this.totalPages
          
          if (isLoadMore) {
            // 追加数据
            this.categoryData = [...this.categoryData, ...newData]
          } else {
            // 替换数据
            this.categoryData = newData
          }
          
          console.log('当前页:', this.currentPage, '总页数:', this.totalPages, '是否有更多:', this.hasMoreData)
          console.log('最终设置的分类数据:', this.categoryData)
        }
      } catch (error) {
        console.error('加载分类数据失败:', error)
        
        // 显示错误提示
        uni.showToast({
          title: `加载${this.getTypeTitle(type)}失败`,
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.isLoading = false
      }
    },

    getTypeIcon(type) {
      const icons = {
        policy: '📋',
        news: '📰',
        faq: '❓',
        interpretation: '🎥'
      }
      return icons[type] || '📄'
    },

    getTypeTitle(type) {
      const titles = {
        policy: '政策文件',
        news: '要闻动态',
        faq: '常见问题',
        interpretation: '政策解读'
      }
      return titles[type] || '全部内容'
    },

    gotoDetail(type, id) {
      let url = ''
      switch (type) {
        case 'policy':
          url = `/pages/policy/detail?id=${id}`
          break
        case 'news':
          url = `/pages/news/detail?id=${id}`
          break
        case 'faq':
          url = `/pages/faq/detail?id=${id}`
          break
        case 'interpretation':
          url = `/pages/interpretation/detail?id=${id}`
          break
      }
      
      if (url) {
        uni.navigateTo({ url })
      }
    },

    highlightKeyword(text) {
      if (!this.searchKeyword || !text) return text
      const regex = new RegExp(this.searchKeyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'gi');
      return text.replace(regex, `<span style="color: #1E90FF; font-weight: bold;">${this.searchKeyword}</span>`);
    },

    truncateText(text, maxLength) {
      if (!text) return ''
      // 移除HTML标签
      const plainText = text.replace(/<[^>]*>/g, '')
      return plainText.length > maxLength 
        ? plainText.substring(0, maxLength) + '...'
        : plainText
    },

    formatDate(dateStr) {
      if (!dateStr) return ''
      return new Date(dateStr).toLocaleDateString()
    },

    // 新增：加载更多方法
    async loadMore() {
      if (this.isLoading || !this.hasMoreData) return
      
      this.currentPage++
      await this.loadCategoryData(this.currentType, true)
    },

    async loadMoreSearch() {
      if (this.isLoading || !this.hasMoreData) return
      
      this.currentPage++
      await this.performSearch()
    },

    // 新增：合并搜索结果
    mergeSearchResults(newResults) {
      Object.keys(newResults).forEach(key => {
        if (this.searchResults[key]) {
          this.searchResults[key] = [...this.searchResults[key], ...newResults[key]]
        } else {
          this.searchResults[key] = newResults[key]
        }
      })
    },
    
    getVideoThumbnail(item) {
      if (item && (item.thumbnail || item.cover_image)) {
        return item.thumbnail || item.cover_image
      }
      if (item && item.video_url) {
        if (item.video_url.includes('example.com')) {
          return item.video_url.replace('.mp4', '_thumb.jpg')
        }
      }
      return ''
    },

    onVideoThumbnailError(item) {
      console.log(`视频缩略图加载失败:`, item)
      this.$set(item, 'thumbnail_error', true)
    },
  }
}
</script>

<style lang="scss" scoped>
.search-page {
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  min-height: 100vh;
  position: relative;
}

/* 顶部重庆山城风格背景 */
.search-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, 
    #1E90FF 0%, 
    #4A90E2 30%, 
    #6BA3E8 60%, 
    rgba(107, 163, 232, 0.6) 80%, 
    rgba(138, 180, 240, 0.3) 90%, 
    transparent 100%
  );
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.search-page::after {
  content: '';
  position: absolute;
  top: 140rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}

.search-header {
  background: transparent;
  padding: 25rpx 30rpx 15rpx;
  position: relative;
  z-index: 2;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  height: 50rpx;
  padding: 0 25rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 35rpx;
  font-size: 28rpx;
  border: none;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-btn {
  width: 70rpx;
  height: 70rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 35rpx;
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.search-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
}

.search-types {
  display: flex;
  gap: 20rpx;
  padding: 15rpx 30rpx 25rpx;
  background: transparent;
  position: relative;
  z-index: 3;
  overflow-x: auto;
  white-space: nowrap;
  justify-content: flex-start;
  
  /* 显示滚动条 */
  &::-webkit-scrollbar {
    height: 6rpx;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3rpx;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.4);
    border-radius: 3rpx;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.6);
  }
}

.type-btn {
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  color: #666;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(15rpx);
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-width: 100rpx;
  text-align: center;
  
  &.active {
    background: linear-gradient(135deg, #1E90FF, #4A90E2);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4rpx 20rpx rgba(30, 144, 255, 0.3);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.search-results {
  padding: 30rpx;
  margin-top: 20rpx;
}

.result-summary {
  margin-bottom: 30rpx;
  padding: 20rpx 30rpx;
  background: rgba(30, 144, 255, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1E90FF;
  font-weight: 600;
  text-align: center;
}

.result-section {
  margin-bottom: 50rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 15rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, rgba(30, 144, 255, 0.1), rgba(74, 144, 226, 0.1));
  border-radius: 16rpx;
  border-left: 6rpx solid #1E90FF;
  
  .icon {
    font-size: 38rpx;
  }
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  background: white;
  padding: 35rpx;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4rpx solid transparent;
  
  &:active {
    transform: scale(0.98);
    border-left-color: #1E90FF;
    box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);
  }
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.item-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-right {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.category {
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.date {
  color: #999;
  font-size: 26rpx;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  text-align: center;
}

.no-results-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.no-results-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.no-results-tip {
  font-size: 26rpx;
  color: #999;
}

.search-load-more {
  padding: 30rpx;
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);
  }
}

.load-more-icon {
  font-size: 24rpx;
  animation: bounce 2s infinite;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  border-radius: 50rpx;
  font-size: 28rpx;
}

.loading-more-icon {
  animation: spin 1s linear infinite;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.9);
  color: #999;
  border-radius: 50rpx;
  font-size: 26rpx;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-6rpx);
  }
  60% {
    transform: translateY(-3rpx);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.category-list {
  padding: 30rpx;
  margin-top: 20rpx;
}

.list-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
}

.category-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.category-item {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4rpx solid transparent;
  min-height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  
  &:active {
    transform: scale(0.98);
    border-left-color: #1E90FF;
    box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);
  }
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
  
  /* 标题最多显示2行 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 89rpx; /* 32rpx * 1.4 * 2 ≈ 89rpx */
}

.item-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15rpx;
  flex: 1;
  
  /* 内容最多显示3行 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 134rpx; /* 28rpx * 1.6 * 3 ≈ 134rpx */
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-right {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.category {
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.date {
  color: #999;
  font-size: 26rpx;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  text-align: center;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.loading-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.load-more-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 新增：政策解读网格样式 */
.interpretation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.interpretation-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.interpretation-cover {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 宽高比 */
}

.interpretation-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  pointer-events: none;
}

.text-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40rpx;
  pointer-events: none;
}

.interpretation-title {
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.4;
  flex: 1; /* 确保标题占满剩余空间 */
}

/* 渐变色背景占位符 */
.interpretation-bg-1 { background: linear-gradient(135deg, #FF9A8B 0%, #FF6A88 100%); }
.interpretation-bg-2 { background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%); }
.interpretation-bg-3 { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); }
.interpretation-bg-4 { background: linear-gradient(135deg, #fccb90 0%, #d57eeb 100%); }
.interpretation-bg-5 { background: linear-gradient(135deg, #5ee7df 0%, #b490ca 100%); }
.interpretation-bg-6 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
</style> 