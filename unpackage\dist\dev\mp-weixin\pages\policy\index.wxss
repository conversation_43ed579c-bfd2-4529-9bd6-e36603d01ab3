@charset "UTF-8";
.policy-page.data-v-223ccfa4 {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  padding-bottom: 140rpx;
  /* 为自定义tabBar留出空间 */
  position: relative;
}
/* 顶部重庆山城风格背景 */
.policy-page.data-v-223ccfa4::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, #1E90FF 0%, #4A90E2 30%, #6BA3E8 60%, rgba(107, 163, 232, 0.6) 80%, rgba(138, 180, 240, 0.3) 90%, transparent 100%);
  z-index: 1;
}
/* 重庆山城剪影装饰 */
.policy-page.data-v-223ccfa4::after {
  content: '';
  position: absolute;
  top: 140rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}
.search-section.data-v-223ccfa4 {
  padding: 30rpx;
  background: white;
  border-bottom: 1rpx solid #e5e5e5;
  position: relative;
  z-index: 3;
}
.search-box.data-v-223ccfa4 {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
}
.search-icon.data-v-223ccfa4 {
  font-size: 28rpx;
  margin-right: 20rpx;
  color: #999;
}
.search-placeholder.data-v-223ccfa4 {
  color: #999;
  font-size: 28rpx;
}
.filter-section.data-v-223ccfa4 {
  background: white;
  border-bottom: 1rpx solid #e5e5e5;
  position: relative;
  z-index: 3;
}
.filter-scroll.data-v-223ccfa4 {
  white-space: nowrap;
}
.filter-list.data-v-223ccfa4 {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}
.filter-item.data-v-223ccfa4 {
  padding: 16rpx 32rpx;
  background: #f5f5f5;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s;
}
.filter-item.active.data-v-223ccfa4 {
  background: #1E90FF;
  /* 主题蓝色 */
  color: white;
}
.policy-list-section.data-v-223ccfa4 {
  padding: 0 30rpx;
}
.policy-item.data-v-223ccfa4 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
.policy-header.data-v-223ccfa4 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.policy-title.data-v-223ccfa4 {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  line-height: 1.4;
  margin-right: 20rpx;
}
.policy-category.data-v-223ccfa4 {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}
.policy-content.data-v-223ccfa4 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}
.policy-footer.data-v-223ccfa4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
.policy-date.data-v-223ccfa4 {
  font-size: 24rpx;
  color: #999;
}
.policy-stats.data-v-223ccfa4 {
  display: flex;
  gap: 24rpx;
  font-size: 24rpx;
  color: #999;
}
.stat-item.data-v-223ccfa4 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.icon.data-v-223ccfa4 {
  font-size: 28rpx;
}
.load-more.data-v-223ccfa4 {
  padding: 40rpx;
  text-align: center;
}
.load-more-btn.data-v-223ccfa4 {
  display: inline-block;
  padding: 20rpx 60rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
}
.no-more.data-v-223ccfa4 {
  padding: 40rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
.empty-state.data-v-223ccfa4 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
}
.empty-icon.data-v-223ccfa4 {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}
.empty-text.data-v-223ccfa4 {
  font-size: 32rpx;
  color: #999;
}

