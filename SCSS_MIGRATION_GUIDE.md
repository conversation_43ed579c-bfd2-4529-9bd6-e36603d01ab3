# SCSS样式迁移指南

## 迁移概述

项目已从传统CSS迁移到SCSS，提供了更强大的样式管理功能。本指南将帮助您了解迁移的变化和如何使用新的样式系统。

## 主要变化

### 1. 文件结构变化

**迁移前**:
```
static/css/common.css    # 单一CSS文件
```

**迁移后**:
```
static/css/
├── common.scss          # 主样式文件
├── variables.scss       # 变量定义
├── mixins.scss         # 混合器定义
└── README.md           # 使用说明
```

### 2. 引入方式变化

**App.vue中的变化**:
```scss
// 迁移前
@import url('./static/css/common.css');

// 迁移后
@import './static/css/common.scss';
```

### 3. 样式定义方式变化

#### 颜色定义
**迁移前** (CSS变量):
```css
:root {
  --primary-color: #1E90FF;
  --secondary-color: #4A90E2;
}

.text-primary {
  color: var(--primary-color);
}
```

**迁移后** (SCSS变量):
```scss
// variables.scss
$primary-color: #1E90FF;
$secondary-color: #4A90E2;

// common.scss
.text-primary {
  color: $primary-color;
}
```

#### 重复样式处理
**迁移前** (重复代码):
```css
.btn-primary {
  background-color: #1E90FF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
}

.btn-secondary {
  background-color: #4A90E2;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
}
```

**迁移后** (混合器):
```scss
// mixins.scss
@mixin button-style($bg-color: $primary-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  border-radius: $border-radius-sm;
  padding: $spacing-2 $spacing-3;
}

// 使用
.btn-primary {
  @include button-style($primary-color);
}

.btn-secondary {
  @include button-style($secondary-color);
}
```

## 迁移对照表

### 颜色使用对照

| 迁移前 | 迁移后 | 说明 |
|--------|--------|------|
| `var(--primary-color)` | `$primary-color` | 主色调 |
| `var(--secondary-color)` | `$secondary-color` | 次要色 |
| `var(--text-color)` | `$text-color` | 文字颜色 |
| `var(--border-color)` | `$border-color` | 边框颜色 |
| `var(--gray-light)` | `$gray-light` | 浅灰色 |

### 间距使用对照

| 迁移前 | 迁移后 | 数值 |
|--------|--------|------|
| `10rpx` | `$spacing-1` | 10rpx |
| `20rpx` | `$spacing-2` | 20rpx |
| `30rpx` | `$spacing-3` | 30rpx |
| `40rpx` | `$spacing-4` | 40rpx |

### 常用样式对照

| 迁移前 | 迁移后 | 说明 |
|--------|--------|------|
| 手动写flex布局 | `@include flex-center` | 居中布局 |
| 手动写文本省略 | `@include text-ellipsis(2)` | 文本省略 |
| 手动写卡片样式 | `@include card-style()` | 卡片样式 |
| 手动写按钮样式 | `@include button-style()` | 按钮样式 |

## 页面迁移示例

### 示例1: 简单页面迁移

**迁移前**:
```vue
<template>
  <view class="page">
    <view class="header">
      <text class="title">标题</text>
    </view>
    <view class="content">
      <text class="text">内容</text>
    </view>
  </view>
</template>

<style>
.page {
  background-color: #F5F5F5;
  padding: 30rpx;
}

.header {
  background-color: #FFFFFF;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1E90FF;
}

.content {
  background-color: #FFFFFF;
  padding: 30rpx;
  border-radius: 16rpx;
}

.text {
  font-size: 28rpx;
  color: #333333;
}
</style>
```

**迁移后** (使用工具类):
```vue
<template>
  <view class="page bg-gray p-3">
    <view class="header bg-white p-3 rounded-lg mb-2">
      <text class="title text-lg font-bold text-primary">标题</text>
    </view>
    <view class="content bg-white p-3 rounded-lg">
      <text class="text text-base text-dark">内容</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 不需要额外的CSS，全部使用工具类
</style>
```

**迁移后** (使用SCSS功能):
```vue
<template>
  <view class="page">
    <view class="header">
      <text class="title">标题</text>
    </view>
    <view class="content">
      <text class="text">内容</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.page {
  background-color: $gray-light;
  padding: $spacing-3;
}

.header {
  @include card-style($spacing-3, 'md');
  margin-bottom: $spacing-2;
}

.title {
  font-size: $text-lg;
  font-weight: bold;
  color: $primary-color;
}

.content {
  @include card-style($spacing-3, 'md');
}

.text {
  font-size: $text-base;
  color: $text-color;
}
</style>
```

## 迁移步骤

### 1. 立即可用
- 所有工具类立即可用，无需修改
- 现有的工具类如 `.flex-center`, `.text-lg` 等继续工作

### 2. 逐步迁移
1. **第一步**: 在新页面中使用SCSS变量和混合器
2. **第二步**: 逐步将现有页面的硬编码值替换为SCSS变量
3. **第三步**: 将重复的样式代码替换为混合器调用
4. **第四步**: 优化和清理不必要的自定义样式

### 3. 最佳实践
- 优先使用工具类
- 复杂样式使用混合器
- 所有数值使用SCSS变量
- 避免硬编码颜色和尺寸

## 注意事项

1. **兼容性**: 新的SCSS样式完全向后兼容，现有页面无需立即修改
2. **性能**: SCSS编译后的CSS与原CSS性能相同
3. **维护性**: 使用变量和混合器后，样式修改更加容易
4. **一致性**: 统一的设计系统确保界面一致性

## 常见问题

### Q: 现有页面需要立即修改吗？
A: 不需要。新的SCSS样式系统完全向后兼容，现有页面可以继续正常工作。

### Q: 如何在页面中使用SCSS变量？
A: 在页面的 `<style lang="scss" scoped>` 中引入变量文件：
```scss
@import '@/static/css/variables.scss';
```

### Q: 工具类和混合器有什么区别？
A: 工具类直接在HTML中使用，混合器在SCSS中通过 `@include` 使用。工具类适合简单样式，混合器适合复杂样式组合。

### Q: 如何扩展新的样式？
A: 在对应的SCSS文件中添加：
- 新变量 → `variables.scss`
- 新混合器 → `mixins.scss`  
- 新工具类 → `common.scss`
