@charset "UTF-8";
.index-page.data-v-******** {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  position: relative;
  padding-bottom: 140rpx;
  /* 为自定义tabBar留出空间 */
}
/* 顶部重庆山城风格背景 */
.index-page.data-v-********::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 280rpx;
  background: linear-gradient(180deg, #1E90FF 0%, #4A90E2 20%, #6BA3E8 40%, rgba(107, 163, 232, 0.7) 65%, rgba(138, 180, 240, 0.4) 80%, rgba(169, 197, 248, 0.2) 90%, rgba(200, 214, 255, 0.1) 95%, transparent 100%);
  z-index: 1;
}
/* 重庆山城剪影装饰 */
.index-page.data-v-********::after {
  content: '';
  position: absolute;
  top: 200rpx;
  left: 0;
  right: 0;
  height: 120rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M0,100 L50,95 L100,85 L150,90 L200,80 L250,85 L300,75 L350,80 L400,70 L450,75 L500,65 L550,70 L600,60 L650,65 L700,55 L750,60 L800,50 L850,55 L900,45 L950,50 L1000,40 L1050,45 L1100,35 L1150,40 L1200,30 L1200,120 L0,120 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 120rpx;
  z-index: 2;
  opacity: 0.6;
}
/* 搜索头部 */
.search-header.data-v-******** {
  padding: 20rpx 20rpx 20rpx;
  background: transparent;
  position: relative;
  z-index: 2;
}
.search-box.data-v-******** {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 30rpx 30rpx;
  -webkit-backdrop-filter: blur(15rpx);
          backdrop-filter: blur(15rpx);
  box-shadow: 0 4rpx 20rpx rgba(255, 255, 255, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}
.search-placeholder.data-v-******** {
  margin-left: 20rpx;
  color: #999;
  font-size: 28rpx;
}
/* 轮播图 */
.banner-section.data-v-******** {
  margin: 15rpx 20rpx 30rpx 20rpx;
  /* 稍微调整上边距 */
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1), 0 6rpx 20rpx rgba(30, 144, 255, 0.15), 0 2rpx 8rpx rgba(248, 113, 162, 0.2);
  /* 多层柔和阴影 */
  background: #ffffff;
  position: relative;
  z-index: 3;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  /* 添加微妙的边框 */
}
.banner-swiper.data-v-******** {
  height: 300rpx;
  position: relative;
}
.banner-image.data-v-******** {
  width: 100%;
  height: 100%;
}
.banner-overlay.data-v-******** {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 30rpx 20rpx;
}
.banner-title.data-v-******** {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}
/* 功能菜单 */
.menu-section.data-v-******** {
  margin: 0 20rpx 20rpx;
}
.menu-grid.data-v-******** {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40rpx;
  padding: 50rpx 30rpx;
}
.menu-item.data-v-******** {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.menu-icon.data-v-******** {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #E5F0FF, #F0F8FF);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(30, 144, 255, 0.25);
  position: relative;
  overflow: hidden;
}
/* 菜单图标山城装饰 */
.menu-icon.data-v-********::before {
  content: '';
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: radial-gradient(circle, rgba(30, 144, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}
.menu-text.data-v-******** {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.icon-font.data-v-******** {
  line-height: 1;
}
/* 数据统计 - 重庆山城风格 */
.stats-section.data-v-******** {
  margin: 20rpx 30rpx 30rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.3);
  position: relative;
}
/* 山城装饰纹理 */
.stats-section.data-v-********::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 30l15-15v30zM15 0l15 15H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  border-radius: 16rpx;
  pointer-events: none;
}
.stats-header.data-v-******** {
  background: rgba(255, 255, 255, 0.1);
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}
.stats-title.data-v-******** {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}
.stats-time.data-v-******** {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}
.stats-cards.data-v-******** {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 20rpx;
  position: relative;
  z-index: 1;
}
.stats-card.data-v-******** {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx 10rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.stats-card.data-v-********:active {
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15);
}
.stats-icon.data-v-******** {
  font-size: 40rpx;
  margin-bottom: 15rpx;
  display: block;
}
.stats-info.data-v-******** {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 10rpx;
}
.stats-number.data-v-******** {
  font-size: 32rpx;
  font-weight: bold;
  color: #705e67;
  line-height: 1;
}
.stats-unit.data-v-******** {
  font-size: 24rpx;
  color: #705e67;
  margin-left: 4rpx;
}
.stats-label.data-v-******** {
  font-size: 16rpx;
  color: #666;
  line-height: 1.2;
}
/* 通用区块样式 - 重庆风格 */
.section.data-v-******** {
  background: white;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  position: relative;
}
/* 为区块添加重庆山城微妙装饰 */
.section.data-v-********::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(45deg, rgba(220, 20, 60, 0.05) 0%, rgba(220, 20, 60, 0.02) 50%, transparent 100%);
  border-radius: 0 24rpx 0 80rpx;
  pointer-events: none;
}
/* 银行机构 */
.bank-section.data-v-******** {
  background: white;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}
.section-header.data-v-******** {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx 30rpx 20rpx;
  position: relative;
  z-index: 1;
}
.section-title-wrap.data-v-******** {
  display: flex;
  flex-direction: column;
}
.section-icon.data-v-******** {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.section-title.data-v-******** {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}
.section-subtitle.data-v-******** {
  font-size: 24rpx;
  color: #999;
}
.more-actions.data-v-******** {
  display: flex;
  gap: 15rpx;
  align-items: center;
}
.action-btn.data-v-******** {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}
.refresh-btn.data-v-******** {
  background: rgba(30, 144, 255, 0.1);
  color: #1E90FF;
}
.more-btn.data-v-******** {
  background: #1E90FF;
  color: white;
}
.action-btn.data-v-********:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.bank-grid.data-v-******** {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 30rpx 30rpx;
}
.bank-section .bank-card.data-v-******** {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4rpx solid transparent;
  color: #333;
}
.bank-section .bank-card.data-v-********:active {
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 6rpx 30rpx rgba(30, 144, 255, 0.15);
  border-left-color: #1E90FF;
}
.bank-section .bank-card-header.data-v-******** {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.bank-logo.data-v-******** {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  overflow: hidden;
  background: #f5f5f5;
}
.bank-logo-image.data-v-******** {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.bank-logo-placeholder.data-v-******** {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
}
.bank-basic-info.data-v-******** {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.bank-section .bank-name.data-v-******** {
  font-size: 28rpx;
  font-weight: 600;
  color: #333 !important;
  margin-bottom: 4rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.bank-contact-row.data-v-******** {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}
.bank-section .bank-contact.data-v-******** {
  font-size: 24rpx;
  color: #666 !important;
  flex-shrink: 0;
}
.bank-section .bank-phone.data-v-******** {
  font-size: 22rpx;
  color: #1E90FF !important;
  text-decoration: underline;
  transition: all 0.3s ease;
}
.bank-section .bank-phone.data-v-********:active {
  opacity: 0.7;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.bank-actions.data-v-******** {
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
}
.bank-location-btn.data-v-******** {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
}
.bank-location-btn.data-v-********:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
}
.location-icon.data-v-******** {
  font-size: 24rpx;
  color: white;
}
.bank-empty.data-v-******** {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  text-align: center;
}
.empty-icon.data-v-******** {
  font-size: 48rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}
.empty-text.data-v-******** {
  font-size: 28rpx;
  color: #999;
}
/* 新闻列表 */
.news-section.data-v-******** {
  margin: 0 30rpx 30rpx;
}
.news-list.data-v-******** {
  padding: 0 30rpx 30rpx;
}
.news-item.data-v-******** {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}
.news-item.data-v-********:last-child {
  border-bottom: none;
}
.news-item.data-v-********:active {
  background: rgba(30, 144, 255, 0.05);
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}
.news-cover.data-v-******** {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  background: #f5f5f5;
}
.news-cover-image.data-v-******** {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.news-cover-placeholder.data-v-******** {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f0f0, #e8e8e8);
  font-size: 32rpx;
  color: #999;
}
/* 不同类型新闻的占位符样式 */
.news-cover-policy.data-v-******** {
  background: linear-gradient(135deg, #ff9a9e, #fecfef) !important;
  color: #d63384 !important;
}
.news-cover-meeting.data-v-******** {
  background: linear-gradient(135deg, #a8edea, #fed6e3) !important;
  color: #0d6efd !important;
}
.news-cover-data.data-v-******** {
  background: linear-gradient(135deg, #ffeaa7, #fab1a0) !important;
  color: #fd7e14 !important;
}
.news-cover-notice.data-v-******** {
  background: linear-gradient(135deg, #74b9ff, #0984e3) !important;
  color: #ffffff !important;
}
.news-cover-news.data-v-******** {
  background: linear-gradient(135deg, #fd79a8, #fdcb6e) !important;
  color: #e84393 !important;
}
.news-cover-industry.data-v-******** {
  background: linear-gradient(135deg, #6c5ce7, #a29bfe) !important;
  color: #ffffff !important;
}
.news-cover-law.data-v-******** {
  background: linear-gradient(135deg, #00b894, #00cec9) !important;
  color: #ffffff !important;
}
.news-cover-guide.data-v-******** {
  background: linear-gradient(135deg, #e17055, #fab1a0) !important;
  color: #ffffff !important;
}
.news-cover-market.data-v-******** {
  background: linear-gradient(135deg, #00b894, #55efc4) !important;
  color: #ffffff !important;
}
.news-cover-default.data-v-******** {
  background: linear-gradient(135deg, #ddd6fe, #e879f9) !important;
  color: #8b5cf6 !important;
}
.news-cover-test.data-v-******** {
  background: linear-gradient(135deg, #ff7675, #fd79a8) !important;
  color: #ffffff !important;
}
.news-cover-finance.data-v-******** {
  background: linear-gradient(135deg, #fdcb6e, #e17055) !important;
  color: #ffffff !important;
}
.news-content.data-v-******** {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.news-title.data-v-******** {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.news-meta.data-v-******** {
  display: flex;
  gap: 16rpx;
  align-items: center;
  flex-wrap: wrap;
}
.news-category.data-v-******** {
  font-size: 20rpx;
  color: #1E90FF;
  background: rgba(30, 144, 255, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}
.news-date.data-v-******** {
  font-size: 20rpx;
  color: #999;
}
.news-views.data-v-******** {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 2rpx;
}
.news-arrow.data-v-******** {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
  align-self: center;
  flex-shrink: 0;
}
/* 热门提问 */
.faq-section.data-v-******** {
  margin: 0 30rpx 30rpx;
}
.faq-list.data-v-******** {
  padding: 0 30rpx 30rpx;
}
.faq-item.data-v-******** {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}
.faq-item.data-v-********:last-child {
  border-bottom: none;
}
.faq-item.data-v-********:active {
  background: rgba(30, 144, 255, 0.05);
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}
.faq-icon.data-v-******** {
  font-size: 24rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 193, 7, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.faq-content.data-v-******** {
  flex: 1;
  min-width: 0;
}
.faq-question.data-v-******** {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.faq-stats.data-v-******** {
  display: flex;
  gap: 20rpx;
}
.faq-stat.data-v-******** {
  font-size: 22rpx;
  color: #999;
}
.faq-arrow.data-v-******** {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
}
/* 政策解读 */
.interpretation-section.data-v-******** {
  margin: 0 20rpx 30rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}
.interpretation-grid.data-v-******** {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
  padding: 0 30rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}
.interpretation-item.data-v-******** {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 160rpx;
  transition: all 0.3s ease;
}
.interpretation-item.data-v-********:active {
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.interpretation-cover.data-v-******** {
  position: relative;
  width: 100%;
  height: 100rpx;
  background: #F0F0F0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
  flex-shrink: 0;
}
.interpretation-image.data-v-******** {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* 当没有真实图片时使用的渐变背景 */
.interpretation-bg-1.data-v-******** {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.interpretation-bg-2.data-v-******** {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.interpretation-bg-3.data-v-******** {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.interpretation-bg-4.data-v-******** {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.interpretation-bg-5.data-v-******** {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}
.interpretation-bg-6.data-v-******** {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
.play-icon.data-v-******** {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  pointer-events: none;
}
.text-icon.data-v-******** {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40rpx;
  pointer-events: none;
}
.interpretation-item-title.data-v-******** {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.3;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  height: 52rpx;
  flex-shrink: 0;
}

