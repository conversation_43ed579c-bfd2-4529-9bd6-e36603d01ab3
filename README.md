# 重庆跨境融资服务小程序

## 项目概述

这是一个基于 Vue2 + Vant + uniapp 开发的重庆跨境融资服务小程序，采用红白色调的重庆风格界面设计，为企业提供专业的跨境融资政策咨询服务。

## 功能特性

### 🏠 首页功能
- 全局搜索（政策文件、问题、新闻、政策解读）
- 重庆景点轮播图（三峡、洪崖洞、解放碑）
- 四大功能菜单（政策文件、常见提问、业务咨询、政策解读）
- 数据统计栏（政策文件数、访问量、业务咨询数）
- 银行机构列表（可换一换、查看更多）
- 最新新闻前三条
- 热门提问显示（按浏览量排序）
- 政策解读展示（2行3列布局）

### 📋 政策文件
- 分类筛选功能
- 政策文件列表展示
- 搜索功能
- 分页加载

### 💬 咨询服务
- 业务咨询表单
- 政策需求表单（需选择银行）
- 咨询历史记录
- 银行机构选择

### 👤 个人中心
- 用户信息展示
- 收藏记录
- 咨询记录
- 浏览历史
- 重庆特色服务
- 意见反馈

## 技术栈

- **前端框架**: Vue2 + uniapp
- **UI组件库**: Vant
- **状态管理**: Vuex
- **样式**: SCSS
- **主题色**: 红白配色方案（#DC143C 主色，#FF6B6B 辅色）

## 项目结构

```
kjrz_front/
├── pages/                    # 页面目录
│   ├── index/               # 首页
│   ├── policy/              # 政策文件页面
│   ├── consultation/        # 咨询服务页面
│   └── profile/             # 个人中心页面
├── static/                  # 静态资源
│   ├── images/              # 图片资源
│   │   └── banner/          # 轮播图（重庆景点）
│   └── tabbar/              # 底部导航图标
├── store/                   # Vuex状态管理
├── utils/                   # 工具类
│   └── api.js              # API接口封装
├── App.vue                 # 应用主组件
├── main.js                 # 入口文件
├── pages.json              # 页面配置
├── manifest.json           # 应用配置
└── package.json            # 依赖配置
```

## 安装与运行

### 环境要求
- Node.js >= 14.0.0
- npm 或 yarn
- HBuilderX 或 微信开发者工具

### 安装依赖
```bash
npm install
```

### 开发运行
```bash
# 微信小程序
npm run dev:mp-weixin

# H5
npm run dev:h5
```

### 构建打包
```bash
# 微信小程序
npm run build:mp-weixin

# H5
npm run build:h5
```

## 重庆风格设计特色

### 🎨 视觉设计
- **主色调**: 深红色 (#DC143C) - 象征重庆火锅的热情
- **辅色调**: 珊瑚红 (#FF6B6B) - 呼应重庆夜景的温暖
- **背景色**: 温暖白色 (#FFFFFF) - 营造干净整洁的感觉
- **装饰元素**: 重庆特色渐变装饰，山城轮廓暗纹

### 🏞️ 重庆元素
- **轮播图**: 重庆著名景点（长江三峡、洪崖洞夜景、解放碑）
- **服务热线**: 023区号体现重庆本地特色
- **文案表达**: 融入重庆方言和文化特色

## API接口需求

基于现有的数据库结构，需要后端实现以下接口：

### 🔍 搜索接口
```
GET /api/search?keyword=xxx&type=all
```

### 📊 统计数据接口
```
GET /api/statistics
返回：政策文件数、访问量、业务咨询数
```

### 🏦 银行机构接口
```
GET /api/banks?per_page=5&random=true
```

### 📰 新闻接口
```
GET /api/news?per_page=3&sort=publish_date
```

### ❓ 热门问题接口
```
GET /api/faq?per_page=5&sort=view_count
```

### 🎥 政策解读接口
```
GET /api/interpretations?per_page=6
```

### 📋 政策文件接口
```
GET /api/policies?page=1&per_page=20&category1=xxx
```

### 💌 咨询提交接口
```
POST /api/inquiries
```

### 📈 用户互动记录接口
```
POST /api/interactions
```

## 部署说明

### 小程序部署
1. 在微信公众平台注册小程序
2. 获取小程序 AppID
3. 修改 `manifest.json` 中的 `mp-weixin.appid`
4. 使用微信开发者工具上传代码

### H5部署
1. 构建 H5 版本
2. 将 `dist/build/h5` 目录部署到服务器
3. 配置 nginx 反向代理

## 开发规范

### 命名规范
- 文件名：kebab-case
- 组件名：PascalCase
- 变量名：camelCase
- 常量名：SCREAMING_SNAKE_CASE

### 代码规范
- 使用 ESLint + Prettier
- 组件化开发
- 响应式设计
- 无障碍访问支持

## 浏览器支持

- 微信小程序
- 微信内置浏览器
- Chrome/Safari/Firefox 现代浏览器

## 许可证

© 2024 重庆市商务委员会 - 保留所有权利

---

**联系方式**
- 技术支持：023-12345678
- 邮箱：<EMAIL> 