@charset "UTF-8";
.bank-list.data-v-6fa623e8 {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  position: relative;
}
/* 顶部重庆山城风格背景 */
.bank-list.data-v-6fa623e8::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, #1E90FF 0%, #4A90E2 30%, #6495ED 60%, rgba(100, 149, 237, 0.6) 80%, rgba(156, 196, 255, 0.3) 90%, transparent 100%);
  z-index: 1;
}
/* 重庆山城剪影装饰 */
.bank-list.data-v-6fa623e8::after {
  content: '';
  position: absolute;
  top: 140rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}
.search-header.data-v-6fa623e8 {
  background: transparent;
  padding: 30rpx;
  position: relative;
  z-index: 3;
}
.search-header.data-v-6fa623e8::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(30, 144, 255, 0.9) 0%, rgba(74, 144, 226, 0.9) 100%);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.search-box.data-v-6fa623e8 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  position: relative;
  z-index: 1;
}
.search-input.data-v-6fa623e8 {
  flex: 1;
  height: 60rpx;
  padding: 0 30rpx;
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.search-input.data-v-6fa623e8::-webkit-input-placeholder {
  color: #999;
}
.search-input.data-v-6fa623e8::placeholder {
  color: #999;
}
.search-btn.data-v-6fa623e8 {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.search-btn.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
.search-icon.data-v-6fa623e8 {
  width: 28rpx;
  height: 28rpx;
  -webkit-filter: invert(1);
          filter: invert(1);
}
.function-section.data-v-6fa623e8 {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}
.function-btn.data-v-6fa623e8 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #f8f8f8 0%, #fff 100%);
  border: 1rpx solid #e0e0e0;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.function-btn.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 100%);
}
.function-btn .btn-icon.data-v-6fa623e8 {
  font-size: 28rpx;
}
.banks-section.data-v-6fa623e8 {
  padding: 30rpx;
}
.result-summary.data-v-6fa623e8 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx 30rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.summary-icon.data-v-6fa623e8 {
  font-size: 40rpx;
  margin-right: 15rpx;
}
.summary-text.data-v-6fa623e8 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}
.summary-count.data-v-6fa623e8 {
  font-size: 26rpx;
  color: #1E90FF;
  font-weight: normal;
}
.bank-list-container.data-v-6fa623e8 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.bank-item.data-v-6fa623e8 {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1rpx solid #f0f0f0;
}
.bank-item.data-v-6fa623e8:active {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}
.bank-main.data-v-6fa623e8 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.bank-icon-wrapper.data-v-6fa623e8 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.bank-icon.data-v-6fa623e8 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.bank-icon-placeholder.data-v-6fa623e8 {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
}
.bank-info.data-v-6fa623e8 {
  flex: 1;
}
.bank-name.data-v-6fa623e8 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}
.bank-contact.data-v-6fa623e8 {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}
.contact-label.data-v-6fa623e8 {
  margin-right: 5rpx;
}
.contact-name.data-v-6fa623e8 {
  color: #1E90FF;
  font-weight: 500;
}
.bank-contact-info.data-v-6fa623e8 {
  margin-bottom: 24rpx;
}
.contact-item.data-v-6fa623e8 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}
.contact-item.data-v-6fa623e8:last-child {
  margin-bottom: 0;
}
.contact-icon.data-v-6fa623e8 {
  font-size: 28rpx;
  margin-right: 12rpx;
  width: 40rpx;
  text-align: center;
}
.contact-text.data-v-6fa623e8 {
  flex: 1;
  color: #333;
  margin-right: 20rpx;
  line-height: 1.4;
}
.call-btn.data-v-6fa623e8 {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
}
.call-btn.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
}
.bank-actions.data-v-6fa623e8 {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}
.action-btn.data-v-6fa623e8 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 28rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 25rpx;
  background: white;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.action-btn.location-btn.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  border-color: #ccc;
  background: #f8f8f8;
}
.action-btn.consult-btn.data-v-6fa623e8 {
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border-color: #1E90FF;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
}
.action-btn.consult-btn.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
}
.action-btn .action-icon.data-v-6fa623e8 {
  font-size: 28rpx;
}
.load-more-section.data-v-6fa623e8 {
  margin-top: 40rpx;
  text-align: center;
}
.load-more.data-v-6fa623e8 {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #f8f8f8 0%, #fff 100%);
  color: #666;
  border: 2rpx solid #e0e0e0;
  border-radius: 30rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.load-more.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 100%);
}
.load-more-text.data-v-6fa623e8 {
  margin-right: 10rpx;
}
.load-more-icon.data-v-6fa623e8 {
  font-size: 28rpx;
  -webkit-animation: bounce-data-v-6fa623e8 2s infinite;
          animation: bounce-data-v-6fa623e8 2s infinite;
}
.loading-more.data-v-6fa623e8 {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  color: #999;
  border-radius: 30rpx;
  font-size: 28rpx;
}
.loading-more-text.data-v-6fa623e8 {
  margin-right: 10rpx;
}
.loading-more-icon.data-v-6fa623e8 {
  font-size: 28rpx;
  -webkit-animation: spin-data-v-6fa623e8 1s linear infinite;
          animation: spin-data-v-6fa623e8 1s linear infinite;
}
.empty-state.data-v-6fa623e8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  text-align: center;
}
.empty-icon.data-v-6fa623e8 {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}
.empty-text.data-v-6fa623e8 {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: 500;
}
.empty-tip.data-v-6fa623e8 {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.4;
}
.refresh-btn.data-v-6fa623e8 {
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
}
.refresh-btn.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
}
.map-modal.data-v-6fa623e8 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.map-container.data-v-6fa623e8 {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 800rpx;
  height: 80%;
  max-height: 800rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}
.map-header.data-v-6fa623e8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
}
.map-title.data-v-6fa623e8 {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}
.close-btn.data-v-6fa623e8 {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: white;
  transition: all 0.3s ease;
}
.close-btn.data-v-6fa623e8:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}
.location-list.data-v-6fa623e8 {
  flex: 1;
  overflow-y: auto;
}
.location-item.data-v-6fa623e8 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.location-item.data-v-6fa623e8:last-child {
  border-bottom: none;
}
.location-info.data-v-6fa623e8 {
  flex: 1;
}
.bank-name-loc.data-v-6fa623e8 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}
.bank-address.data-v-6fa623e8 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.location-meta.data-v-6fa623e8 {
  font-size: 26rpx;
  color: #999;
  margin-top: 8rpx;
}
.location-actions.data-v-6fa623e8 {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}
.action-call.data-v-6fa623e8 {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
}
.action-call.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
}
.action-nav.data-v-6fa623e8 {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
}
.action-nav.data-v-6fa623e8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
}
.map-footer.data-v-6fa623e8 {
  padding: 20rpx 30rpx;
  text-align: center;
  background: #f8f8f8;
  border-top: 1rpx solid #f0f0f0;
}
.map-tip.data-v-6fa623e8 {
  font-size: 26rpx;
  color: #666;
}
@-webkit-keyframes bounce-data-v-6fa623e8 {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-8rpx);
            transform: translateY(-8rpx);
}
60% {
    -webkit-transform: translateY(-4rpx);
            transform: translateY(-4rpx);
}
}
@keyframes bounce-data-v-6fa623e8 {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-8rpx);
            transform: translateY(-8rpx);
}
60% {
    -webkit-transform: translateY(-4rpx);
            transform: translateY(-4rpx);
}
}
@-webkit-keyframes spin-data-v-6fa623e8 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-6fa623e8 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}

