<template>
  <view class="news-detail-page">
    <view v-if="newsDetail" class="content">
      <view class="article-card">
        <!-- 文章头部 -->
        <view class="article-header">
          <view class="article-title">{{ newsDetail.title }}</view>
          <view class="article-meta">
            <text class="category">{{ newsDetail.category }}</text>
            <text class="publish-date">{{ formatDate(newsDetail.publish_date) }}</text>
          </view>
          <view class="article-stats">
            <text class="stat-item">浏览 {{ newsDetail.view_count || 0 }}</text>
            <text class="stat-item">点赞 {{ newsDetail.like_count || 0 }}</text>
            <text class="stat-item">收藏 {{ newsDetail.collect_count || 0 }}</text>
          </view>
        </view>

        <!-- 封面图片 -->
        <image 
          v-if="newsDetail.cover_img" 
          :src="newsDetail.cover_img" 
          class="cover-image"
          mode="aspectFill"
        />

        <!-- 文章内容 -->
        <view class="article-body" @tap="handleContentTap">
          <view v-html="processedContent"></view>
        </view>
      </view>
    </view>

    <!-- 悬浮操作栏 -->
    <view v-if="newsDetail" class="action-bar-sticky">
      <button @click="toggleLike" class="action-btn" :class="{ active: isLiked }">
        <text class="icon">👍</text>
        <text class="action-text">{{ newsDetail.like_count > 0 ? newsDetail.like_count : '点赞' }}</text>
      </button>
      <button @click="toggleCollect" class="action-btn" :class="{ active: isCollected }">
        <text class="icon">{{ isCollected ? '⭐' : '☆' }}</text>
        <text class="action-text">{{ newsDetail.collect_count > 0 ? newsDetail.collect_count : '收藏' }}</text>
      </button>
      <button open-type="share" class="action-btn">
        <text class="icon">📤</text>
        <text class="action-text">{{ newsDetail.forward_count > 0 ? newsDetail.forward_count : '分享' }}</text>
      </button>
    </view>

    <view v-else class="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      newsDetail: null,
      newsId: null,
      isLiked: false,
      isCollected: false
    }
  },

  computed: {
    processedContent() {
      if (!this.newsDetail || !this.newsDetail.content) {
        return ''
      }

      // 处理相对路径的链接，转换为绝对路径
      let content = this.newsDetail.content

      // 将相对路径的文件链接转换为完整的下载链接
      content = content.replace(
        /href="\/files\//g,
        'href="http://kjrzymt.com/files/'
      )

      // 为所有链接添加样式和数据属性
      content = content.replace(
        /<a\s+([^>]*?)href="([^"]*?)"([^>]*?)>/g,
        '<a $1href="$2"$3 data-link="$2" style="color: #1E90FF; text-decoration: underline; cursor: pointer;">'
      )

      return content
    }
  },

  onLoad(options) {
    this.newsId = options.id
    if (this.newsId) {
      this.loadNewsDetail()
    }
  },

  methods: {
    async loadNewsDetail() {
      try {
        const res = await api.getNewsDetail(this.newsId)
        this.newsDetail = res.data
        
        // 详情加载成功后
        this.recordView()
        this.checkStatus()
      } catch (error) {
        console.error('加载新闻详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    async checkStatus() {
      const token = uni.getStorageSync('token');
      if (!token) return;

      try {
        const params = {
          item_type: 'news',
          item_id: parseInt(this.newsId)
        };
        const [likeRes, collectRes] = await Promise.all([
          api.checkInteractionStatus({ ...params, actions: ['like'] }),
          api.checkCollectionStatus(params)
        ]);

        if (likeRes.data.like) {
          this.isLiked = true;
        }
        if (collectRes.data.is_collected) {
          this.isCollected = true;
        }
      } catch (error) {
        console.error('检查状态失败:', error);
      }
    },

    async recordView() {
      const token = uni.getStorageSync('token');
      if (!token) {
        return; // 未登录，不记录
      }
      try {
        await api.recordView({
          item_type: 'news',
          item_id: parseInt(this.newsId)
        });
        if (this.newsDetail) {
          this.newsDetail.view_count++;
        }
      } catch (error) {
        console.error('记录浏览失败:', error);
      }
    },

    async toggleLike() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('点赞');
        return;
      }
      try {
        const res = await api.toggleInteraction({
          action: 'like',
          item_type: 'news',
          item_id: parseInt(this.newsId)
        });
        
        this.isLiked = res.data.action === 'added';
        this.newsDetail.like_count = res.data.current_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('点赞操作失败:', error);
      }
    },

    async toggleCollect() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('收藏');
        return;
      }
      try {
        const res = await api.toggleCollection({
          item_type: 'news',
          item_id: parseInt(this.newsId)
        });
        
        this.isCollected = res.data.action === 'added';
        this.newsDetail.collect_count = res.data.current_collect_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('收藏操作失败:', error);
      }
    },

    onShareAppMessage() {
      return {
        title: this.newsDetail.title,
        path: `/pages/news/detail?id=${this.newsId}`,
        imageUrl: this.newsDetail.cover_img || ''
      };
    },

    promptLogin(action) {
      uni.showModal({
        title: '请先登录',
        content: `登录后才能${action}哦`,
        success: (res) => {
          if (res.confirm) {
            uni.switchTab({
              url: '/pages/profile/index'
            });
          }
        }
      });
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = ('0' + (date.getMonth() + 1)).slice(-2)
      const day = ('0' + date.getDate()).slice(-2)
      return `${year}/${month}/${day}`
    },

    // 处理内容区域的点击事件
    handleContentTap(event) {
      // 在 H5 环境下，尝试获取点击的元素
      if (typeof window !== 'undefined' && event.target) {
        const target = event.target

        // 检查是否点击了链接
        if (target.tagName === 'A' || target.closest('a')) {
          const link = target.tagName === 'A' ? target : target.closest('a')
          const url = link.getAttribute('href') || link.getAttribute('data-link')

          if (url) {
            event.preventDefault()
            event.stopPropagation()
            this.handleLinkClick(url)
          }
        }
      }
    },

    // 处理富文本中的链接点击
    handleLinkClick(url) {
      console.log('点击链接:', url)

      if (url.startsWith('http://') || url.startsWith('https://')) {
        // 外部链接，复制到剪贴板
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制到剪贴板，请在浏览器中打开',
              showCancel: false
            })
          }
        })
      } else if (url.startsWith('/pages/')) {
        // 内部页面跳转
        uni.navigateTo({
          url: url,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      } else {
        // 其他链接，也复制到剪贴板
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制到剪贴板，请在浏览器中打开',
              showCancel: false
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.news-detail-page {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding-bottom: 180rpx; // 为悬浮按钮留出空间
}

.content {
  padding: 30rpx;
}

.article-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.article-header {
  margin-bottom: 30rpx;
}

.article-title {
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1.4;
  color: #333;
  margin-bottom: 20rpx;
}

.article-meta {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.category {
  background: #3b82f6;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.publish-date {
  color: #999;
  font-size: 26rpx;
}

.article-stats {
  display: flex;
  gap: 30rpx;
  font-size: 26rpx;
  color: #888;
}

.cover-image {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}

.article-body {
  font-size: 32rpx;
  line-height: 1.8;
  color: #333;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
}

.action-bar-sticky {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  transition: color 0.2s ease-in-out, transform 0.1s ease;

  &::after {
    border: none;
  }
  
  &:active {
    transform: scale(0.9);
  }
  
  &.active {
    color: #3b82f6;
  }
  
  .icon {
    font-size: 44rpx;
    margin-bottom: 6rpx;
    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    opacity: 0.7;
    filter: grayscale(80%);
  }
  
  &.active .icon {
    transform: scale(1.1);
    opacity: 1;
    filter: grayscale(0%);
  }
  
  .action-text {
    font-size: 24rpx;
  }
}
</style> 