<template>
  <view class="test-page bg-gray">
    <!-- 页面标题 -->
    <view class="header bg-primary text-white p-3 text-center">
      <text class="text-xl font-bold">公共样式测试页面</text>
    </view>

    <!-- Flex布局测试 -->
    <view class="section bg-white m-3 p-3 rounded-lg">
      <text class="text-lg font-bold mb-2 text-primary">Flex布局测试</text>
      
      <!-- 水平居中 -->
      <view class="flex-center bg-gray p-2 mb-2 rounded">
        <text class="text-base">水平垂直居中</text>
      </view>
      
      <!-- 两端对齐 -->
      <view class="flex-between bg-gray p-2 mb-2 rounded">
        <text class="text-base">左侧文字</text>
        <text class="text-base">右侧文字</text>
      </view>
      
      <!-- 垂直布局 -->
      <view class="flex-col bg-gray p-2 rounded">
        <text class="text-base mb-1">第一行</text>
        <text class="text-base mb-1">第二行</text>
        <text class="text-base">第三行</text>
      </view>
    </view>

    <!-- 文本样式测试 -->
    <view class="section bg-white m-3 p-3 rounded-lg">
      <text class="text-lg font-bold mb-2 text-primary">文本样式测试</text>
      
      <text class="text-xs text-gray mb-1">超小文字 (20rpx)</text>
      <text class="text-sm text-gray mb-1">小文字 (24rpx)</text>
      <text class="text-base text-dark mb-1">基础文字 (28rpx)</text>
      <text class="text-lg text-dark mb-1">大文字 (32rpx)</text>
      <text class="text-xl text-primary mb-1">超大文字 (36rpx)</text>
      <text class="text-2xl font-bold text-primary">最大文字 (40rpx)</text>
    </view>

    <!-- 颜色测试 -->
    <view class="section bg-white m-3 p-3 rounded-lg">
      <text class="text-lg font-bold mb-2 text-primary">颜色测试</text>
      
      <view class="flex-row mb-2">
        <view class="bg-primary p-2 rounded mr-2">
          <text class="text-white text-sm">主色调</text>
        </view>
        <view class="bg-secondary p-2 rounded mr-2">
          <text class="text-white text-sm">次要色</text>
        </view>
        <view class="bg-gray p-2 rounded">
          <text class="text-dark text-sm">灰色</text>
        </view>
      </view>
      
      <text class="text-primary mb-1">主色调文字</text>
      <text class="text-secondary mb-1">次要色文字</text>
      <text class="text-gray mb-1">灰色文字</text>
      <text class="text-dark">深色文字</text>
    </view>

    <!-- 间距测试 -->
    <view class="section bg-white m-3 p-3 rounded-lg">
      <text class="text-lg font-bold mb-2 text-primary">间距测试</text>
      
      <view class="bg-gray rounded mb-2">
        <view class="bg-primary p-1 rounded">
          <text class="text-white text-sm">p-1 (10rpx)</text>
        </view>
      </view>
      
      <view class="bg-gray rounded mb-2">
        <view class="bg-primary p-2 rounded">
          <text class="text-white text-sm">p-2 (20rpx)</text>
        </view>
      </view>
      
      <view class="bg-gray rounded">
        <view class="bg-primary p-3 rounded">
          <text class="text-white text-sm">p-3 (30rpx)</text>
        </view>
      </view>
    </view>

    <!-- 文本省略测试 -->
    <view class="section bg-white m-3 p-3 rounded-lg">
      <text class="text-lg font-bold mb-2 text-primary">文本省略测试</text>
      
      <view class="mb-2">
        <text class="text-sm text-gray mb-1">单行省略：</text>
        <text class="ellipsis text-base text-dark">这是一段很长很长很长很长很长很长很长很长很长很长的文字，应该会被省略显示</text>
      </view>
      
      <view class="mb-2">
        <text class="text-sm text-gray mb-1">两行省略：</text>
        <text class="ellipsis-2 text-base text-dark">这是一段很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的文字，应该会在两行后被省略显示</text>
      </view>
      
      <view>
        <text class="text-sm text-gray mb-1">三行省略：</text>
        <text class="ellipsis-3 text-base text-dark">这是一段很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的文字，应该会在三行后被省略显示</text>
      </view>
    </view>

    <!-- 边框和圆角测试 -->
    <view class="section bg-white m-3 p-3 rounded-lg">
      <text class="text-lg font-bold mb-2 text-primary">边框和圆角测试</text>
      
      <view class="flex-row mb-2">
        <view class="border p-2 mr-2">
          <text class="text-sm">普通边框</text>
        </view>
        <view class="border rounded p-2 mr-2">
          <text class="text-sm">小圆角</text>
        </view>
        <view class="border rounded-lg p-2">
          <text class="text-sm">大圆角</text>
        </view>
      </view>
      
      <view class="flex-row">
        <view class="bg-primary rounded-full p-2 mr-2" style="width: 80rpx; height: 80rpx;">
          <text class="text-white text-xs text-center">圆形</text>
        </view>
        <view class="border-t border-b p-2">
          <text class="text-sm">上下边框</text>
        </view>
      </view>
    </view>

    <!-- 卡片样式示例 -->
    <view class="section bg-white m-3 p-3 rounded-lg">
      <text class="text-lg font-bold mb-2 text-primary">实际应用示例</text>
      
      <!-- 新闻卡片 -->
      <view class="bg-gray rounded-lg p-3 mb-2">
        <view class="flex-between mb-2">
          <text class="text-base font-bold text-dark ellipsis">重要政策发布通知</text>
          <text class="text-xs text-gray">2024-01-01</text>
        </view>
        <text class="text-sm text-gray ellipsis-2 mb-2">这是一条重要的政策发布通知，内容包含了详细的政策解读和实施细则...</text>
        <view class="flex-end">
          <view class="bg-primary text-white text-xs p-1 rounded">
            <text>政策文件</text>
          </view>
        </view>
      </view>
      
      <!-- 按钮组 -->
      <view class="flex-between">
        <view class="bg-primary text-white p-2 rounded">
          <text class="text-sm">主要按钮</text>
        </view>
        <view class="bg-secondary text-white p-2 rounded">
          <text class="text-sm">次要按钮</text>
        </view>
        <view class="border border-primary p-2 rounded">
          <text class="text-sm text-primary">边框按钮</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TestCommonStyles',
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
}

.section {
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
</style>
