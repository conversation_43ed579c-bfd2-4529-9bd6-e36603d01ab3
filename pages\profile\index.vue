<template>
  <view class="profile-page">
    <!-- 用户信息卡片 -->
    <view v-if="isLoggedIn" class="user-card cq-decoration">
      <view class="user-avatar-section">
        <image :src="userInfo.avatar || '/static/images/default-avatar.png'" class="user-avatar"></image>
        <view class="user-info">
          <text class="user-name">{{ userInfo.nickname || '微信用户' }}</text>
          <text class="user-desc">零距离·汇万家服务用户</text>
        </view>
      </view>
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{ userStats.collectionCount || 0 }}</text>
          <text class="stat-label">收藏</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userStats.inquiryCount || 0 }}</text>
          <text class="stat-label">咨询</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userStats.viewCount || 0 }}</text>
          <text class="stat-label">浏览</text>
        </view>
      </view>
    </view>

    <!-- 登录提示卡片 -->
    <view v-else class="login-prompt-card cq-decoration">
      <view class="prompt-info">
        <text class="prompt-title">登录体验更多服务</text>
        <text class="prompt-desc">收藏、咨询、浏览记录一目了然</text>
      </view>
      <button class="login-btn" @click="handleLogin">
        <text class="login-btn-text">微信一键登录</text>
      </button>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section section">
      <view class="menu-list">
        <view class="menu-item" @click="toCollectionPage">
          <view class="menu-icon">⭐</view>
          <text class="menu-title">我的收藏</text>
          <view class="menu-extra">
            <text class="menu-count">{{ userStats.collectionCount || 0 }}</text>
            <text class="menu-arrow">></text>
          </view>
        </view>
        
        <view class="menu-item" @click="toInquiryPage">
          <view class="menu-icon">💬</view>
          <text class="menu-title">我的咨询</text>
          <view class="menu-extra">
            <text class="menu-count">{{ userStats.inquiryCount || 0 }}</text>
            <text class="menu-arrow">></text>
          </view>
        </view>
        
        <view class="menu-item" @click="toHistoryPage">
          <view class="menu-icon">📖</view>
          <text class="menu-title">浏览历史</text>
          <view class="menu-extra">
            <text class="menu-arrow">></text>
          </view>
        </view>
        
        <!-- <view class="menu-item" @click="toFeedbackPage">
          <view class="menu-icon">📝</view>
          <text class="menu-title">意见反馈</text>
          <view class="menu-extra">
            <text class="menu-arrow">></text>
          </view>
        </view> -->
      </view>
    </view>

    <!-- 联系我们 -->
    <view class="service-section section cq-decoration">
      <view class="section-title">联系我们</view>
      <view class="service-list">
        <view class="service-item" @click="openService('hotline')">
          <view class="service-icon">📞</view>
          <view class="service-info">
            <text class="service-title">外汇局咨询热线</text>
            <text class="service-desc">023-67677161</text>
          </view>
        </view>
        
        <view class="service-item" @click="openService('location')">
          <view class="service-icon">📍</view>
          <view class="service-info">
            <text class="service-title">银行网点一览</text>
            <text class="service-desc">就近银行机构查询</text>
          </view>
        </view>
        
        <!-- <view class="service-item" @click="openService('policy')">
          <view class="service-icon">📋</view>
          <view class="service-info">
            <text class="service-title">最新政策通知</text>
            <text class="service-desc">实时政策更新提醒</text>
          </view>
        </view> -->
      </view>
    </view>

    <!-- 设置选项 -->
    <view class="settings-section section">
      <view class="menu-list">
        <view class="menu-item" @click="clearCache">
          <view class="menu-icon">🗑️</view>
          <text class="menu-title">清除缓存</text>
          <view class="menu-extra">
            <text class="menu-arrow">></text>
          </view>
        </view>
        
        <view class="menu-item" @click="aboutApp">
          <view class="menu-icon">ℹ️</view>
          <text class="menu-title">关于我们</text>
          <view class="menu-extra">
            <text class="menu-arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">零距离·汇万家服务</text>
      <text class="copyright-text">© 2025 重庆市商务委员会</text>
    </view>

    <!-- 快速反馈弹窗 -->
    <view class="modal-overlay" v-if="showFeedbackModal" @click="showFeedbackModal = false">
              <view class="feedback-modal" catchtap="true">
        <view class="modal-header">
          <text class="modal-title">意见反馈</text>
          <text class="modal-close" @click="showFeedbackModal = false">×</text>
        </view>
        <view class="modal-content">
          <textarea 
            class="feedback-textarea" 
            v-model="feedbackContent" 
            placeholder="请描述您遇到的问题或建议..."
            maxlength="500"
          ></textarea>
          <view class="char-count">{{ feedbackContent.length }}/500</view>
        </view>
        <view class="modal-footer">
          <view class="modal-btn cancel-btn" @click="showFeedbackModal = false">取消</view>
          <view class="modal-btn submit-btn" @click="submitFeedback">提交</view>
        </view>
      </view>
    </view>
    
    <!-- 自定义TabBar -->
    <custom-tab-bar />
  </view>
</template>

<script>
import { api } from '@/utils/api'
import CustomTabBar from '@/custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      isLoggedIn: false,
      userInfo: {
        nickname: '',
        avatar: ''
      },
      userStats: {
        collectionCount: 0,
        inquiryCount: 0,
        viewCount: 0
      },
      showFeedbackModal: false,
      feedbackContent: ''
    }
  },
  onLoad() {
    // this.loadUserInfo()
    // this.loadUserStats()
  },
  onShow() {
    if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {
      this.$root.$mp.page.getTabBar().$vm.updateSelected(3)
    }
    this.checkLoginStatus();
  },
  methods: {
    checkLoginStatus() {
      const token = uni.getStorageSync('token');
      if (token) {
        this.isLoggedIn = true;
        this.loadUserInfo();
        this.loadUserStats();
      } else {
        this.isLoggedIn = false;
        // 清空用户信息和统计数据
        this.userInfo = { nickname: '微信用户', avatar: '/static/images/default-avatar.png' };
        this.userStats = { collectionCount: 0, inquiryCount: 0, viewCount: 0 };
      }
    },

    handleLogin() {
      uni.getUserProfile({
        desc: '用于完善会员资料',
        success: (userProfileRes) => {
          const userProfile = {
            nickname: userProfileRes.userInfo.nickName,
            avatar: userProfileRes.userInfo.avatarUrl,
          };
          this.userInfo = userProfile; // 立即更新UI

          uni.login({
            provider: 'weixin',
            success: async (loginRes) => {
              const params = {
                code: loginRes.code,
                nickname: userProfile.nickname,
                avatar: userProfile.avatar,
              };
              try {
                uni.showLoading({ title: '登录中...' });
                const res = await api.wechatLogin(params);
                if (res.code === 200 && res.data.token) {
                  uni.setStorageSync('token', res.data.user.id);
                  
                  // 合并后端返回的用户信息（如ID）和从微信获取的最新信息
                  const finalUserInfo = { ...res.data.user, ...userProfile };

                  uni.setStorageSync('userInfo', finalUserInfo);
                  this.isLoggedIn = true;
                  this.userInfo = finalUserInfo; // 再次更新，确保数据完整
                  this.loadUserStats();
                  uni.hideLoading();
                  uni.showToast({ title: '登录成功', icon: 'success' });
                } else {
                  throw new Error('登录失败，请重试');
                }
              } catch (error) {
                uni.hideLoading();
                uni.showToast({
                  title: error.message || '登录时发生错误',
                  icon: 'none',
                });
              }
            },
            fail: (err) => {
                uni.showToast({ title: '微信登录授权失败', icon: 'none' });
            }
          });
        },
        fail: (err) => {
          uni.showToast({ title: '您取消了授权', icon: 'none' });
        },
      });
    },

    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.userInfo = userInfo;
      }
    },

    async loadUserStats() {
      if (!this.isLoggedIn) return;
      
      const user = uni.getStorageSync('userInfo');
      if (!user || !user.id) {
        console.error("无法获取用户信息或用户ID");
        return;
      }

      uni.showLoading({ title: '加载中...', mask: true });
      try {
        const [collectionRes, inquiryRes, viewRes] = await Promise.all([
          api.getMyCollections({ per_page: 1 }),
          api.getMyInquiries(user.id, { per_page: 1 }),
          api.getMyInteractions({ action: 'view', per_page: 1 })
        ]);

        this.userStats = {
          collectionCount: collectionRes.data.total || 0,
          inquiryCount: inquiryRes.data.total || 0,
          viewCount: viewRes.data.total || 0,
        };
      } catch (error) {
        console.error('加载用户统计失败:', error);
        uni.showToast({
          title: '统计数据加载失败',
          icon: 'none'
        });
        // 保留旧数据或清零
        this.userStats = {
          collectionCount: 'N/A',
          inquiryCount: 'N/A',
          viewCount: 'N/A'
        };
      } finally {
        uni.hideLoading();
      }
    },

    toCollectionPage() {
      if (!this.isLoggedIn) {
        this.promptLogin('查看收藏');
        return;
      }
      uni.navigateTo({
        url: '/pages/profile/collection'
      })
    },

    toInquiryPage() {
      if (!this.isLoggedIn) {
        this.promptLogin('查看咨询');
        return;
      }
      uni.navigateTo({
        url: '/pages/profile/inquiry'
      })
    },

    toHistoryPage() {
      if (!this.isLoggedIn) {
        this.promptLogin('查看浏览历史');
        return;
      }
      uni.navigateTo({
        url: '/pages/profile/history'
      })
    },

    promptLogin(action) {
      uni.showModal({
        title: '请先登录',
        content: `登录后才能${action}哦`,
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确认，可以在此处理登录逻辑，或者停留在当前页让用户点击登录按钮
            // 当前逻辑是让用户停留在个人中心页手动登录
          }
        }
      });
    },

    toFeedbackPage() {
      this.showFeedbackModal = true
    },

    openService(type) {
      switch (type) {
        case 'hotline':
          uni.makePhoneCall({
            phoneNumber: '023-********'
          })
          break
        case 'location':
          uni.navigateTo({
            url: '/pages/bank/list'
          })
          break
        case 'policy':
          uni.switchTab({
            url: '/pages/policy/index'
          })
          break
      }
    },

    clearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确认清除所有缓存数据吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除缓存逻辑
            uni.clearStorageSync()
            this.isLoggedIn = false;
            this.checkLoginStatus(); // 重新检查状态并更新UI
            uni.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
          }
        }
      })
    },

    aboutApp() {
      uni.showModal({
        title: '关于我们',
        content: '重庆跨境融资服务小程序\n\n为企业提供专业的跨境融资政策咨询服务，助力重庆开放型经济发展。\n\n联系我们：023-********',
        showCancel: false
      })
    },

    async submitFeedback() {
      if (!this.feedbackContent.trim()) {
        uni.showToast({
          title: '请输入反馈内容',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: '提交中...' })
        
        // 提交反馈接口
        // await api.submitFeedback({ content: this.feedbackContent })
        
        uni.hideLoading()
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        })
        
        this.showFeedbackModal = false
        this.feedbackContent = ''
        
      } catch (error) {
        uni.hideLoading()
        console.error('提交反馈失败:', error)
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */
  position: relative;
}

/* 顶部重庆山城风格背景 */
.profile-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(180deg, 
    #1E90FF 0%, 
    #4A90E2 25%, 
    #6BA3E8 50%, 
    rgba(107, 163, 232, 0.7) 70%, 
    rgba(138, 180, 240, 0.4) 85%, 
    rgba(169, 197, 248, 0.2) 95%, 
    transparent 100%
  );
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.profile-page::after {
  content: '';
  position: absolute;
  top: 180rpx;
  left: 0;
  right: 0;
  height: 100rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 100'%3E%3Cpath d='M0,80 L50,75 L100,70 L150,65 L200,60 L250,65 L300,55 L350,60 L400,50 L450,55 L500,45 L550,50 L600,40 L650,45 L700,35 L750,40 L800,30 L850,35 L900,25 L950,30 L1000,20 L1050,25 L1100,15 L1150,20 L1200,10 L1200,100 L0,100 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 100rpx;
  z-index: 2;
  opacity: 0.8;
}

.user-card {
  margin: 0 30rpx 30rpx;
  margin-top: 0;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20rpx;
  color: #333;
  position: relative;
  z-index: 3;
  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.15);
  border: 1rpx solid rgba(30, 144, 255, 0.1);
}

.user-avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(30, 144, 255, 0.2);
  margin-right: 30rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  color: #333;
}

.user-desc {
  font-size: 26rpx;
  color: #666;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(30, 144, 255, 0.05);
  border-radius: 16rpx;
  padding: 30rpx 0;
  border: 1rpx solid rgba(30, 144, 255, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #1E90FF;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  z-index: 3;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-section {
  margin: 0 30rpx 30rpx;
}

.menu-list {
  padding: 0 30rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.menu-title {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-color);
}

.menu-extra {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.menu-count {
  font-size: 24rpx;
  color: #999;
}

.menu-arrow {
  font-size: 28rpx;
  color: #999;
}

.service-section {
  margin: 0 30rpx 30rpx;
}

.service-list {
  padding: 30rpx;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.service-title {
  font-size: 30rpx;
  color: var(--text-color);
}

.service-desc {
  font-size: 24rpx;
  color: #999;
}

.settings-section {
  margin: 0 30rpx 30rpx;
}

.version-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 30rpx;
  gap: 16rpx;
}

.version-text {
  font-size: 26rpx;
  color: #999;
}

.copyright-text {
  font-size: 24rpx;
  color: #ccc;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.feedback-modal {
  width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}

.modal-close {
  font-size: 40rpx;
  color: #999;
}

.modal-content {
  padding: 30rpx;
}

.feedback-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  font-size: 28rpx;
  resize: none;
  border: none;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 30rpx;
  text-align: center;
  font-size: 30rpx;
}

.cancel-btn {
  color: #999;
  border-right: 1rpx solid #f0f0f0;
}

.submit-btn {
  color: var(--primary-color);
  font-weight: bold;
}

.login-prompt-card {
  margin: 0 30rpx 30rpx;
  padding: 60rpx 40rpx;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  border-radius: 20rpx;
  color: white;
  position: relative;
  z-index: 3;
  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.prompt-info {
  margin-bottom: 40rpx;
}

.prompt-title {
  display: block;
  font-size: 38rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: white;
}

.prompt-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

.login-btn {
  background-color: #ffffff;
  color: #1E90FF;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 30rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.login-btn-text {
  margin-left: 10rpx;
}
</style> 