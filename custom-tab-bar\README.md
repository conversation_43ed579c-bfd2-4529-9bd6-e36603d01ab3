# 自定义底部标题栏组件

## 概述

重新设计的底部标题栏组件，使用SCSS公共样式系统，支持本地图标资源，提供更好的用户体验。

## 功能特性

### ✅ 已实现功能
- **SCSS样式系统**: 使用项目统一的SCSS变量和混合器
- **本地图标资源**: 所有图标和背景图片使用本地资源
- **响应式设计**: 支持不同屏幕尺寸的适配
- **安全区域适配**: 自动适配iPhone等设备的安全区域
- **动画效果**: 平滑的切换动画和点击反馈
- **触觉反馈**: 切换时提供轻微震动反馈
- **错误处理**: 完善的错误处理和用户提示

### 🎨 设计特点
- **三层结构**: 顶部分割线 + 内容区域 + 底部背景
- **统一风格**: 与项目整体设计风格保持一致
- **图标状态**: 支持普通和激活状态的图标切换
- **颜色系统**: 使用项目统一的颜色变量

## 文件结构

```
custom-tab-bar/
├── index.vue              # 主组件文件
└── README.md             # 使用说明（本文件）

static/
├── icons/                # 图标资源
│   ├── home.svg          # 首页图标
│   ├── home-active.svg   # 首页激活图标
│   ├── policy.svg        # 政策图标
│   ├── policy-active.svg # 政策激活图标
│   ├── consult.svg       # 咨询图标
│   ├── consult-active.svg# 咨询激活图标
│   ├── profile.svg       # 个人中心图标
│   └── profile-active.svg# 个人中心激活图标
└── images/               # 背景图片
    ├── tabbar-top-line.png    # 顶部分割线
    └── tabbar-bottom-bg.png   # 底部背景
```

## 组件配置

### Tab配置
```javascript
tabList: [
  {
    icon: '/static/icons/home.svg',           // 普通状态图标
    activeIcon: '/static/icons/home-active.svg', // 激活状态图标
    text: '首页',                              // 显示文字
    pagePath: '/pages/index/index'            // 页面路径
  },
  // ... 其他tab配置
]
```

### 样式变量
```scss
// 使用的SCSS变量
$background-color     // 背景颜色
$primary-color        // 主色调（激活状态）
$text-color          // 文字颜色
$gray-medium         // 灰色（非激活状态）
$spacing-1, $spacing-2 // 间距变量
$transition-fast     // 动画时长
```

## 使用方法

### 1. 在页面中引用
```vue
<template>
  <view class="page">
    <!-- 页面内容 -->
    <view class="content">
      <!-- ... -->
    </view>
    
    <!-- 自定义tabbar -->
    <custom-tab-bar ref="tabbar" />
  </view>
</template>

<script>
import CustomTabBar from '@/custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  }
}
</script>
```

### 2. 在pages.json中配置
```json
{
  "tabBar": {
    "custom": true,
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页"
      },
      {
        "pagePath": "pages/policy/index", 
        "text": "政策"
      },
      {
        "pagePath": "pages/consultation/index",
        "text": "咨询"
      },
      {
        "pagePath": "pages/profile/index",
        "text": "个人中心"
      }
    ]
  }
}
```

### 3. 组件方法调用
```javascript
// 获取tabbar组件引用
const tabbar = this.$refs.tabbar;

// 更新选中状态
tabbar.updateSelected(1);

// 获取当前tab索引
const currentIndex = tabbar.getCurrentTabIndex();

// 获取tab配置
const tabConfig = tabbar.getTabConfig(0);
```

## 样式定制

### 1. 修改颜色
在 `static/css/variables.scss` 中修改颜色变量：
```scss
$primary-color: #1E90FF;      // 主色调
$background-color: #FFFFFF;   // 背景色
$text-color: #333333;         // 文字色
```

### 2. 修改尺寸
```scss
// 在组件样式中修改
.custom-tab-bar {
  height: 166rpx;  // 整体高度
}

.tab-icon {
  width: 48rpx;    // 图标尺寸
  height: 48rpx;
}

.tab-text {
  font-size: $text-xs;  // 文字大小
}
```

### 3. 修改动画
```scss
.tab-item {
  transition: $transition-fast;  // 动画时长
  
  &:active {
    transform: scale(0.95);      // 点击缩放
  }
}
```

## 响应式适配

组件支持响应式设计，在不同屏幕尺寸下自动调整：

```scss
@include respond-to('sm') {
  .custom-tab-bar {
    height: 180rpx;        // 小屏幕下增加高度
  }
  
  .tab-icon {
    width: 56rpx;          // 放大图标
    height: 56rpx;
  }
}
```

## 安全区域适配

自动适配iPhone等设备的安全区域：
```scss
.custom-tab-bar {
  padding-bottom: env(safe-area-inset-bottom);
}
```

## 注意事项

1. **图标格式**: 建议使用SVG格式图标，支持更好的缩放效果
2. **图片路径**: 确保所有图片路径正确，使用绝对路径
3. **页面配置**: 确保pages.json中正确配置了自定义tabbar
4. **z-index**: 组件使用z-index: 1000，确保不被其他元素遮挡
5. **性能优化**: 图标文件建议压缩优化，减少加载时间

## 故障排除

### 常见问题

1. **图标不显示**
   - 检查图片路径是否正确
   - 确认图片文件是否存在
   - 检查图片格式是否支持

2. **切换无效果**
   - 检查pages.json中的tabbar配置
   - 确认页面路径是否正确
   - 检查是否正确设置custom: true

3. **样式异常**
   - 确认已正确引入SCSS变量文件
   - 检查CSS优先级是否被覆盖
   - 验证SCSS编译是否正常

### 调试方法
```javascript
// 在组件中添加调试信息
console.log('当前tab索引:', this.currentTab);
console.log('tab配置:', this.tabList);
console.log('当前页面路径:', getCurrentPages());
```

## 更新日志

### v2.0.0 (当前版本)
- ✅ 重新设计UI结构，采用三层布局
- ✅ 集成SCSS公共样式系统
- ✅ 图片资源本地化
- ✅ 增加响应式支持
- ✅ 完善错误处理和用户反馈
- ✅ 添加触觉反馈功能

### v1.0.0 (原版本)
- ✅ 基础tabbar功能
- ✅ 简单的图标和文字显示
- ✅ 基础的切换功能
