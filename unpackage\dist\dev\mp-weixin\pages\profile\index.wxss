@charset "UTF-8";
.profile-page.data-v-14bc1b43 {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  padding-bottom: 140rpx;
  /* 为自定义tabBar留出空间 */
  position: relative;
}
/* 顶部重庆山城风格背景 */
.profile-page.data-v-14bc1b43::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(180deg, #1E90FF 0%, #4A90E2 25%, #6BA3E8 50%, rgba(107, 163, 232, 0.7) 70%, rgba(138, 180, 240, 0.4) 85%, rgba(169, 197, 248, 0.2) 95%, transparent 100%);
  z-index: 1;
}
/* 重庆山城剪影装饰 */
.profile-page.data-v-14bc1b43::after {
  content: '';
  position: absolute;
  top: 180rpx;
  left: 0;
  right: 0;
  height: 100rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 100'%3E%3Cpath d='M0,80 L50,75 L100,70 L150,65 L200,60 L250,65 L300,55 L350,60 L400,50 L450,55 L500,45 L550,50 L600,40 L650,45 L700,35 L750,40 L800,30 L850,35 L900,25 L950,30 L1000,20 L1050,25 L1100,15 L1150,20 L1200,10 L1200,100 L0,100 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 100rpx;
  z-index: 2;
  opacity: 0.8;
}
.user-card.data-v-14bc1b43 {
  margin: 0 30rpx 30rpx;
  margin-top: 0;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20rpx;
  color: #333;
  position: relative;
  z-index: 3;
  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.15);
  border: 1rpx solid rgba(30, 144, 255, 0.1);
}
.user-avatar-section.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.user-avatar.data-v-14bc1b43 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(30, 144, 255, 0.2);
  margin-right: 30rpx;
}
.user-info.data-v-14bc1b43 {
  flex: 1;
}
.user-name.data-v-14bc1b43 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  color: #333;
}
.user-desc.data-v-14bc1b43 {
  font-size: 26rpx;
  color: #666;
}
.user-stats.data-v-14bc1b43 {
  display: flex;
  justify-content: space-around;
  background: rgba(30, 144, 255, 0.05);
  border-radius: 16rpx;
  padding: 30rpx 0;
  border: 1rpx solid rgba(30, 144, 255, 0.1);
}
.stat-item.data-v-14bc1b43 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-number.data-v-14bc1b43 {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #1E90FF;
}
.stat-label.data-v-14bc1b43 {
  font-size: 24rpx;
  color: #666;
}
.section.data-v-14bc1b43 {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  z-index: 3;
}
.section-title.data-v-14bc1b43 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.menu-section.data-v-14bc1b43 {
  margin: 0 30rpx 30rpx;
}
.menu-list.data-v-14bc1b43 {
  padding: 0 30rpx;
}
.menu-item.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.menu-item.data-v-14bc1b43:last-child {
  border-bottom: none;
}
.menu-icon.data-v-14bc1b43 {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}
.menu-title.data-v-14bc1b43 {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-color);
}
.menu-extra.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.menu-count.data-v-14bc1b43 {
  font-size: 24rpx;
  color: #999;
}
.menu-arrow.data-v-14bc1b43 {
  font-size: 28rpx;
  color: #999;
}
.service-section.data-v-14bc1b43 {
  margin: 0 30rpx 30rpx;
}
.service-list.data-v-14bc1b43 {
  padding: 30rpx;
}
.service-item.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.service-item.data-v-14bc1b43:last-child {
  border-bottom: none;
}
.service-icon.data-v-14bc1b43 {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}
.service-info.data-v-14bc1b43 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.service-title.data-v-14bc1b43 {
  font-size: 30rpx;
  color: var(--text-color);
}
.service-desc.data-v-14bc1b43 {
  font-size: 24rpx;
  color: #999;
}
.settings-section.data-v-14bc1b43 {
  margin: 0 30rpx 30rpx;
}
.version-info.data-v-14bc1b43 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 30rpx;
  gap: 16rpx;
}
.version-text.data-v-14bc1b43 {
  font-size: 26rpx;
  color: #999;
}
.copyright-text.data-v-14bc1b43 {
  font-size: 24rpx;
  color: #ccc;
}
.modal-overlay.data-v-14bc1b43 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.feedback-modal.data-v-14bc1b43 {
  width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}
.modal-header.data-v-14bc1b43 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-14bc1b43 {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}
.modal-close.data-v-14bc1b43 {
  font-size: 40rpx;
  color: #999;
}
.modal-content.data-v-14bc1b43 {
  padding: 30rpx;
}
.feedback-textarea.data-v-14bc1b43 {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  font-size: 28rpx;
  resize: none;
  border: none;
}
.char-count.data-v-14bc1b43 {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}
.modal-footer.data-v-14bc1b43 {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}
.modal-btn.data-v-14bc1b43 {
  flex: 1;
  padding: 30rpx;
  text-align: center;
  font-size: 30rpx;
}
.cancel-btn.data-v-14bc1b43 {
  color: #999;
  border-right: 1rpx solid #f0f0f0;
}
.submit-btn.data-v-14bc1b43 {
  color: var(--primary-color);
  font-weight: bold;
}
.login-prompt-card.data-v-14bc1b43 {
  margin: 0 30rpx 30rpx;
  padding: 60rpx 40rpx;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  border-radius: 20rpx;
  color: white;
  position: relative;
  z-index: 3;
  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.prompt-info.data-v-14bc1b43 {
  margin-bottom: 40rpx;
}
.prompt-title.data-v-14bc1b43 {
  display: block;
  font-size: 38rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: white;
}
.prompt-desc.data-v-14bc1b43 {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}
.login-btn.data-v-14bc1b43 {
  background-color: #ffffff;
  color: #1E90FF;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 30rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
.login-btn-text.data-v-14bc1b43 {
  margin-left: 10rpx;
}

