# SCSS公共样式使用指南

## 项目配置

### 1. 文件结构
```
├── static/
│   └── css/
│       ├── common.scss         # 主要公共样式文件
│       ├── variables.scss      # SCSS变量定义
│       ├── mixins.scss         # SCSS混合器定义
│       └── README.md           # 详细使用说明
├── App.vue                     # 全局引入公共样式
├── pages/
│   ├── scss-demo.vue          # SCSS功能演示页面
│   └── test-common-styles.vue  # 工具类测试页面
```

### 2. 全局引入
在 `App.vue` 中已经全局引入了公共样式：
```scss
<style lang="scss">
/* 引入公共样式 */
@import './static/css/common.scss';
</style>
```

这意味着所有页面都可以直接使用这些样式类，无需重复引入。

### 3. 在页面中使用SCSS功能
在任何页面的 `<style>` 标签中，可以引入变量和混合器：
```scss
<style lang="scss" scoped>
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.my-component {
  color: $primary-color;
  @include flex-center;
  @include card-style($spacing-3, 'lg');
}
</style>
```

## 主要功能

### 1. SCSS变量系统
完整的设计系统变量，包括：
- **颜色变量**: `$primary-color`, `$secondary-color`, `$text-color` 等
- **间距变量**: `$spacing-1` 到 `$spacing-6` (10rpx - 60rpx)
- **字体大小变量**: `$text-xs` 到 `$text-3xl` (20rpx - 48rpx)
- **圆角变量**: `$border-radius-sm`, `$border-radius-lg`, `$border-radius-xl`
- **阴影变量**: `$shadow-sm`, `$shadow-md`, `$shadow-lg`
- **动画变量**: `$transition-fast`, `$transition-normal`, `$transition-slow`

### 2. SCSS混合器(Mixins)
强大的样式复用功能：
- **布局混合器**: `@include flex-center`, `@include flex-between` 等
- **样式混合器**: `@include button-style()`, `@include card-style()` 等
- **动画混合器**: `@include fade-in()`, `@include slide-up()` 等
- **响应式混合器**: `@include respond-to('md')` 等

### 3. 工具类系统
- **Flex布局**: `.flex-col`, `.flex-row`, `.justify-center`, `.align-center` 等
- **间距工具**: `.m-0` 到 `.m-4`, `.p-0` 到 `.p-4` 等（使用SCSS循环生成）
- **文本工具**: `.text-xs` 到 `.text-2xl`, `.font-normal`, `.font-bold` 等
- **颜色工具**: `.text-primary`, `.bg-primary` 等（使用SCSS映射生成）
- **边框工具**: `.border`, `.rounded`, `.border-primary` 等
- **文本省略**: `.ellipsis`, `.ellipsis-2`, `.ellipsis-3`

### 4. 增强功能
- **嵌套语法**: 支持CSS嵌套写法
- **自动生成**: 使用SCSS循环和映射自动生成工具类
- **模块化**: 变量、混合器、工具类分离管理
- **类型安全**: 通过变量避免硬编码值

## 实际应用示例

### SCSS功能对比

**传统CSS写法**:
```html
<view class="policy-item">
  <view class="policy-header">
    <text class="policy-title">标题</text>
    <view class="policy-category">分类</view>
  </view>
</view>

<style>
.policy-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.policy-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.policy-category {
  background-color: #1E90FF;
  color: white;
  font-size: 20rpx;
  padding: 10rpx;
  border-radius: 8rpx;
}
</style>
```

**使用SCSS变量和混合器**:
```html
<view class="policy-item">
  <view class="policy-header">
    <text class="policy-title">标题</text>
    <view class="policy-category">分类</view>
  </view>
</view>

<style lang="scss" scoped>
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.policy-item {
  @include card-style($spacing-3, 'md');
  margin-bottom: $spacing-2;
}

.policy-header {
  @include flex-between;
  margin-bottom: $spacing-2;
}

.policy-title {
  font-size: $text-lg;
  font-weight: bold;
  color: $text-color;
}

.policy-category {
  @include button-style($primary-color, white, 'small');
}
</style>
```

**使用工具类 + SCSS混合器**:
```html
<view class="policy-item bg-white rounded-lg p-3 mb-2 card-shadow">
  <view class="policy-header flex-between mb-2">
    <text class="policy-title text-lg font-bold text-dark">标题</text>
    <view class="policy-category">分类</view>
  </view>
</view>

<style lang="scss" scoped>
@import '@/static/css/mixins.scss';

.card-shadow {
  @include card-shadow('md');
}

.policy-category {
  @include button-style($primary-color, white, 'small');
}
</style>
```

### 常用布局模式

#### 1. 卡片布局
```html
<view class="bg-white rounded-lg p-3 mb-2">
  <view class="flex-between mb-2">
    <text class="text-lg font-bold text-dark">标题</text>
    <text class="text-sm text-gray">时间</text>
  </view>
  <text class="text-base text-gray ellipsis-2">内容描述...</text>
</view>
```

#### 2. 列表项布局
```html
<view class="flex-between p-3 border-b">
  <view class="flex-1">
    <text class="text-base font-bold text-dark ellipsis">项目名称</text>
    <text class="text-sm text-gray">项目描述</text>
  </view>
  <view class="bg-primary text-white text-xs p-1 rounded">状态</view>
</view>
```

#### 3. 按钮组布局
```html
<view class="flex-between p-3">
  <view class="bg-primary text-white p-2 rounded flex-center">
    <text class="text-sm">确认</text>
  </view>
  <view class="border border-primary p-2 rounded flex-center">
    <text class="text-sm text-primary">取消</text>
  </view>
</view>
```

## 使用建议

### 1. 优先使用工具类
- 减少自定义CSS的编写
- 保持样式的一致性
- 提高开发效率

### 2. 组合使用
- 多个工具类可以组合使用
- 例如: `flex-center bg-primary text-white p-2 rounded`

### 3. 响应式考虑
- 所有尺寸使用 `rpx` 单位
- 自动适配不同屏幕尺寸

### 4. 扩展原则
- 如需新增工具类，在 `common.css` 中添加
- 保持命名规范的一致性
- 更新相应的文档说明

## 测试页面

创建了 `pages/test-common-styles.vue` 测试页面，展示了所有工具类的使用效果，可以用来：
- 验证样式是否正常工作
- 查看各种样式的视觉效果
- 作为开发参考

## 注意事项

1. **全局生效**: 所有样式类在整个项目中都可以直接使用
2. **优先级**: 工具类样式优先级较高，可以覆盖大部分默认样式
3. **兼容性**: 样式已考虑uni-app的跨平台兼容性
4. **维护性**: 统一的样式管理，便于后期维护和修改

## 下一步

1. 在现有页面中逐步应用公共样式类
2. 减少页面中的自定义CSS代码
3. 根据实际使用情况扩展更多工具类
4. 建立团队的样式使用规范
