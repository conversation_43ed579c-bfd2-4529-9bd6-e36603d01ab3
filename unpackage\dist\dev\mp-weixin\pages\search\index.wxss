@charset "UTF-8";
.search-page.data-v-482e85b8 {
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  min-height: 100vh;
  position: relative;
}
/* 顶部重庆山城风格背景 */
.search-page.data-v-482e85b8::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, #1E90FF 0%, #4A90E2 30%, #6BA3E8 60%, rgba(107, 163, 232, 0.6) 80%, rgba(138, 180, 240, 0.3) 90%, transparent 100%);
  z-index: 1;
}
/* 重庆山城剪影装饰 */
.search-page.data-v-482e85b8::after {
  content: '';
  position: absolute;
  top: 140rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}
.search-header.data-v-482e85b8 {
  background: transparent;
  padding: 25rpx 30rpx 15rpx;
  position: relative;
  z-index: 2;
}
.search-box.data-v-482e85b8 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.search-input.data-v-482e85b8 {
  flex: 1;
  height: 50rpx;
  padding: 0 25rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 35rpx;
  font-size: 28rpx;
  border: none;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.search-btn.data-v-482e85b8 {
  width: 70rpx;
  height: 70rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 35rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.search-btn.data-v-482e85b8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
.search-icon.data-v-482e85b8 {
  width: 32rpx;
  height: 32rpx;
}
.search-types.data-v-482e85b8 {
  display: flex;
  gap: 20rpx;
  padding: 15rpx 30rpx 25rpx;
  background: transparent;
  position: relative;
  z-index: 3;
  overflow-x: auto;
  white-space: nowrap;
  justify-content: flex-start;
  /* 显示滚动条 */
}
.search-types.data-v-482e85b8::-webkit-scrollbar {
  height: 6rpx;
}
.search-types.data-v-482e85b8::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3rpx;
}
.search-types.data-v-482e85b8::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 3rpx;
}
.search-types.data-v-482e85b8::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.6);
}
.type-btn.data-v-482e85b8 {
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  color: #666;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(15rpx);
          backdrop-filter: blur(15rpx);
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-width: 100rpx;
  text-align: center;
}
.type-btn.active.data-v-482e85b8 {
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
  box-shadow: 0 4rpx 20rpx rgba(30, 144, 255, 0.3);
}
.type-btn.data-v-482e85b8:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.search-results.data-v-482e85b8 {
  padding: 30rpx;
  margin-top: 20rpx;
}
.result-summary.data-v-482e85b8 {
  margin-bottom: 30rpx;
  padding: 20rpx 30rpx;
  background: rgba(30, 144, 255, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1E90FF;
  font-weight: 600;
  text-align: center;
}
.result-section.data-v-482e85b8 {
  margin-bottom: 50rpx;
}
.section-title.data-v-482e85b8 {
  display: flex;
  align-items: center;
  gap: 15rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, rgba(30, 144, 255, 0.1), rgba(74, 144, 226, 0.1));
  border-radius: 16rpx;
  border-left: 6rpx solid #1E90FF;
}
.section-title .icon.data-v-482e85b8 {
  font-size: 38rpx;
}
.result-list.data-v-482e85b8 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.result-item.data-v-482e85b8 {
  background: white;
  padding: 35rpx;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4rpx solid transparent;
}
.result-item.data-v-482e85b8:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  border-left-color: #1E90FF;
  box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);
}
.item-title.data-v-482e85b8 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.4;
}
.item-content.data-v-482e85b8 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}
.item-meta.data-v-482e85b8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.meta-right.data-v-482e85b8 {
  display: flex;
  gap: 20rpx;
  align-items: center;
}
.category.data-v-482e85b8 {
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.date.data-v-482e85b8 {
  color: #999;
  font-size: 26rpx;
}
.no-results.data-v-482e85b8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  text-align: center;
}
.no-results-icon.data-v-482e85b8 {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}
.no-results-text.data-v-482e85b8 {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.no-results-tip.data-v-482e85b8 {
  font-size: 26rpx;
  color: #999;
}
.search-load-more.data-v-482e85b8 {
  padding: 30rpx;
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
}
.load-more.data-v-482e85b8 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);
  transition: all 0.3s ease;
}
.load-more.data-v-482e85b8:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);
}
.load-more-icon.data-v-482e85b8 {
  font-size: 24rpx;
  -webkit-animation: bounce-data-v-482e85b8 2s infinite;
          animation: bounce-data-v-482e85b8 2s infinite;
}
.loading-more.data-v-482e85b8 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  border-radius: 50rpx;
  font-size: 28rpx;
}
.loading-more-icon.data-v-482e85b8 {
  -webkit-animation: spin-data-v-482e85b8 1s linear infinite;
          animation: spin-data-v-482e85b8 1s linear infinite;
}
.no-more.data-v-482e85b8 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.9);
  color: #999;
  border-radius: 50rpx;
  font-size: 26rpx;
}
@-webkit-keyframes bounce-data-v-482e85b8 {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-6rpx);
            transform: translateY(-6rpx);
}
60% {
    -webkit-transform: translateY(-3rpx);
            transform: translateY(-3rpx);
}
}
@keyframes bounce-data-v-482e85b8 {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-6rpx);
            transform: translateY(-6rpx);
}
60% {
    -webkit-transform: translateY(-3rpx);
            transform: translateY(-3rpx);
}
}
@-webkit-keyframes spin-data-v-482e85b8 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-482e85b8 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.category-list.data-v-482e85b8 {
  padding: 30rpx;
  margin-top: 20rpx;
}
.list-header.data-v-482e85b8 {
  display: flex;
  align-items: center;
  gap: 15rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
}
.category-items.data-v-482e85b8 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.category-item.data-v-482e85b8 {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4rpx solid transparent;
  min-height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.category-item.data-v-482e85b8:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  border-left-color: #1E90FF;
  box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);
}
.item-title.data-v-482e85b8 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
  /* 标题最多显示2行 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 89rpx;
  /* 32rpx * 1.4 * 2 ≈ 89rpx */
}
.item-content.data-v-482e85b8 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15rpx;
  flex: 1;
  /* 内容最多显示3行 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 134rpx;
  /* 28rpx * 1.6 * 3 ≈ 134rpx */
}
.item-meta.data-v-482e85b8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.meta-right.data-v-482e85b8 {
  display: flex;
  gap: 20rpx;
  align-items: center;
}
.category.data-v-482e85b8 {
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.date.data-v-482e85b8 {
  color: #999;
  font-size: 26rpx;
}
.loading-state.data-v-482e85b8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  text-align: center;
}
.loading-icon.data-v-482e85b8 {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}
.loading-text.data-v-482e85b8 {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}
.load-more-text.data-v-482e85b8 {
  font-size: 28rpx;
  font-weight: 600;
}
/* 新增：政策解读网格样式 */
.interpretation-grid.data-v-482e85b8 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}
.interpretation-item.data-v-482e85b8 {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}
.interpretation-cover.data-v-482e85b8 {
  position: relative;
  width: 100%;
  padding-top: 56.25%;
  /* 16:9 宽高比 */
}
.interpretation-image.data-v-482e85b8 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
}
.play-icon.data-v-482e85b8 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  pointer-events: none;
}
.text-icon.data-v-482e85b8 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40rpx;
  pointer-events: none;
}
.interpretation-title.data-v-482e85b8 {
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
  /* 确保标题占满剩余空间 */
}
/* 渐变色背景占位符 */
.interpretation-bg-1.data-v-482e85b8 {
  background: linear-gradient(135deg, #FF9A8B 0%, #FF6A88 100%);
}
.interpretation-bg-2.data-v-482e85b8 {
  background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
}
.interpretation-bg-3.data-v-482e85b8 {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}
.interpretation-bg-4.data-v-482e85b8 {
  background: linear-gradient(135deg, #fccb90 0%, #d57eeb 100%);
}
.interpretation-bg-5.data-v-482e85b8 {
  background: linear-gradient(135deg, #5ee7df 0%, #b490ca 100%);
}
.interpretation-bg-6.data-v-482e85b8 {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

