@charset "UTF-8";
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 手机适配 - 增大关键UI元素尺寸 */
.uni-navigation-bar {
  height: 88rpx !important;
}
.uni-navigation-bar .uni-navigation-bar-text {
  font-size: 36rpx !important;
  font-weight: 600 !important;
}
.uni-tabbar {
  height: 100rpx !important;
  padding-top: 10rpx !important;
  padding-bottom: 10rpx !important;
}
.uni-tabbar-item {
  font-size: 22rpx !important;
}
.uni-tabbar .uni-tabbar-item-icon {
  width: 48rpx !important;
  height: 48rpx !important;
}
/* 按钮优化 */
button {
  font-size: 32rpx !important;
  padding: 20rpx 40rpx !important;
  border-radius: 12rpx !important;
}
/* 输入框优化 */
input, textarea {
  font-size: 30rpx !important;
  padding: 20rpx !important;
}
/* 矢量图标通用样式 */
.emoji-icon {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
  line-height: 1;
  font-style: normal;
}
/* 手机端图标放大 */
.icon-large {
  font-size: 48rpx !important;
}
.icon-medium {
  font-size: 36rpx !important;
}
.icon-small {
  font-size: 28rpx !important;
}
/* 重庆风格红白主题色 */
:root {
  --primary-color: #1E90FF;
  --secondary-color: #4A90E2;
  --background-color: #FFFFFF;
  --text-color: #333333;
  --border-color: #E5E5E5;
  --gray-light: #F5F5F5;
  --gray-medium: #999999;
}
/* 通用样式 */
.container {
  padding: 0 30rpx;
  background-color: var(--background-color);
}
.section {
  margin-bottom: 30rpx;
  background-color: var(--background-color);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}
.flex {
  display: flex;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
/* 重庆特色装饰元素 */
.cq-decoration {
  position: relative;
}
.cq-decoration::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(30, 144, 255, 0.1) 0%, rgba(74, 144, 226, 0.1) 100%);
  border-radius: inherit;
  pointer-events: none;
}

