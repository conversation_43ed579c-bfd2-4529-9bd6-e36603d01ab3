<template>
  <view class="bank-list">
    <!-- 搜索头部 -->
    <view class="search-header">
      <view class="search-box">
        <input 
          v-model="searchKeyword" 
          placeholder="搜索银行名称"
          class="search-input"
          @confirm="searchBanks"
        />
        <view @click="searchBanks" class="search-btn">
          <image src="/static/icons/search.svg" class="search-icon" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 功能按钮 -->
    <view class="function-section">
      <view @click="refreshBanks" class="function-btn">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">换一换</text>
      </view>
      <view @click="showMapView" class="function-btn">
        <text class="btn-icon">🗺️</text>
        <text class="btn-text">地图查看</text>
      </view>
    </view>

    <!-- 银行列表 -->
    <view class="banks-section">
      <!-- 结果统计 -->
      <view class="result-summary" v-if="bankList.length > 0">
        <text class="summary-icon">🏦</text>
        <text class="summary-text">银行机构</text>
        <text class="summary-count">({{ totalCount }}家)</text>
      </view>
      
      <view class="bank-list-container">
        <view 
          v-for="bank in bankList" 
          :key="bank.id"
          class="bank-item"
          @click="viewBankDetail(bank)"
        >
          <!-- 银行基本信息 -->
          <view class="bank-main">
            <view class="bank-icon-wrapper">
              <image 
                v-if="bank.icon" 
                :src="bank.icon" 
                class="bank-icon"
                mode="aspectFit"
              />
              <view v-else class="bank-icon-placeholder">
                <text>🏦</text>
              </view>
            </view>
            
            <view class="bank-info">
              <view class="bank-name">{{ bank.name }}</view>
              <view class="bank-contact">
                <text class="contact-label">联系人：</text>
                <text class="contact-name">{{ bank.contact_person }}</text>
              </view>
            </view>
          </view>
          
          <!-- 联系方式 -->
          <view class="bank-contact-info">
            <view class="contact-item">
              <text class="contact-icon">📞</text>
              <text class="contact-text">{{ bank.phone }}</text>
              <view @tap="callBank(bank.phone)" class="call-btn" catchtap="true">
                <text>拨打</text>
              </view>
            </view>
            <view class="contact-item">
              <text class="contact-icon">📍</text>
              <text class="contact-text">{{ bank.address }}</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="bank-actions">
            <view @tap="showLocation(bank)" class="action-btn location-btn" catchtap="true">
              <text class="action-icon">📍</text>
              <text class="action-text">位置</text>
            </view>
            <view @tap="consultBank(bank)" class="action-btn consult-btn" catchtap="true">
              <text class="action-icon">💬</text>
              <text class="action-text">咨询</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more-section" v-if="hasMore && bankList.length > 0">
        <view class="load-more" v-if="!loading" @click="loadMore">
          <text class="load-more-text">加载更多银行</text>
          <text class="load-more-icon">↓</text>
        </view>
        
        <view class="loading-more" v-if="loading">
          <text class="loading-more-text">正在加载...</text>
          <text class="loading-more-icon">⏳</text>
        </view>
      </view>

      <!-- 无数据提示 -->
      <view v-if="bankList.length === 0 && !loading" class="empty-state">
        <text class="empty-icon">🏦</text>
        <text class="empty-text">暂无银行机构信息</text>
        <text class="empty-tip">请尝试刷新或使用其他关键词搜索</text>
        <view @click="refreshBanks" class="refresh-btn">
          <text>刷新重试</text>
        </view>
      </view>
    </view>

    <!-- 地图视图模态框 -->
    <view v-if="showMap" class="map-modal" @click="closeMapView">
              <view class="map-container" catchtap="true">
        <view class="map-header">
          <text class="map-title">银行位置分布</text>
          <view @click="closeMapView" class="close-btn">
            <text>✕</text>
          </view>
        </view>
        
        <!-- 银行位置列表 -->
        <scroll-view class="location-list" scroll-y>
          <view 
            v-for="bank in bankList" 
            :key="bank.id"
            class="location-item"
            @click="openBankLocation(bank)"
          >
            <view class="location-info">
              <view class="bank-name-loc">{{ bank.name }}</view>
              <view class="bank-address">{{ bank.address }}</view>
              <view class="location-meta">
                <text class="distance">📍 点击打开地图导航</text>
              </view>
            </view>
            <view class="location-actions">
              <view @tap="callBank(bank.phone)" class="action-call" catchtap="true">
                <text class="action-icon">📞</text>
              </view>
              <view @tap="openBankLocation(bank)" class="action-nav" catchtap="true">
                <text class="action-icon">🧭</text>
              </view>
            </view>
          </view>
        </scroll-view>
        
        <view class="map-footer">
          <text class="map-tip">点击银行信息打开系统地图导航</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      bankList: [],
      searchKeyword: '',
      currentPage: 1,
      totalCount: 0,
      hasMore: true,
      loading: false,
      showMap: false
    }
  },

  onLoad() {
    try {
      this.loadBanks().catch(err => {
        console.error('银行列表加载失败:', err)
      })
    } catch (err) {
      console.error('onLoad错误:', err)
    }
  },

  onPullDownRefresh() {
    this.refreshBanks()
      .then(() => {
        uni.stopPullDownRefresh()
      })
      .catch(err => {
        console.error('下拉刷新失败:', err)
        uni.stopPullDownRefresh()
      })
  },

  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },

  methods: {
    async loadBanks(isRefresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      try {
        const params = {
          page: isRefresh ? 1 : this.currentPage,
          per_page: 10
        }
        
        if (this.searchKeyword) {
          params.name = this.searchKeyword
        }
        
        const res = await api.getBanks(params)
        const newBanks = res.data?.items || []
        
        // 为每个银行添加默认坐标（如果没有的话）
        const banksWithCoords = newBanks.map((bank, index) => ({
          ...bank,
          latitude: bank.latitude || (29.5647 + (Math.random() - 0.5) * 0.1),
          longitude: bank.longitude || (106.5507 + (Math.random() - 0.5) * 0.1)
        }))
        
        if (isRefresh) {
          this.bankList = banksWithCoords
          this.currentPage = 1
        } else {
          this.bankList = [...this.bankList, ...banksWithCoords]
        }
        
        this.totalCount = res.data?.total || 0
        this.hasMore = res.data?.page < res.data?.pages
        this.currentPage = res.data?.page + 1
        
      } catch (error) {
        console.error('加载银行列表失败:', error)
        // 使用模拟数据
        this.loadMockData(isRefresh)
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },

    loadMockData(isRefresh = false) {
      const mockBanks = [
        {
          id: 1,
          name: '中国银行重庆分行',
          contact_person: '张经理',
          phone: '023-********',
          address: '重庆市渝中区解放碑步行街123号',
          latitude: 29.5647,
          longitude: 106.5507
        },
        {
          id: 2,
          name: '建设银行重庆分行',
          contact_person: '李经理',
          phone: '023-********',
          address: '重庆市江北区观音桥步行街456号',
          latitude: 29.5734,
          longitude: 106.5347
        },
        {
          id: 3,
          name: '工商银行重庆分行',
          contact_person: '王经理',
          phone: '023-********',
          address: '重庆市沙坪坝区三峡广场789号',
          latitude: 29.5267,
          longitude: 106.4564
        }
      ]
      
      if (isRefresh) {
        this.bankList = mockBanks
      } else {
        this.bankList = [...this.bankList, ...mockBanks]
      }
      
      this.totalCount = mockBanks.length
      this.hasMore = false
    },

    searchBanks() {
      this.currentPage = 1
      this.hasMore = true
      this.loadBanks(true)
    },

    async refreshBanks() {
      try {
        const res = await api.getBanks({ per_page: 10, random: true })
        const newBanks = res.data?.data || res.data?.items || []
        
        // 添加坐标信息
        this.bankList = newBanks.map(bank => ({
          ...bank,
          latitude: bank.latitude || (29.5647 + (Math.random() - 0.5) * 0.1),
          longitude: bank.longitude || (106.5507 + (Math.random() - 0.5) * 0.1)
        }))
        
        this.totalCount = res.data?.total || 0
        
        uni.showToast({
          title: '已刷新',
          icon: 'success'
        })
        
        return Promise.resolve()
      } catch (error) {
        console.error('刷新银行列表失败:', error)
        // 使用模拟数据刷新
        this.loadMockData(true)
        uni.showToast({
          title: '已刷新',
          icon: 'success'
        })
        
        return Promise.resolve() // 即使出错也返回resolved，因为我们有fallback
      }
    },

    loadMore() {
      this.loadBanks()
    },

    viewBankDetail(bank) {
      uni.showModal({
        title: bank.name,
        content: `联系人：${bank.contact_person}\n电话：${bank.phone}\n地址：${bank.address}`,
        showCancel: false,
        confirmText: '我知道了'
      })
    },

    callBank(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        fail: (err) => {
          console.error('拨打电话失败:', err)
          uni.showToast({
            title: '拨打失败',
            icon: 'none'
          })
        }
      })
    },

    showLocation(bank) {
      if (!bank.latitude || !bank.longitude) {
        uni.showToast({
          title: '该银行暂无位置信息',
          icon: 'none'
        })
        return
      }
      
      uni.openLocation({
        latitude: parseFloat(bank.latitude),
        longitude: parseFloat(bank.longitude),
        name: bank.name,
        address: bank.address,
        fail: (err) => {
          console.error('打开地图失败:', err)
          uni.showToast({
            title: '地图打开失败',
            icon: 'none'
          })
        }
      })
    },

    showMapView() {
      if (this.bankList.length === 0) {
        uni.showToast({
          title: '暂无银行数据',
          icon: 'none'
        })
        return
      }
      
      this.showMap = true
    },

    closeMapView() {
      this.showMap = false
    },

    openBankLocation(bank) {
      if (!bank.latitude || !bank.longitude) {
        uni.showToast({
          title: '该银行暂无位置信息',
          icon: 'none'
        })
        return
      }
      
      uni.openLocation({
        latitude: parseFloat(bank.latitude),
        longitude: parseFloat(bank.longitude),
        name: bank.name,
        address: bank.address,
        fail: (err) => {
          console.error('打开地图失败:', err)
          uni.showToast({
            title: '地图打开失败',
            icon: 'none'
          })
        }
      })
    },

    consultBank(bank) {
      // 跳转到咨询页面，并预填银行信息
      uni.switchTab({
        url: '/pages/consultation/index'
      })
      
      // 通过事件总线传递银行信息
      this.$nextTick(() => {
        uni.$emit('selectBank', bank)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bank-list {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  position: relative;
}

/* 顶部重庆山城风格背景 */
.bank-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, 
    #1E90FF 0%, 
    #4A90E2 30%, 
    #6495ED 60%, 
    rgba(100, 149, 237, 0.6) 80%, 
    rgba(156, 196, 255, 0.3) 90%, 
    transparent 100%
  );
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.bank-list::after {
  content: '';
  position: absolute;
  top: 140rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}

// 搜索头部样式
.search-header {
  background: transparent;
  padding: 30rpx;
  position: relative;
  z-index: 3;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 144, 255, 0.9) 0%, rgba(74, 144, 226, 0.9) 100%);
    backdrop-filter: blur(10px);
  }
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  position: relative;
  z-index: 1;
}

.search-input {
  flex: 1;
  height: 60rpx;
  padding: 0 30rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  &::placeholder {
    color: #999;
  }
}

.search-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
  }
}

.search-icon {
  width: 28rpx;
  height: 28rpx;
  filter: invert(1);
}

// 功能按钮样式
.function-section {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.function-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #f8f8f8 0%, #fff 100%);
  border: 1rpx solid #e0e0e0;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  
  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 100%);
  }
  
  .btn-icon {
    font-size: 28rpx;
  }
}

// 银行列表样式
.banks-section {
  padding: 30rpx;
}

.result-summary {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx 30rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.summary-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.summary-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.summary-count {
  font-size: 26rpx;
  color: #1E90FF;
  font-weight: normal;
}

.bank-list-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.bank-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1rpx solid #f0f0f0;
  
  &:active {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  }
}

// 银行基本信息
.bank-main {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.bank-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.bank-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bank-icon-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
}

.bank-info {
  flex: 1;
}

.bank-name {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.bank-contact {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.contact-label {
  margin-right: 5rpx;
}

.contact-name {
  color: #1E90FF;
  font-weight: 500;
}

// 联系方式
.bank-contact-info {
  margin-bottom: 24rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.contact-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  width: 40rpx;
  text-align: center;
}

.contact-text {
  flex: 1;
  color: #333;
  margin-right: 20rpx;
  line-height: 1.4;
}

.call-btn {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
  }
}

// 操作按钮
.bank-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 28rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 25rpx;
  background: white;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  
  &.location-btn {
    &:active {
      transform: scale(0.95);
      border-color: #ccc;
      background: #f8f8f8;
    }
  }
  
  &.consult-btn {
    background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
    color: white;
    border-color: #1E90FF;
    box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
    
    &:active {
      transform: scale(0.95);
      box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
    }
  }
  
  .action-icon {
    font-size: 28rpx;
  }
}

// 加载更多
.load-more-section {
  margin-top: 40rpx;
  text-align: center;
}

.load-more {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #f8f8f8 0%, #fff 100%);
  color: #666;
  border: 2rpx solid #e0e0e0;
  border-radius: 30rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 100%);
  }
}

.load-more-text {
  margin-right: 10rpx;
}

.load-more-icon {
  font-size: 28rpx;
  animation: bounce 2s infinite;
}

.loading-more {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  color: #999;
  border-radius: 30rpx;
  font-size: 28rpx;
}

.loading-more-text {
  margin-right: 10rpx;
}

.loading-more-icon {
  font-size: 28rpx;
  animation: spin 1s linear infinite;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.refresh-btn {
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
  }
}

// 地图模态框
.map-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.map-container {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 800rpx;
  height: 80%;
  max-height: 800rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.map-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
}

.map-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: white;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.9);
    background: rgba(255, 255, 255, 0.3);
  }
}

.location-list {
  flex: 1;
  overflow-y: auto;
}

.location-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.location-info {
  flex: 1;
}

.bank-name-loc {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.bank-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.location-meta {
  font-size: 26rpx;
  color: #999;
  margin-top: 8rpx;
}

.location-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-call {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
  }
}

.action-nav {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
  }
}

.map-footer {
  padding: 20rpx 30rpx;
  text-align: center;
  background: #f8f8f8;
  border-top: 1rpx solid #f0f0f0;
}

.map-tip {
  font-size: 26rpx;
  color: #666;
}

// 动画
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8rpx);
  }
  60% {
    transform: translateY(-4rpx);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 