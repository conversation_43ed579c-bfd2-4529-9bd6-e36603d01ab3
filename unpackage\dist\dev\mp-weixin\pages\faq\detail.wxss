@charset "UTF-8";
.faq-detail-page.data-v-72665da3 {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding: 30rpx;
  padding-bottom: 180rpx;
}
.content.data-v-72665da3 {
  width: 100%;
}
.qa-card.data-v-72665da3 {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.question-section.data-v-72665da3, .answer-section.data-v-72665da3 {
  position: relative;
  padding-left: 60rpx;
  line-height: 1.7;
}
.question-section.data-v-72665da3 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}
.answer-section.data-v-72665da3 {
  font-size: 30rpx;
  color: #555;
  padding-bottom: 40rpx;
  border-bottom: 1rpx solid #eee;
}
.question-section.data-v-72665da3::before, .answer-section.data-v-72665da3::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
}
.question-section.data-v-72665da3::before {
  content: '问';
  background-color: #007bff;
}
.answer-section.data-v-72665da3::before {
  content: '答';
  background-color: #28a745;
}
.meta-info.data-v-72665da3 {
  display: flex;
  justify-content: space-between;
  padding-top: 30rpx;
  font-size: 26rpx;
  color: #999;
}
.disclaimer.data-v-72665da3 {
  margin-top: 40rpx;
  padding: 20rpx;
  font-size: 24rpx;
  color: #aaa;
  line-height: 1.6;
  text-align: center;
}
.loading.data-v-72665da3 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
}
.action-bar-sticky.data-v-72665da3 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.action-btn.data-v-72665da3 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  transition: color 0.2s ease-in-out, -webkit-transform 0.1s ease;
  transition: color 0.2s ease-in-out, transform 0.1s ease;
  transition: color 0.2s ease-in-out, transform 0.1s ease, -webkit-transform 0.1s ease;
}
.action-btn.data-v-72665da3::after {
  border: none;
}
.action-btn.data-v-72665da3:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.action-btn.active.data-v-72665da3 {
  color: #DC143C;
}
.action-btn .icon.data-v-72665da3 {
  font-size: 44rpx;
  margin-bottom: 6rpx;
  transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  opacity: 0.7;
  -webkit-filter: grayscale(80%);
          filter: grayscale(80%);
}
.action-btn.active .icon.data-v-72665da3 {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  opacity: 1;
  -webkit-filter: grayscale(0%);
          filter: grayscale(0%);
}
.action-btn .action-text.data-v-72665da3 {
  font-size: 24rpx;
}

