.custom-tab-bar.data-v-47b4a9d0 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: #FFFFFF;
  border-top: 1rpx solid #E5E5E5;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.tab-item.data-v-47b4a9d0 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 5rpx;
  transition: all 0.3s ease;
}
.tab-item.active .tab-icon.data-v-47b4a9d0 {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.tab-item.active .tab-text.data-v-47b4a9d0 {
  color: #1E90FF;
  font-weight: 600;
}
.tab-item.data-v-47b4a9d0:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.tab-icon.data-v-47b4a9d0 {
  font-size: 48rpx;
  line-height: 1;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}
.tab-text.data-v-47b4a9d0 {
  font-size: 22rpx;
  color: #999999;
  line-height: 1;
  transition: all 0.3s ease;
  text-align: center;
}

