<view class="index-page data-v-********"><view class="search-header data-v-********"><view data-event-opts="{{[['tap',[['toSearch',['$event']]]]]}}" class="search-box data-v-********" bindtap="__e"><uni-icons vue-id="8dd740cc-1" type="search" color="#999" size="18" class="data-v-********" bind:__l="__l"></uni-icons><text class="search-placeholder data-v-********">🏔️ 搜索山城跨境政策、咨询...</text></view></view><view class="banner-section data-v-********"><swiper class="banner-swiper data-v-********" autoplay="{{true}}" interval="3000" duration="500" circular="{{true}}" indicator-dots="{{true}}" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#1E90FF"><block wx:for="{{bannerList}}" wx:for-item="banner" wx:for-index="index"><swiper-item data-event-opts="{{[['tap',[['onBannerClick',['$0'],[[['bannerList','id||index',banner.id||index]]]]]]]}}" bindtap="__e" class="data-v-********"><image class="banner-image data-v-********" src="{{banner.image}}" mode="aspectFill" data-event-opts="{{[['error',[['onBannerImageError',['$0',index],[[['bannerList','id||index',banner.id||index]]]]]],['load',[['onBannerImageLoad',['$0'],[[['bannerList','id||index',banner.id||index]]]]]]]}}" binderror="__e" bindload="__e"></image><view class="banner-overlay data-v-********"><text class="banner-title data-v-********">{{banner.title}}</text></view></swiper-item></block></swiper></view><view class="menu-section section data-v-********"><view class="menu-grid data-v-********"><view data-event-opts="{{[['tap',[['toPage',['/pages/policy/index']]]]]}}" class="menu-item data-v-********" bindtap="__e"><view class="menu-icon data-v-********"><text class="icon-font data-v-********" style="color:#1E90FF;font-size:48rpx;">📋</text></view><text class="menu-text data-v-********">政策文件</text></view><view data-event-opts="{{[['tap',[['toFAQList',['$event']]]]]}}" class="menu-item data-v-********" bindtap="__e"><view class="menu-icon data-v-********"><text class="icon-font data-v-********" style="color:#1E90FF;font-size:48rpx;">🤔</text></view><text class="menu-text data-v-********">热门问答</text></view><view data-event-opts="{{[['tap',[['toPage',['/pages/consultation/index']]]]]}}" class="menu-item data-v-********" bindtap="__e"><view class="menu-icon data-v-********"><text class="icon-font data-v-********" style="color:#1E90FF;font-size:48rpx;">💬</text></view><text class="menu-text data-v-********">业务咨询</text></view><view data-event-opts="{{[['tap',[['toInterpretationList',['$event']]]]]}}" class="menu-item data-v-********" bindtap="__e"><view class="menu-icon data-v-********"><text class="icon-font data-v-********" style="color:#1E90FF;font-size:48rpx;">📺</text></view><text class="menu-text data-v-********">政策解读</text></view></view></view><view class="stats-section data-v-********"><view class="stats-header data-v-********"><text class="stats-title data-v-********">🏙️ 服务数据统计</text><text class="stats-time data-v-********">{{"截至："+$root.m0}}</text></view><view class="stats-cards data-v-********"><view class="stats-card data-v-********"><view class="stats-icon data-v-********">📋</view><view class="stats-info data-v-********"><text class="stats-number data-v-********">{{statistics.policyCount||0}}</text><text class="stats-unit data-v-********">份</text></view><text class="stats-label data-v-********">已收录政策文件</text></view><view class="stats-card data-v-********"><view class="stats-icon data-v-********">💬</view><view class="stats-info data-v-********"><text class="stats-number data-v-********">{{statistics.consultCount||0}}</text><text class="stats-unit data-v-********">次</text></view><text class="stats-label data-v-********">已解答业务咨询</text></view><view class="stats-card data-v-********"><view class="stats-icon data-v-********">👀</view><view class="stats-info data-v-********"><text class="stats-number data-v-********">{{statistics.visitCount||0}}</text><text class="stats-unit data-v-********">次</text></view><text class="stats-label data-v-********">累计访问量</text></view><view class="stats-card data-v-********"><view class="stats-icon data-v-********">🏦</view><view class="stats-info data-v-********"><text class="stats-number data-v-********">{{statistics.bankCount||0}}</text><text class="stats-unit data-v-********">个</text></view><text class="stats-label data-v-********">在线机构数量</text></view></view></view><view class="bank-section section data-v-********"><view class="section-header data-v-********"><view class="section-title-wrap data-v-********"><text class="section-icon data-v-********">🏦金融机构</text><text class="section-subtitle data-v-********">服务重庆跨境融资发展</text></view><view class="more-actions data-v-********"><text data-event-opts="{{[['tap',[['refreshBanks',['$event']]]]]}}" class="action-btn refresh-btn data-v-********" bindtap="__e">🔄</text><text data-event-opts="{{[['tap',[['toBankListPage',['$event']]]]]}}" class="action-btn more-btn data-v-********" bindtap="__e">更多</text></view></view><view class="bank-grid data-v-********"><block wx:for="{{$root.l0}}" wx:for-item="bank" wx:for-index="__i0__" wx:key="id"><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({bank:bank.$orig})}}" class="bank-card data-v-********" bindtap="__e"><view class="bank-card-header data-v-********"><view class="bank-logo data-v-********"><block wx:if="{{bank.m1}}"><image class="bank-logo-image data-v-********" src="{{bank.m2}}" mode="aspectFill" data-event-opts="{{[['error',[['e1',['$event']]]]]}}" data-event-params="{{({bank:bank.$orig})}}" binderror="__e"></image></block><block wx:else><view class="bank-logo-placeholder data-v-********">🏦</view></block></view><view class="bank-basic-info data-v-********"><text class="bank-name data-v-********">{{bank.$orig.name||'银行名称'}}</text><view class="bank-contact-row data-v-********"><text class="bank-contact data-v-********">{{bank.$orig.contact_person||'联系人'}}</text><text data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({bank:bank.$orig})}}" class="bank-phone data-v-********" catchtap="__e">{{bank.$orig.phone||'电话号码'}}</text></view></view><view class="bank-actions data-v-********"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({bank:bank.$orig})}}" class="bank-location-btn data-v-********" catchtap="__e"><text class="location-icon data-v-********">📍</text></view></view></view></view></block><block wx:else><view class="bank-empty data-v-********"><text class="empty-icon data-v-********">🏦</text><text class="empty-text data-v-********">正在加载银行信息...</text></view></block></block></view></view><view class="news-section section data-v-********"><view class="section-header data-v-********"><view class="section-title-wrap data-v-********"><text class="section-icon data-v-********">🌉最新资讯</text><text class="section-subtitle data-v-********">山城跨境金融最新动态</text></view><text data-event-opts="{{[['tap',[['toNewsList',['$event']]]]]}}" class="action-btn more-btn data-v-********" bindtap="__e">更多</text></view><view class="news-list data-v-********"><block wx:for="{{$root.l1}}" wx:for-item="news" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" data-event-params="{{({news:news.$orig})}}" class="news-item data-v-********" bindtap="__e"><view class="news-cover data-v-********"><block wx:if="{{news.m3}}"><image class="news-cover-image data-v-********" src="{{news.m4}}" mode="aspectFill" data-event-opts="{{[['error',[['e5',['$event']]]]]}}" data-event-params="{{({news:news.$orig})}}" binderror="__e"></image></block><block wx:else><view class="{{['news-cover-placeholder','data-v-********','news-cover-'+news.m5]}}">{{''+news.m6+''}}</view></block></view><view class="news-content data-v-********"><text class="news-title data-v-********">{{news.$orig.title}}</text><view class="news-meta data-v-********"><text class="news-category data-v-********">{{news.$orig.category}}</text><text class="news-date data-v-********">{{news.m7}}</text><text class="news-views data-v-********">{{"👁 "+news.m8}}</text></view></view><text class="news-arrow data-v-********">›</text></view></block></view></view><view class="faq-section section data-v-********"><view class="section-header data-v-********"><view class="section-title-wrap data-v-********"><text class="section-icon data-v-********">🤔热门问答</text><text class="section-subtitle data-v-********">山城企业常见问题解答</text></view><text data-event-opts="{{[['tap',[['toFAQList',['$event']]]]]}}" class="action-btn more-btn data-v-********" bindtap="__e">更多</text></view><view class="faq-list data-v-********"><block wx:for="{{$root.l2}}" wx:for-item="faq" wx:for-index="__i2__" wx:key="id"><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" data-event-params="{{({faq})}}" class="faq-item data-v-********" bindtap="__e"><view class="faq-icon data-v-********">❓</view><view class="faq-content data-v-********"><text class="faq-question data-v-********">{{faq.question}}</text><view class="faq-stats data-v-********"><text class="faq-stat data-v-********">{{"👁 "+(faq.view_count||0)}}</text><text class="faq-stat data-v-********">{{"👍 "+(faq.like_count||0)}}</text></view></view><text class="faq-arrow data-v-********">›</text></view></block></view></view><view class="interpretation-section section data-v-********"><view class="section-header data-v-********"><view class="section-title-wrap data-v-********"><text class="section-icon data-v-********">📺政策解读</text><text class="section-subtitle data-v-********">重庆跨境政策专业解读</text></view><text data-event-opts="{{[['tap',[['toInterpretationList',['$event']]]]]}}" class="action-btn more-btn data-v-********" bindtap="__e">更多</text></view><view class="interpretation-grid data-v-********"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['e7',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="interpretation-item data-v-********" bindtap="__e"><view class="interpretation-cover data-v-********"><block wx:if="{{item.m9}}"><image class="interpretation-image data-v-********" src="{{item.m10}}" mode="aspectFill" data-event-opts="{{[['error',[['e8',['$event']]]]]}}" data-event-params="{{({item:item.$orig,index})}}" binderror="__e"></image></block><block wx:else><view class="{{['interpretation-image','data-v-********','interpretation-bg-'+(index%6+1)]}}"></view></block><block wx:if="{{item.$orig.video_url}}"><view class="play-icon data-v-********"><text class="data-v-********">▶</text></view></block><block wx:else><view class="text-icon data-v-********"><text class="data-v-********">📄</text></view></block></view><text class="interpretation-item-title data-v-********">{{item.$orig.title||'课程'+(index+1)+'：利用...'}}</text></view></block></view></view><custom-tab-bar vue-id="8dd740cc-2" class="data-v-********" bind:__l="__l"></custom-tab-bar></view>