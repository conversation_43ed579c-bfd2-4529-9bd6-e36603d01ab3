/* SCSS混合器文件 - mixins.scss */
/* 可以在任何SCSS文件中引入使用 */

@import './variables.scss';

// Flex布局混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// 文本省略混合器
@mixin text-ellipsis($lines: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  @if $lines == 1 {
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
  }
}

// 阴影混合器
@mixin card-shadow($level: 'md') {
  @if $level == 'sm' {
    box-shadow: $shadow-sm;
  } @else if $level == 'md' {
    box-shadow: $shadow-md;
  } @else if $level == 'lg' {
    box-shadow: $shadow-lg;
  }
}

// 按钮样式混合器
@mixin button-style($bg-color: $primary-color, $text-color: white, $size: 'normal') {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  border-radius: $border-radius-sm;
  cursor: pointer;
  transition: $transition-fast;
  
  @if $size == 'small' {
    padding: $spacing-1 $spacing-2;
    font-size: $text-sm;
  } @else if $size == 'large' {
    padding: $spacing-3 $spacing-4;
    font-size: $text-lg;
  } @else {
    padding: $spacing-2 $spacing-3;
    font-size: $text-base;
  }
  
  &:active {
    opacity: 0.8;
    transform: translateY(1rpx);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// 输入框样式混合器
@mixin input-style {
  border: 1rpx solid $border-color;
  border-radius: $border-radius-sm;
  padding: $spacing-2;
  font-size: $text-base;
  background-color: $background-color;
  transition: $transition-fast;
  
  &:focus {
    border-color: $primary-color;
    outline: none;
  }
  
  &::placeholder {
    color: $gray-medium;
  }
}

// 卡片样式混合器
@mixin card-style($padding: $spacing-3, $shadow: 'md') {
  background-color: $background-color;
  border-radius: $border-radius-lg;
  padding: $padding;
  @include card-shadow($shadow);
}

// 渐变背景混合器
@mixin gradient-bg($start-color: $primary-color, $end-color: $secondary-color, $direction: 'to right') {
  background: linear-gradient(#{$direction}, $start-color, $end-color);
}

// 居中定位混合器
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 清除浮动混合器
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 隐藏滚动条混合器
@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 响应式断点混合器
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'sm' {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  } @else if $breakpoint == 'md' {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  } @else if $breakpoint == 'lg' {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  } @else if $breakpoint == 'xl' {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
}

// 动画混合器
@mixin fade-in($duration: $transition-normal) {
  animation: fadeIn $duration ease-in-out;
}

@mixin slide-up($duration: $transition-normal) {
  animation: slideUp $duration ease-out;
}

// 定义动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 文字渐变混合器
@mixin text-gradient($start-color: $primary-color, $end-color: $secondary-color) {
  background: linear-gradient(45deg, $start-color, $end-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 边框渐变混合器
@mixin border-gradient($start-color: $primary-color, $end-color: $secondary-color, $width: 1rpx) {
  border: $width solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, $start-color, $end-color) border-box;
}
