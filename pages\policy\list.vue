<template>
  <view class="policy-list-page">
    <!-- 顶部导航 -->
    <view class="page-header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="header-title">
        <text class="title-text">📋 政策文件</text>
        <text class="title-subtitle">重庆跨境融资政策文件库</text>
      </view>
      <view class="header-right">
        <text class="refresh-btn" @click="refreshList">🔄</text>
      </view>
    </view>



    <!-- 筛选标签 -->
    <view class="filter-section" v-if="categories.length > 0">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-tags">
          <view 
            class="filter-tag" 
            :class="{ active: selectedCategory === '' }"
            @click="selectCategory('')"
          >
            全部
          </view>
          <view 
            v-for="category in categories" 
            :key="category"
            class="filter-tag" 
            :class="{ active: selectedCategory === category }"
            @click="selectCategory(category)"
          >
            {{ category }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 政策列表 -->
    <view class="policy-content">
      <view v-if="loading" class="loading-section">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载政策文件...</text>
      </view>

      <view v-else-if="filteredPolicyList.length === 0" class="empty-section">
        <text class="empty-icon">📋</text>
        <text class="empty-title">暂无相关政策文件</text>
        <text class="empty-subtitle">稍后再来看看</text>
      </view>

      <view v-else class="policy-list">
        <view 
          class="policy-item" 
          v-for="policy in filteredPolicyList" 
          :key="policy.id" 
          @click="toPolicyDetail(policy.id)"
        >
          <view class="policy-item-header">
            <view class="policy-icon">📄</view>
            <view class="policy-main">
              <text class="policy-title">{{ policy.title }}</text>
              <view class="policy-meta">
                <text class="policy-category" v-if="policy.category">{{ policy.category }}</text>
                <text class="policy-date">{{ formatDate(policy.publish_date) }}</text>
              </view>
            </view>
            <view class="policy-arrow">›</view>
          </view>
          
          <view class="policy-preview" v-if="policy.summary">
            <text class="policy-summary">{{ getSummaryPreview(policy.summary) }}</text>
          </view>
          
          <view class="policy-stats">
            <view class="stat-item">
              <text class="stat-icon">👁</text>
              <text class="stat-value">{{ formatViewCount(policy.view_count) }}</text>
            </view>
            <view class="stat-item">
              <text class="stat-icon">📥</text>
              <text class="stat-value">{{ policy.download_count || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="stat-icon">⭐</text>
              <text class="stat-value">{{ policy.rating || 0 }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading" class="load-more">
        <text class="load-more-btn" @click="loadMore">加载更多</text>
      </view>
    </view>
  </view>
</template>

<script>
import { api } from '@/utils/api.js'

export default {
  data() {
    return {
      policyList: [],
      filteredPolicyList: [],
      loading: true,
      selectedCategory: '',
      categories: [],
      page: 1,
      perPage: 20,
      hasMore: true,

    }
  },

  onLoad() {
    this.loadPolicyList()
  },

  onPullDownRefresh() {
    this.refreshList()
  },

  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadPolicyList() {
      try {
        this.loading = true
        const params = {
          page: this.page,
          per_page: this.perPage
        }
        
        if (this.selectedCategory) {
          params.category = this.selectedCategory
        }
        


        const res = await api.getPolicies(params)
        console.log('政策列表API返回:', res)
        
        if (res.success && res.data) {
          const policies = res.data.items || []
          
          if (this.page === 1) {
            this.policyList = policies
          } else {
            this.policyList = [...this.policyList, ...policies]
          }
          
          this.filteredPolicyList = this.policyList
          this.hasMore = res.data.has_next || false
          
          // 提取分类
          if (this.page === 1) {
            this.extractCategories()
          }
        } else {
          console.error('API返回数据格式错误:', res)
          uni.showToast({
            title: '数据格式错误',
            icon: 'error'
          })
        }
        

        
      } catch (error) {
        console.error('加载政策列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },



    extractCategories() {
      const categories = [...new Set(this.policyList.map(policy => policy.category).filter(Boolean))]
      this.categories = categories
    },

    refreshList() {
      this.page = 1
      this.hasMore = true
      this.loadPolicyList()
    },

    loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++
        this.loadPolicyList()
      }
    },



    selectCategory(category) {
      this.selectedCategory = category
      this.page = 1
      this.hasMore = true
      this.loadPolicyList()
    },

    toPolicyDetail(id) {
      uni.navigateTo({
        url: `/pages/policy/detail?id=${id}`
      })
    },

    getSummaryPreview(summary) {
      if (!summary) return ''
      return summary.length > 120 ? summary.substring(0, 120) + '...' : summary
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) {
        return '昨天'
      } else if (diffDays <= 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    },

    formatViewCount(count) {
      if (!count) return '0'
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w'
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k'
      }
      return count.toString()
    }
  }
}
</script>

<style scoped>
.policy-list-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.title-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 4rpx;
}

.header-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.refresh-btn {
  font-size: 36rpx;
  color: #1E90FF;
}



.filter-section {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.filter-tag {
  padding: 12rpx 24rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #1E90FF;
  color: white;
}

.policy-content {
  padding: 20rpx 0;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
}

.policy-list {
  padding: 0 30rpx;
}

.policy-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.policy-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.policy-item-header {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.policy-icon {
  font-size: 24rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(30, 144, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.policy-main {
  flex: 1;
  min-width: 0;
}

.policy-title {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.policy-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.policy-category {
  font-size: 22rpx;
  color: #1E90FF;
  background: rgba(30, 144, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.policy-date {
  font-size: 22rpx;
  color: #999;
}

.policy-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
  flex-shrink: 0;
}

.policy-preview {
  margin-bottom: 16rpx;
}

.policy-summary {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

.policy-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  font-size: 24rpx;
}

.stat-value {
  font-size: 24rpx;
  color: #999;
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #e8e8e8;
}
</style> 