{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/search/index.vue?d0a3", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/search/index.vue?bf48", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/search/index.vue?732b", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/search/index.vue?266b", "uni-app:///pages/search/index.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/search/index.vue?7377", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/search/index.vue?337b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "searchKeyword", "currentType", "hasSearched", "searchResults", "totalResults", "categoryData", "currentPage", "hasMoreData", "isLoading", "totalPages", "searchTypes", "label", "value", "onLoad", "console", "methods", "performSearch", "params", "q", "type", "page", "per_page", "api", "res", "items", "newGroupedResults", "uni", "title", "icon", "groupSearchResults", "policies", "news", "faq", "interpretations", "grouped", "id", "content", "category", "publish_date", "question", "answer", "answer_date", "video_url", "switchSearchType", "loadCategoryData", "isLoadMore", "response", "newData", "duration", "getTypeIcon", "policy", "interpretation", "getTypeTitle", "gotoDetail", "url", "highlightKeyword", "truncateText", "plainText", "formatDate", "loadMore", "loadMoreSearch", "mergeSearchResults", "Object", "getVideoThumbnail", "onVideoThumbnailError"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnIA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC+Op4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MAEAC,cACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EAEAC;IACA;MACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;;MAEA;MACAC;MACA;QACAA;MACA;IACA;MACAA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;gBAIA;gBACA;kBACA;kBACA;gBACA;gBAEA;gBACA;gBAAA;gBAGAC;kBACAC;kBAAA;kBACAC;kBACAC;kBACAC;gBACA;gBAEAP;gBAAA;gBAAA,OACAQ;cAAA;gBAAAC;gBACAT;;gBAEA;gBACAU;gBACAV;gBAEAW;gBACAX;gBAEA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;gBAEAA;;gBAEA;gBACA;gBACA;gBACA;gBAEA;kBACA;kBACA;gBACA;gBAEAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAY;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MAEAT;QACA;UACA;YACAU;cACAC;cACAR;cACAS;cACAC;cACAC;YACA;YACA;UACA;YACAJ;cACAC;cACAR;cACAS;cACAC;cACAC;YACA;YACA;UACA;YACAJ;cACAC;cACAI;cACAC;cACAH;cACAI;YACA;YACA;UACA;YACAP;cACAC;cACAR;cACAS;cACAC;cACAC;cACAI;YACA;YACA;QAAA;MAEA;MAEA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;;gBAEA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACA/B;gBAEA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAEA;gBAAA;gBAGAgC;gBACA7B;kBACAG;kBACAC;gBACA;gBAAA,eAEAF;gBAAA,kCACA,iCAKA,+BAKA,8BAKA;gBAAA;cAAA;gBAdAL;gBAAA;gBAAA,OACAQ;cAAA;gBAAAwB;gBACAhC;gBAAA;cAAA;gBAGAA;gBAAA;gBAAA,OACAQ;cAAA;gBAAAwB;gBACAhC;gBAAA;cAAA;gBAGAA;gBAAA;gBAAA,OACAQ;cAAA;gBAAAwB;gBACAhC;gBAAA;cAAA;gBAGAA;gBAAA;gBAAA,OACAQ;cAAA;gBAAAwB;gBACAhC;gBAAA;cAAA;gBAIA;kBACAiC;kBACAjC;;kBAEA;kBACA;kBACA;kBAEA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;kBAEAA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;;gBAEA;gBACAY;kBACAC;kBACAC;kBACAoB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;QACAC;QACAnB;QACAC;QACAmB;MACA;MACA;IACA;IAEAC;MACA;QACAF;QACAnB;QACAC;QACAmB;MACA;MACA;IACA;IAEAE;MACA;MACA;QACA;UACAC;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;MAAA;MAGA;QACA5B;UAAA4B;QAAA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA,sCACAC,4CACAA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACAC;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;IACA;IAEAC;MACAlD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzlBA;AAAA;AAAA;AAAA;AAA2oD,CAAgB,g/CAAG,EAAC,C;;;;;;;;;;;ACA/pD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/search/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/search/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=482e85b8&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=482e85b8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"482e85b8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/search/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=482e85b8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.hasSearched\n    ? _vm.currentType === \"policy\" &&\n      _vm.searchResults.policies &&\n      _vm.searchResults.policies.length > 0\n    : null\n  var l0 =\n    _vm.hasSearched && g0\n      ? _vm.__map(_vm.searchResults.policies, function (item, __i1__) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.highlightKeyword(item.title)\n          var m1 = _vm.truncateText(item.content, 100)\n          var m2 = _vm.formatDate(item.publish_date)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var g1 = _vm.hasSearched\n    ? _vm.currentType === \"news\" &&\n      _vm.searchResults.news &&\n      _vm.searchResults.news.length > 0\n    : null\n  var l1 =\n    _vm.hasSearched && g1\n      ? _vm.__map(_vm.searchResults.news, function (item, __i2__) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.highlightKeyword(item.title)\n          var m4 = _vm.truncateText(item.content, 100)\n          var m5 = _vm.formatDate(item.publish_date)\n          return {\n            $orig: $orig,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n          }\n        })\n      : null\n  var g2 = _vm.hasSearched\n    ? _vm.currentType === \"faq\" &&\n      _vm.searchResults.faq &&\n      _vm.searchResults.faq.length > 0\n    : null\n  var l2 =\n    _vm.hasSearched && g2\n      ? _vm.__map(_vm.searchResults.faq, function (item, __i3__) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.highlightKeyword(item.question)\n          var m7 = _vm.truncateText(item.answer, 100)\n          var m8 = _vm.formatDate(item.answer_date)\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n            m8: m8,\n          }\n        })\n      : null\n  var g3 = _vm.hasSearched\n    ? _vm.currentType === \"interpretation\" &&\n      _vm.searchResults.interpretations &&\n      _vm.searchResults.interpretations.length > 0\n    : null\n  var l3 =\n    _vm.hasSearched && g3\n      ? _vm.__map(_vm.searchResults.interpretations, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m9 = _vm.getVideoThumbnail(item) && !item.thumbnail_error\n          var m10 = m9 ? _vm.getVideoThumbnail(item) : null\n          var m11 = _vm.highlightKeyword(item.title)\n          return {\n            $orig: $orig,\n            m9: m9,\n            m10: m10,\n            m11: m11,\n          }\n        })\n      : null\n  var m12 = !_vm.hasSearched ? _vm.getTypeIcon(_vm.currentType) : null\n  var m13 = !_vm.hasSearched ? _vm.getTypeTitle(_vm.currentType) : null\n  var g4 = !_vm.hasSearched ? _vm.categoryData.length : null\n  var l4 =\n    !_vm.hasSearched && g4 > 0\n      ? _vm.__map(_vm.categoryData, function (item, __i4__) {\n          var $orig = _vm.__get_orig(item)\n          var m14 = _vm.truncateText(item.content || item.answer, 120)\n          var m15 = _vm.formatDate(item.publish_date || item.answer_date)\n          return {\n            $orig: $orig,\n            m14: m14,\n            m15: m15,\n          }\n        })\n      : null\n  var g5 =\n    !_vm.hasSearched && g4 > 0\n      ? !_vm.hasMoreData && _vm.categoryData.length > 0\n      : null\n  var m16 =\n    !_vm.hasSearched && !(g4 > 0) ? _vm.getTypeTitle(_vm.currentType) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        l1: l1,\n        g2: g2,\n        l2: l2,\n        g3: g3,\n        l3: l3,\n        m12: m12,\n        m13: m13,\n        g4: g4,\n        l4: l4,\n        g5: g5,\n        m16: m16,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"search-page\">\n    <!-- 搜索头部 -->\n    <view class=\"search-header\">\n      <view class=\"search-box\">\n        <input \n          v-model=\"searchKeyword\" \n          placeholder=\"搜索政策文件、问题、新闻、政策解读\"\n          class=\"search-input\"\n          @confirm=\"performSearch()\"\n          focus\n        />\n        <view @click=\"performSearch()\" class=\"search-btn\">\n          <image src=\"/static/icons/search.svg\" class=\"search-icon\" mode=\"aspectFit\"></image>\n        </view>\n      </view>\n    </view>\n\n    <!-- 搜索类型选择 -->\n    <view class=\"search-types\">\n      <view \n        v-for=\"type in searchTypes\" \n        :key=\"type.value\"\n        @click=\"switchSearchType(type.value)\"\n        class=\"type-btn\"\n        :class=\"{ active: currentType === type.value }\"\n      >\n        {{ type.label }}\n      </view>\n    </view>\n\n    <!-- 搜索结果 -->\n    <view class=\"search-results\" v-if=\"hasSearched\">\n      <!-- 结果统计 -->\n      <view class=\"result-summary\" v-if=\"totalResults > 0\">\n        <text>找到 {{ totalResults }} 条相关结果</text>\n      </view>\n\n      <!-- 政策文件结果 -->\n      <view v-if=\"currentType === 'policy' && searchResults.policies && searchResults.policies.length > 0\" class=\"result-section\">\n        <view class=\"section-title\">\n          <text class=\"icon\">📋</text>\n          <text>政策文件</text>\n        </view>\n        <view class=\"result-list\">\n          <view \n            v-for=\"item in searchResults.policies\" \n            :key=\"item.id\"\n            class=\"result-item\"\n            @click=\"gotoDetail('policy', item.id)\"\n          >\n            <view class=\"item-title\" v-html=\"highlightKeyword(item.title)\"></view>\n            <view class=\"item-content\">{{ truncateText(item.content, 100) }}</view>\n            <view class=\"item-meta\">\n              <text class=\"category\">{{ item.category }}</text>\n              <view class=\"meta-right\">\n                <text class=\"date\">👍{{ item.like_count || 0 }}</text>\n                <text class=\"date\">👁️{{ item.view_count || 0 }}</text>\n                <text class=\"date\">{{ formatDate(item.publish_date) }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 新闻结果 -->\n      <view v-if=\"currentType === 'news' && searchResults.news && searchResults.news.length > 0\" class=\"result-section\">\n        <view class=\"section-title\">\n          <text class=\"icon\">📰</text>\n          <text>要闻动态</text>\n        </view>\n        <view class=\"result-list\">\n          <view \n            v-for=\"item in searchResults.news\" \n            :key=\"item.id\"\n            class=\"result-item\"\n            @click=\"gotoDetail('news', item.id)\"\n          >\n            <view class=\"item-title\" v-html=\"highlightKeyword(item.title)\"></view>\n            <view class=\"item-content\">{{ truncateText(item.content, 100) }}</view>\n            <view class=\"item-meta\">\n              <text class=\"category\">{{ item.category }}</text>\n              <view class=\"meta-right\">\n                <text class=\"date\">👍{{ item.like_count || 0 }}</text>\n                <text class=\"date\">👁️{{ item.view_count || 0 }}</text>\n                <text class=\"date\">{{ formatDate(item.publish_date) }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- FAQ结果 -->\n      <view v-if=\"currentType === 'faq' && searchResults.faq && searchResults.faq.length > 0\" class=\"result-section\">\n        <view class=\"section-title\">\n          <text class=\"icon\">❓</text>\n          <text>热门问题</text>\n        </view>\n        <view class=\"result-list\">\n          <view \n            v-for=\"item in searchResults.faq\" \n            :key=\"item.id\"\n            class=\"result-item\"\n            @click=\"gotoDetail('faq', item.id)\"\n          >\n            <view class=\"item-title\" v-html=\"highlightKeyword(item.question)\"></view>\n            <view class=\"item-content\">{{ truncateText(item.answer, 100) }}</view>\n            <view class=\"item-meta\">\n              <text class=\"category\">{{ item.category }}</text>\n              <view class=\"meta-right\">\n                <text class=\"date\">👍{{ item.like_count || 0 }}</text>\n                <text class=\"date\">👁️{{ item.view_count || 0 }}</text>\n                <text class=\"date\">{{ formatDate(item.answer_date) }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 政策解读结果 -->\n      <view v-if=\"currentType === 'interpretation' && searchResults.interpretations && searchResults.interpretations.length > 0\" class=\"result-section\">\n        <view class=\"section-title\">\n          <text class=\"icon\">🎥</text>\n          <text>政策解读</text>\n        </view>\n        <view class=\"interpretation-grid\">\n          <view \n            class=\"interpretation-item\" \n            v-for=\"(item, index) in searchResults.interpretations\" \n            :key=\"item.id\"\n            @click=\"gotoDetail('interpretation', item.id)\"\n          >\n            <view class=\"interpretation-cover\">\n              <image \n                v-if=\"getVideoThumbnail(item) && !item.thumbnail_error\" \n                :src=\"getVideoThumbnail(item)\" \n                class=\"interpretation-image\"\n                mode=\"aspectFill\"\n                @error=\"onVideoThumbnailError(item)\"\n              />\n              <view \n                v-else \n                class=\"interpretation-image\"\n                :class=\"'interpretation-bg-' + (index % 6 + 1)\"\n              ></view>\n              <view v-if=\"item.video_url\" class=\"play-icon\">\n                <text>▶</text>\n              </view>\n              <view v-else class=\"text-icon\">\n                <text>📄</text>\n              </view>\n            </view>\n            <view class=\"interpretation-title\" v-html=\"highlightKeyword(item.title)\"></view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 无结果提示 -->\n      <view v-if=\"totalResults === 0\" class=\"no-results\">\n        <text class=\"no-results-icon\">🔍</text>\n        <text class=\"no-results-text\">没有找到相关内容</text>\n        <text class=\"no-results-tip\">请尝试使用其他关键词</text>\n      </view>\n      \n      <!-- 搜索结果的加载更多 -->\n      <view class=\"search-load-more\" v-if=\"totalResults > 0\">\n        <!-- 加载更多按钮 -->\n        <view class=\"load-more\" v-if=\"hasMoreData && !isLoading\" @click=\"loadMoreSearch\">\n          <text class=\"load-more-text\">加载更多搜索结果</text>\n          <text class=\"load-more-icon\">↓</text>\n        </view>\n        \n        <!-- 加载中状态 -->\n        <view class=\"loading-more\" v-if=\"isLoading\">\n          <text class=\"loading-more-text\">正在搜索...</text>\n          <text class=\"loading-more-icon\">⏳</text>\n        </view>\n        \n        <!-- 没有更多数据 -->\n        <view class=\"no-more\" v-if=\"!hasMoreData && totalResults > 0\">\n          <text class=\"no-more-text\">搜索结果已全部显示</text>\n          <text class=\"no-more-icon\">🔍</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 类型列表展示 -->\n    <view class=\"category-list\" v-if=\"!hasSearched\">\n      <view class=\"list-header\">\n        <text class=\"icon\">{{ getTypeIcon(currentType) }}</text>\n        <text>{{ getTypeTitle(currentType) }}</text>\n      </view>\n      <view class=\"category-items\" v-if=\"categoryData.length > 0\">\n        <view \n          v-for=\"item in categoryData\" \n          :key=\"item.id\"\n          class=\"category-item\"\n          @click=\"gotoDetail(currentType, item.id)\"\n        >\n          <view class=\"item-title\">{{ item.title || item.question }}</view>\n          <view class=\"item-content\">{{ truncateText(item.content || item.answer, 120) }}</view>\n          <view class=\"item-meta\">\n            <text class=\"category\" v-if=\"item.category\">{{ item.category }}</text>\n            <view class=\"meta-right\">\n                <text class=\"date\">👍{{ item.like_count || 0 }}</text>\n                <text class=\"date\">👁️{{ item.view_count || 0 }}</text>\n                <text class=\"date\">{{ formatDate(item.publish_date || item.answer_date) }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 加载更多按钮 -->\n        <view class=\"load-more\" v-if=\"hasMoreData && !isLoading\" @click=\"loadMore\">\n          <text class=\"load-more-text\">加载更多</text>\n          <text class=\"load-more-icon\">↓</text>\n        </view>\n        \n        <!-- 加载中状态 -->\n        <view class=\"loading-more\" v-if=\"isLoading\">\n          <text class=\"loading-more-text\">正在加载...</text>\n          <text class=\"loading-more-icon\">⏳</text>\n        </view>\n        \n        <!-- 没有更多数据 -->\n        <view class=\"no-more\" v-if=\"!hasMoreData && categoryData.length > 0\">\n          <text class=\"no-more-text\">没有更多数据了</text>\n          <text class=\"no-more-icon\">📝</text>\n        </view>\n      </view>\n      <!-- 加载中状态 -->\n      <view class=\"loading-state\" v-else>\n        <view class=\"loading-icon\">⏳</view>\n        <view class=\"loading-text\">正在加载{{ getTypeTitle(currentType) }}...</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport api from '@/utils/api.js'\n\nexport default {\n  data() {\n    return {\n      searchKeyword: '',\n      currentType: 'policy',\n      hasSearched: false,\n      searchResults: {},\n      totalResults: 0,\n      categoryData: [], // 类型数据\n      \n      // 分页相关状态\n      currentPage: 1,\n      hasMoreData: true,\n      isLoading: false,\n      totalPages: 1,\n\n      searchTypes: [\n        { label: '政策', value: 'policy' },\n        { label: '新闻', value: 'news' },\n        { label: '问题', value: 'faq' },\n        { label: '解读', value: 'interpretation' }\n      ]\n    }\n  },\n\n  onLoad(options) {\n    try {\n      if (options && options.type) {\n        const validTypes = this.searchTypes.map(t => t.value)\n        if (validTypes.includes(options.type)) {\n          this.currentType = options.type\n        }\n      }\n\n      // 页面加载时自动加载当前类型的数据\n      console.log('搜索页面加载完成，当前类型:', this.currentType)\n      this.loadCategoryData(this.currentType).catch(err => {\n        console.error('分类数据加载失败:', err)\n      })\n    } catch (err) {\n      console.error('onLoad错误:', err)\n    }\n  },\n\n  methods: {\n    async performSearch() {\n      if (!this.searchKeyword.trim()) {\n        // 当搜索词为空时，重置为分类列表视图\n        this.hasSearched = false\n        this.searchResults = {}\n        this.totalResults = 0\n        // 加载当前类型的分类数据\n        await this.loadCategoryData(this.currentType, false)\n        return\n      }\n\n      // 如果是新搜索（不是加载更多），重置页码\n      if (!this.isLoading) {\n        this.currentPage = 1\n        this.hasMoreData = true\n      }\n\n      this.hasSearched = true\n      this.isLoading = true\n\n      try {\n        const params = {\n          q: this.searchKeyword,  // 根据API文档使用q参数\n          type: 'all',\n          page: this.currentPage,\n          per_page: 20\n        }\n\n        console.log('搜索参数:', params)\n        const res = await api.search(params)\n        console.log('搜索响应:', res)\n        \n        // 根据API文档，搜索结果在data.items中，需要按类型分组\n        const items = res.data?.items || []\n        console.log('搜索原始数据:', items)\n        \n        const newGroupedResults = this.groupSearchResults(items)\n        console.log('分组后的新搜索结果:', newGroupedResults)\n        \n        if (this.currentPage === 1) {\n          // 第一页，替换结果\n          this.searchResults = newGroupedResults\n        } else {\n          // 后续页，合并结果\n          this.mergeSearchResults(newGroupedResults)\n        }\n        \n        console.log('最终搜索结果:', this.searchResults)\n        \n        // 更新分页信息\n        this.totalResults = res.data?.total || 0\n        this.totalPages = res.data?.pages || 1\n        this.hasMoreData = this.currentPage < this.totalPages\n        \n        if (this.currentPage === 1) {\n          // 首次搜索后，默认选中第一个tab\n          this.currentType = 'policy';\n        }\n        \n        console.log('搜索结果总数:', this.totalResults, '当前页:', this.currentPage, '总页数:', this.totalPages)\n\n      } catch (error) {\n        console.error('搜索失败:', error)\n        uni.showToast({\n          title: '搜索失败',\n          icon: 'none'\n        })\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    groupSearchResults(items) {\n      const grouped = {\n        policies: [],\n        news: [],\n        faq: [],\n        interpretations: []\n      }\n      \n      items.forEach(item => {\n        switch (item.type) {\n          case 'policy':\n            grouped.policies.push({\n              id: item.id,\n              title: item.title,\n              content: item.content,\n              category: item.category || '政策文件',\n              publish_date: item.publish_date || item.created_at\n            })\n            break\n          case 'news':\n            grouped.news.push({\n              id: item.id,\n              title: item.title,\n              content: item.content,\n              category: item.category || '要闻动态',\n              publish_date: item.publish_date || item.created_at\n            })\n            break\n          case 'faq':\n            grouped.faq.push({\n              id: item.id,\n              question: item.title,\n              answer: item.content,\n              category: item.category || '常见问题',\n              answer_date: item.publish_date || item.created_at\n            })\n            break\n          case 'interpretation':\n            grouped.interpretations.push({\n              id: item.id,\n              title: item.title,\n              content: item.content,\n              category: item.category || '政策解读',\n              publish_date: item.publish_date || item.created_at,\n              video_url: item.video_url\n            })\n            break\n        }\n      })\n      \n      return grouped\n    },\n\n    async switchSearchType(type) {\n      this.currentType = type\n      \n      // 只有在非搜索结果视图下，切换tab才重新加载分类列表\n      if (!this.hasSearched) {\n        this.hasSearched = false\n        await this.loadCategoryData(type, false)\n      }\n    },\n\n    async loadCategoryData(type, isLoadMore = false) {\n      console.log('开始加载分类数据，类型:', type, '是否加载更多:', isLoadMore)\n      \n      if (!isLoadMore) {\n        // 重置分页状态\n        this.currentPage = 1\n        this.hasMoreData = true\n        this.categoryData = []\n      }\n      \n      this.isLoading = true\n      \n      try {\n        let response = null\n        const params = { \n          page: this.currentPage, \n          per_page: 10 \n        }\n        \n        switch (type) {\n          case 'policy':\n            console.log('正在请求政策数据...', params)\n            response = await api.getPolicies(params)\n            console.log('政策数据响应:', response)\n            break\n          case 'news':\n            console.log('正在请求新闻数据...', params)\n            response = await api.getNews(params)\n            console.log('新闻数据响应:', response)\n            break\n          case 'faq':\n            console.log('正在请求FAQ数据...', params)\n            response = await api.getFaqs(params)\n            console.log('FAQ数据响应:', response)\n            break\n          case 'interpretation':\n            console.log('正在请求政策解读数据...', params)\n            response = await api.getInterpretations(params)\n            console.log('政策解读数据响应:', response)\n            break\n        }\n        \n        if (response && response.data) {\n          const newData = response.data.items || response.data.data || []\n          console.log('解析后的新数据:', newData)\n          \n          // 更新分页信息\n          this.totalPages = response.data.pages || 1\n          this.hasMoreData = this.currentPage < this.totalPages\n          \n          if (isLoadMore) {\n            // 追加数据\n            this.categoryData = [...this.categoryData, ...newData]\n          } else {\n            // 替换数据\n            this.categoryData = newData\n          }\n          \n          console.log('当前页:', this.currentPage, '总页数:', this.totalPages, '是否有更多:', this.hasMoreData)\n          console.log('最终设置的分类数据:', this.categoryData)\n        }\n      } catch (error) {\n        console.error('加载分类数据失败:', error)\n        \n        // 显示错误提示\n        uni.showToast({\n          title: `加载${this.getTypeTitle(type)}失败`,\n          icon: 'none',\n          duration: 2000\n        })\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    getTypeIcon(type) {\n      const icons = {\n        policy: '📋',\n        news: '📰',\n        faq: '❓',\n        interpretation: '🎥'\n      }\n      return icons[type] || '📄'\n    },\n\n    getTypeTitle(type) {\n      const titles = {\n        policy: '政策文件',\n        news: '要闻动态',\n        faq: '常见问题',\n        interpretation: '政策解读'\n      }\n      return titles[type] || '全部内容'\n    },\n\n    gotoDetail(type, id) {\n      let url = ''\n      switch (type) {\n        case 'policy':\n          url = `/pages/policy/detail?id=${id}`\n          break\n        case 'news':\n          url = `/pages/news/detail?id=${id}`\n          break\n        case 'faq':\n          url = `/pages/faq/detail?id=${id}`\n          break\n        case 'interpretation':\n          url = `/pages/interpretation/detail?id=${id}`\n          break\n      }\n      \n      if (url) {\n        uni.navigateTo({ url })\n      }\n    },\n\n    highlightKeyword(text) {\n      if (!this.searchKeyword || !text) return text\n      const regex = new RegExp(this.searchKeyword.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), 'gi');\n      return text.replace(regex, `<span style=\"color: #1E90FF; font-weight: bold;\">${this.searchKeyword}</span>`);\n    },\n\n    truncateText(text, maxLength) {\n      if (!text) return ''\n      // 移除HTML标签\n      const plainText = text.replace(/<[^>]*>/g, '')\n      return plainText.length > maxLength \n        ? plainText.substring(0, maxLength) + '...'\n        : plainText\n    },\n\n    formatDate(dateStr) {\n      if (!dateStr) return ''\n      return new Date(dateStr).toLocaleDateString()\n    },\n\n    // 新增：加载更多方法\n    async loadMore() {\n      if (this.isLoading || !this.hasMoreData) return\n      \n      this.currentPage++\n      await this.loadCategoryData(this.currentType, true)\n    },\n\n    async loadMoreSearch() {\n      if (this.isLoading || !this.hasMoreData) return\n      \n      this.currentPage++\n      await this.performSearch()\n    },\n\n    // 新增：合并搜索结果\n    mergeSearchResults(newResults) {\n      Object.keys(newResults).forEach(key => {\n        if (this.searchResults[key]) {\n          this.searchResults[key] = [...this.searchResults[key], ...newResults[key]]\n        } else {\n          this.searchResults[key] = newResults[key]\n        }\n      })\n    },\n    \n    getVideoThumbnail(item) {\n      if (item && (item.thumbnail || item.cover_image)) {\n        return item.thumbnail || item.cover_image\n      }\n      if (item && item.video_url) {\n        if (item.video_url.includes('example.com')) {\n          return item.video_url.replace('.mp4', '_thumb.jpg')\n        }\n      }\n      return ''\n    },\n\n    onVideoThumbnailError(item) {\n      console.log(`视频缩略图加载失败:`, item)\n      this.$set(item, 'thumbnail_error', true)\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.search-page {\n  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);\n  min-height: 100vh;\n  position: relative;\n}\n\n/* 顶部重庆山城风格背景 */\n.search-page::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 200rpx;\n  background: linear-gradient(180deg, \n    #1E90FF 0%, \n    #4A90E2 30%, \n    #6BA3E8 60%, \n    rgba(107, 163, 232, 0.6) 80%, \n    rgba(138, 180, 240, 0.3) 90%, \n    transparent 100%\n  );\n  z-index: 1;\n}\n\n/* 重庆山城剪影装饰 */\n.search-page::after {\n  content: '';\n  position: absolute;\n  top: 140rpx;\n  left: 0;\n  right: 0;\n  height: 80rpx;\n  background: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E\") repeat-x;\n  background-size: 1200rpx 80rpx;\n  z-index: 2;\n  opacity: 0.7;\n}\n\n.search-header {\n  background: transparent;\n  padding: 25rpx 30rpx 15rpx;\n  position: relative;\n  z-index: 2;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.search-input {\n  flex: 1;\n  height: 50rpx;\n  padding: 0 25rpx;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 35rpx;\n  font-size: 28rpx;\n  border: none;\n  backdrop-filter: blur(10rpx);\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n.search-btn {\n  width: 70rpx;\n  height: 70rpx;\n  background: rgba(255, 255, 255, 0.2);\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  border-radius: 35rpx;\n  backdrop-filter: blur(10rpx);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.search-btn:active {\n  transform: scale(0.95);\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.search-icon {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.search-types {\n  display: flex;\n  gap: 20rpx;\n  padding: 15rpx 30rpx 25rpx;\n  background: transparent;\n  position: relative;\n  z-index: 3;\n  overflow-x: auto;\n  white-space: nowrap;\n  justify-content: flex-start;\n  \n  /* 显示滚动条 */\n  &::-webkit-scrollbar {\n    height: 6rpx;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.2);\n    border-radius: 3rpx;\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: rgba(255, 255, 255, 0.4);\n    border-radius: 3rpx;\n  }\n  \n  &::-webkit-scrollbar-thumb:hover {\n    background: rgba(255, 255, 255, 0.6);\n  }\n}\n\n.type-btn {\n  padding: 16rpx 32rpx;\n  background: rgba(255, 255, 255, 0.95);\n  color: #666;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(15rpx);\n  flex-shrink: 0;\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  min-width: 100rpx;\n  text-align: center;\n  \n  &.active {\n    background: linear-gradient(135deg, #1E90FF, #4A90E2);\n    color: white;\n    transform: scale(1.05);\n    box-shadow: 0 4rpx 20rpx rgba(30, 144, 255, 0.3);\n  }\n  \n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.search-results {\n  padding: 30rpx;\n  margin-top: 20rpx;\n}\n\n.result-summary {\n  margin-bottom: 30rpx;\n  padding: 20rpx 30rpx;\n  background: rgba(30, 144, 255, 0.1);\n  border-radius: 16rpx;\n  font-size: 28rpx;\n  color: #1E90FF;\n  font-weight: 600;\n  text-align: center;\n}\n\n.result-section {\n  margin-bottom: 50rpx;\n}\n\n.section-title {\n  display: flex;\n  align-items: center;\n  gap: 15rpx;\n  font-size: 34rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 25rpx;\n  padding: 20rpx 30rpx;\n  background: linear-gradient(135deg, rgba(30, 144, 255, 0.1), rgba(74, 144, 226, 0.1));\n  border-radius: 16rpx;\n  border-left: 6rpx solid #1E90FF;\n  \n  .icon {\n    font-size: 38rpx;\n  }\n}\n\n.result-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.result-item {\n  background: white;\n  padding: 35rpx;\n  border-radius: 20rpx;\n  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  border-left: 4rpx solid transparent;\n  \n  &:active {\n    transform: scale(0.98);\n    border-left-color: #1E90FF;\n    box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);\n  }\n}\n\n.item-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 15rpx;\n  line-height: 1.4;\n}\n\n.item-content {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 20rpx;\n}\n\n.item-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.meta-right {\n  display: flex;\n  gap: 20rpx;\n  align-items: center;\n}\n\n.category {\n  background: linear-gradient(135deg, #1E90FF, #4A90E2);\n  color: white;\n  padding: 8rpx 16rpx;\n  border-radius: 16rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n.date {\n  color: #999;\n  font-size: 26rpx;\n}\n\n.no-results {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 100rpx 0;\n  text-align: center;\n}\n\n.no-results-icon {\n  font-size: 120rpx;\n  margin-bottom: 30rpx;\n  opacity: 0.3;\n}\n\n.no-results-text {\n  font-size: 32rpx;\n  color: #333;\n  margin-bottom: 16rpx;\n}\n\n.no-results-tip {\n  font-size: 26rpx;\n  color: #999;\n}\n\n.search-load-more {\n  padding: 30rpx;\n  margin: 20rpx 0;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10rpx;\n  padding: 30rpx;\n  margin: 20rpx 0;\n  background: linear-gradient(135deg, #1E90FF, #4A90E2);\n  color: white;\n  border-radius: 50rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);\n  transition: all 0.3s ease;\n  \n  &:active {\n    transform: scale(0.98);\n    box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);\n  }\n}\n\n.load-more-icon {\n  font-size: 24rpx;\n  animation: bounce 2s infinite;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10rpx;\n  padding: 30rpx;\n  margin: 20rpx 0;\n  background: rgba(255, 255, 255, 0.9);\n  color: #666;\n  border-radius: 50rpx;\n  font-size: 28rpx;\n}\n\n.loading-more-icon {\n  animation: spin 1s linear infinite;\n}\n\n.no-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10rpx;\n  padding: 30rpx;\n  margin: 20rpx 0;\n  background: rgba(255, 255, 255, 0.9);\n  color: #999;\n  border-radius: 50rpx;\n  font-size: 26rpx;\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-6rpx);\n  }\n  60% {\n    transform: translateY(-3rpx);\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.category-list {\n  padding: 30rpx;\n  margin-top: 20rpx;\n}\n\n.list-header {\n  display: flex;\n  align-items: center;\n  gap: 15rpx;\n  font-size: 34rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 25rpx;\n}\n\n.category-items {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.category-item {\n  background: white;\n  padding: 30rpx;\n  border-radius: 20rpx;\n  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  border-left: 4rpx solid transparent;\n  min-height: 180rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  \n  &:active {\n    transform: scale(0.98);\n    border-left-color: #1E90FF;\n    box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);\n  }\n}\n\n.item-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 12rpx;\n  line-height: 1.4;\n  \n  /* 标题最多显示2行 */\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-height: 89rpx; /* 32rpx * 1.4 * 2 ≈ 89rpx */\n}\n\n.item-content {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 15rpx;\n  flex: 1;\n  \n  /* 内容最多显示3行 */\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-height: 134rpx; /* 28rpx * 1.6 * 3 ≈ 134rpx */\n}\n\n.item-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.meta-right {\n  display: flex;\n  gap: 20rpx;\n  align-items: center;\n}\n\n.category {\n  background: linear-gradient(135deg, #1E90FF, #4A90E2);\n  color: white;\n  padding: 8rpx 16rpx;\n  border-radius: 16rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n.date {\n  color: #999;\n  font-size: 26rpx;\n}\n\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n  text-align: center;\n}\n\n.loading-icon {\n  font-size: 120rpx;\n  margin-bottom: 30rpx;\n  opacity: 0.3;\n}\n\n.loading-text {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 600;\n}\n\n.load-more-text {\n  font-size: 28rpx;\n  font-weight: 600;\n}\n\n/* 新增：政策解读网格样式 */\n.interpretation-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n  margin-top: 20rpx;\n}\n\n.interpretation-item {\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  display: flex;\n  flex-direction: column;\n}\n\n.interpretation-cover {\n  position: relative;\n  width: 100%;\n  padding-top: 56.25%; /* 16:9 宽高比 */\n}\n\n.interpretation-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: #f0f0f0;\n}\n\n.play-icon {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80rpx;\n  height: 80rpx;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 32rpx;\n  pointer-events: none;\n}\n\n.text-icon {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80rpx;\n  height: 80rpx;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 40rpx;\n  pointer-events: none;\n}\n\n.interpretation-title {\n  padding: 20rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  line-height: 1.4;\n  flex: 1; /* 确保标题占满剩余空间 */\n}\n\n/* 渐变色背景占位符 */\n.interpretation-bg-1 { background: linear-gradient(135deg, #FF9A8B 0%, #FF6A88 100%); }\n.interpretation-bg-2 { background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%); }\n.interpretation-bg-3 { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); }\n.interpretation-bg-4 { background: linear-gradient(135deg, #fccb90 0%, #d57eeb 100%); }\n.interpretation-bg-5 { background: linear-gradient(135deg, #5ee7df 0%, #b490ca 100%); }\n.interpretation-bg-6 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=482e85b8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=482e85b8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051455\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}