{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/detail.vue?0c02", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/detail.vue?c9a4", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/detail.vue?70e2", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/detail.vue?fec9", "uni-app:///pages/interpretation/detail.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/detail.vue?8e52", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/detail.vue?77e8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "interpretationDetail", "interpretationId", "isLiked", "isCollected", "videoPoster", "onLoad", "methods", "loadInterpretationDetail", "api", "res", "console", "uni", "title", "icon", "checkStatus", "token", "params", "item_type", "item_id", "Promise", "actions", "likeRes", "collectRes", "recordView", "recordVideoPlay", "toggleLike", "action", "toggleCollect", "openAttachment", "url", "success", "filePath", "showMenu", "fail", "promptLogin", "content", "onShareAppMessage", "path", "imageUrl", "formatDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC6Dr4B;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACAC,aACAX;kBAAAY;gBAAA,KACAZ,2CACA;cAAA;gBAAA;gBAAA;gBAHAa;gBAAAC;gBAKA;kBACA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAP;kBACAS;kBACAC;gBACA;cAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAR;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAc;MACA;MACAd;IACA;IAEAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAV;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAP;kBACAkB;kBACAT;kBACAC;gBACA;cAAA;gBAJAT;gBAMA;gBACA;gBAEAE;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAZ;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAP;kBACAS;kBACAC;gBACA;cAAA;gBAHAT;gBAKA;gBACA;gBAEAE;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAkB;MACA;MACAjB;QAAAC;MAAA;MACAD;QACAkB;QACAC;UACA;UACAnB;YACAoB;YACAC;YACAF;cACAnB;YACA;YACAsB;cACAtB;cACAA;gBACAC;gBACAC;cACA;cACAH;YACA;UACA;QACA;QACAuB;UACAtB;UACAA;YACAC;YACAC;UACA;UACAH;QACA;MACA;IACA;IAEAwB;MACAvB;QACAC;QACAuB;QACAL;UACA;YACAnB;cACAkB;YACA;UACA;QACA;MACA;IACA;IAEAO;MACA;QACAxB;QACAyB;QACAC;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrQA;AAAA;AAAA;AAAA;AAA4oD,CAAgB,i/CAAG,EAAC,C;;;;;;;;;;;ACAhqD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/interpretation/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/interpretation/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=0c55e0e2&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=0c55e0e2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c55e0e2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/interpretation/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=0c55e0e2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.interpretationDetail\n    ? _vm.formatDate(_vm.interpretationDetail.publish_date)\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"interpretation-page\">\r\n    <view v-if=\"interpretationDetail\">\r\n      \r\n      <!-- 视频播放器 -->\r\n      <view v-if=\"interpretationDetail.video_url\" class=\"video-player-section\">\r\n        <video \r\n          :src=\"interpretationDetail.video_url\"\r\n          :poster=\"videoPoster\"\r\n          controls\r\n          class=\"video-player\"\r\n          @play=\"recordVideoPlay\"\r\n        ></video>\r\n      </view>\r\n\r\n      <!-- 内容信息卡片 -->\r\n      <view class=\"info-card\">\r\n        <view class=\"title\">{{ interpretationDetail.title }}</view>\r\n        <view class=\"meta-info\">\r\n          <text>发布时间：{{ formatDate(interpretationDetail.publish_date) }}</text>\r\n          <text>观看：{{ interpretationDetail.view_count || 0 }}次</text>\r\n        </view>\r\n        \r\n        <view v-if=\"interpretationDetail.content\" class=\"content-body\">\r\n          <rich-text :nodes=\"interpretationDetail.content\"></rich-text>\r\n        </view>\r\n\r\n        <view v-if=\"interpretationDetail.attachment_url\" class=\"attachment-section\">\r\n          <view class=\"attachment-title\">附件：</view>\r\n          <view class=\"attachment-item\" @click=\"openAttachment\">\r\n            <text class=\"attachment-icon\">📎</text>\r\n            <text class=\"attachment-name\">{{ interpretationDetail.attachment_name || '点击查看附件' }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 底部操作栏 -->\r\n      <view class=\"action-bar-footer\">\r\n        <button @click=\"toggleLike\" class=\"action-btn\" :class=\"{ active: isLiked }\">\r\n          <text class=\"icon\">👍</text>\r\n          <text class=\"action-text\">{{ interpretationDetail.like_count > 0 ? interpretationDetail.like_count : '点赞' }}</text>\r\n        </button>\r\n        <button @click=\"toggleCollect\" class=\"action-btn\" :class=\"{ active: isCollected }\">\r\n          <text class=\"icon\">{{ isCollected ? '⭐' : '☆' }}</text>\r\n          <text class=\"action-text\">{{ interpretationDetail.collect_count > 0 ? interpretationDetail.collect_count : '收藏' }}</text>\r\n        </button>\r\n        <button open-type=\"share\" class=\"action-btn\">\r\n          <text class=\"icon\">📤</text>\r\n          <text class=\"action-text\">{{ interpretationDetail.forward_count > 0 ? interpretationDetail.forward_count : '分享' }}</text>\r\n        </button>\r\n      </view>\r\n      \r\n    </view>\r\n\r\n    <view v-else class=\"loading\">\r\n      <text>加载中...</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/utils/api.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      interpretationDetail: null,\r\n      interpretationId: null,\r\n      isLiked: false,\r\n      isCollected: false,\r\n      videoPoster: '' // 可设置一个默认封面\r\n    }\r\n  },\r\n\r\n  onLoad(options) {\r\n    this.interpretationId = options.id\r\n    if (this.interpretationId) {\r\n      this.loadInterpretationDetail()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    async loadInterpretationDetail() {\r\n      try {\r\n        const res = await api.getInterpretationDetail(this.interpretationId)\r\n        this.interpretationDetail = res.data\r\n        this.videoPoster = this.interpretationDetail.cover_img || '/static/images/video-poster.jpg';\r\n\r\n        // 详情加载成功后\r\n        this.recordView()\r\n        this.checkStatus()\r\n      } catch (error) {\r\n        console.error('加载政策解读详情失败:', error)\r\n        uni.showToast({\r\n          title: '加载失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    async checkStatus() {\r\n      const token = uni.getStorageSync('token');\r\n      if (!token) return;\r\n\r\n      try {\r\n        const params = {\r\n          item_type: 'interpretation',\r\n          item_id: parseInt(this.interpretationId)\r\n        };\r\n        const [likeRes, collectRes] = await Promise.all([\r\n          api.checkInteractionStatus({ ...params, actions: ['like'] }),\r\n          api.checkCollectionStatus(params)\r\n        ]);\r\n\r\n        if (likeRes.data.like) {\r\n          this.isLiked = true;\r\n        }\r\n        if (collectRes.data.is_collected) {\r\n          this.isCollected = true;\r\n        }\r\n      } catch (error) {\r\n        console.error('检查状态失败:', error);\r\n      }\r\n    },\r\n\r\n    async recordView() {\r\n      const token = uni.getStorageSync('token');\r\n      if (!token) {\r\n        return; // 未登录，不记录\r\n      }\r\n      try {\r\n        await api.recordView({\r\n          item_type: 'interpretation',\r\n          item_id: parseInt(this.interpretationId)\r\n        });\r\n        if (this.interpretationDetail) {\r\n          this.interpretationDetail.view_count++;\r\n        }\r\n      } catch (error) {\r\n        console.error('记录浏览失败:', error);\r\n      }\r\n    },\r\n\r\n    recordVideoPlay() {\r\n      // 可以在此添加更详细的视频播放记录逻辑\r\n      console.log('视频开始播放');\r\n    },\r\n\r\n    async toggleLike() {\r\n      const token = uni.getStorageSync('token');\r\n      if (!token) {\r\n        this.promptLogin('点赞');\r\n        return;\r\n      }\r\n      try {\r\n        const res = await api.toggleInteraction({\r\n          action: 'like',\r\n          item_type: 'interpretation',\r\n          item_id: parseInt(this.interpretationId)\r\n        });\r\n        \r\n        this.isLiked = res.data.action === 'added';\r\n        this.interpretationDetail.like_count = res.data.current_count;\r\n        \r\n        uni.showToast({\r\n          title: res.data.message,\r\n          icon: 'none'\r\n        });\r\n      } catch (error) {\r\n        console.error('点赞操作失败:', error);\r\n      }\r\n    },\r\n\r\n    async toggleCollect() {\r\n      const token = uni.getStorageSync('token');\r\n      if (!token) {\r\n        this.promptLogin('收藏');\r\n        return;\r\n      }\r\n      try {\r\n        const res = await api.toggleCollection({\r\n          item_type: 'interpretation',\r\n          item_id: parseInt(this.interpretationId)\r\n        });\r\n        \r\n        this.isCollected = res.data.action === 'added';\r\n        this.interpretationDetail.collect_count = res.data.current_collect_count;\r\n        \r\n        uni.showToast({\r\n          title: res.data.message,\r\n          icon: 'none'\r\n        });\r\n      } catch (error) {\r\n        console.error('收藏操作失败:', error);\r\n      }\r\n    },\r\n    \r\n    openAttachment() {\r\n      if (!this.interpretationDetail.attachment_url) return;\r\n      uni.showLoading({ title: '正在打开附件' });\r\n      uni.downloadFile({\r\n        url: this.interpretationDetail.attachment_url,\r\n        success: (res) => {\r\n          const filePath = res.tempFilePath;\r\n          uni.openDocument({\r\n            filePath: filePath,\r\n            showMenu: true,\r\n            success: () => {\r\n              uni.hideLoading();\r\n            },\r\n            fail: (err) => {\r\n              uni.hideLoading();\r\n              uni.showToast({\r\n                title: '打开附件失败',\r\n                icon: 'none'\r\n              });\r\n              console.error('打开附件失败', err);\r\n            }\r\n          });\r\n        },\r\n        fail: (err) => {\r\n          uni.hideLoading();\r\n          uni.showToast({\r\n            title: '下载附件失败',\r\n            icon: 'none'\r\n          });\r\n          console.error('下载附件失败', err);\r\n        }\r\n      });\r\n    },\r\n\r\n    promptLogin(action) {\r\n      uni.showModal({\r\n        title: '请先登录',\r\n        content: `登录后才能${action}哦`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.switchTab({\r\n              url: '/pages/profile/index'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    onShareAppMessage() {\r\n      return {\r\n        title: this.interpretationDetail.title,\r\n        path: `/pages/interpretation/detail?id=${this.interpretationId}`,\r\n        imageUrl: this.videoPoster\r\n      };\r\n    },\r\n\r\n    formatDate(dateStr) {\r\n      const date = new Date(dateStr)\r\n      const year = date.getFullYear()\r\n      const month = ('0' + (date.getMonth() + 1)).slice(-2)\r\n      const day = ('0' + date.getDate()).slice(-2)\r\n      return `${year}年${month}月${day}日`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.interpretation-page {\r\n  background-color: #f4f5f7;\r\n  min-height: 100vh;\r\n  padding-bottom: 160rpx; // 为底部按钮留出空间\r\n}\r\n\r\n.video-player-section {\r\n  width: 100%;\r\n  height: 420rpx;\r\n  background-color: #000;\r\n}\r\n\r\n.video-player {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.info-card {\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n}\r\n\r\n.title {\r\n  font-size: 38rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  line-height: 1.5;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.meta-info {\r\n  font-size: 26rpx;\r\n  color: #999;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.content-body {\r\n  font-size: 30rpx;\r\n  color: #555;\r\n  line-height: 1.8;\r\n  padding-top: 30rpx;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.attachment-section {\r\n  margin-top: 40rpx;\r\n  padding: 30rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.attachment-title {\r\n  font-size: 30rpx;\r\n  color: #666;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.attachment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx;\r\n  background-color: #e9ecef;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.attachment-icon {\r\n  font-size: 36rpx;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.attachment-name {\r\n  color: #007bff;\r\n  font-size: 28rpx;\r\n  text-decoration: underline;\r\n}\r\n\r\n.loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200rpx;\r\n  color: #999;\r\n}\r\n\r\n.action-bar-footer {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  padding: 20rpx 30rpx;\r\n  padding-bottom: constant(safe-area-inset-bottom);\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  background: rgba(255, 255, 255, 0.98);\r\n  border-top: 1rpx solid #f0f0f0;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  background: none;\r\n  border: none;\r\n  padding: 0;\r\n  margin: 0;\r\n  line-height: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  color: #666;\r\n  transition: color 0.2s ease-in-out, transform 0.1s ease;\r\n  \r\n  &::after {\r\n    border: none;\r\n  }\r\n  \r\n  &.active {\r\n    color: #DC143C;\r\n  }\r\n  \r\n  .icon {\r\n    font-size: 44rpx;\r\n    transition: all 0.2s ease;\r\n  }\r\n\r\n  &.active .icon {\r\n    transform: scale(1.1);\r\n  }\r\n\r\n  .action-text {\r\n    font-size: 24rpx;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=0c55e0e2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=0c55e0e2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051463\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}