/* 公共样式文件 - common.scss */

// 引入变量和混合器文件
@import './variables.scss';
@import './mixins.scss';

/* 基础样式重置 */
body *,
page view {
  box-sizing: border-box;
  flex-shrink: 0;
}

body {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma,
    Arial, PingFang SC-Light, Microsoft YaHei;
  margin: 0;
}

button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;

  &:active {
    opacity: 0.6;
  }
}

/* Flex布局工具类 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

/* 水平对齐工具类 */
.justify-start {
  display: flex;
  justify-content: flex-start;
}

.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}

.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}

.justify-around {
  display: flex;
  justify-content: space-around;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

/* 垂直对齐工具类 */
.align-start {
  display: flex;
  align-items: flex-start;
}

.align-center {
  display: flex;
  align-items: center;
}

.align-end {
  display: flex;
  align-items: flex-end;
}

/* 组合布局工具类 */
.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 间距工具类 - 使用SCSS循环生成 */
$spacings: (
  0: $spacing-0,
  1: $spacing-1,
  2: $spacing-2,
  3: $spacing-3,
  4: $spacing-4
);

// 生成margin工具类
@each $key, $value in $spacings {
  .m-#{$key} { margin: #{$value}; }
  .mt-#{$key} { margin-top: #{$value}; }
  .mb-#{$key} { margin-bottom: #{$value}; }
  .ml-#{$key} { margin-left: #{$value}; }
  .mr-#{$key} { margin-right: #{$value}; }
}

// 生成padding工具类
@each $key, $value in $spacings {
  .p-#{$key} { padding: #{$value}; }
  .pt-#{$key} { padding-top: #{$value}; }
  .pb-#{$key} { padding-bottom: #{$value}; }
  .pl-#{$key} { padding-left: #{$value}; }
  .pr-#{$key} { padding-right: #{$value}; }
}

/* 文本工具类 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

// 字体大小工具类
$font-sizes: (
  xs: $text-xs,
  sm: $text-sm,
  base: $text-base,
  lg: $text-lg,
  xl: $text-xl,
  2xl: $text-2xl
);

@each $key, $value in $font-sizes {
  .text-#{$key} { font-size: #{$value}; }
}

.font-normal { font-weight: normal; }
.font-bold { font-weight: bold; }

/* 颜色工具类 */
$text-colors: (
  primary: $primary-color,
  secondary: $secondary-color,
  gray: $gray-medium,
  dark: $text-color,
  white: $background-color
);

$bg-colors: (
  primary: $primary-color,
  secondary: $secondary-color,
  white: $background-color,
  gray: $gray-light
);

// 生成文字颜色工具类
@each $key, $value in $text-colors {
  .text-#{$key} { color: #{$value}; }
}

// 生成背景颜色工具类
@each $key, $value in $bg-colors {
  .bg-#{$key} { background-color: #{$value}; }
}

/* 边框工具类 */
$border-style: 1rpx solid $border-color;

.border { border: $border-style; }
.border-t { border-top: $border-style; }
.border-b { border-bottom: $border-style; }
.border-l { border-left: $border-style; }
.border-r { border-right: $border-style; }

// 边框颜色变体
.border-primary { border-color: $primary-color; }
.border-secondary { border-color: $secondary-color; }

// 圆角工具类
.rounded { border-radius: $border-radius-sm; }
.rounded-lg { border-radius: $border-radius-lg; }
.rounded-full { border-radius: 50%; }

/* 显示/隐藏工具类 */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* 位置工具类 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

/* 宽高工具类 */
.w-full { width: 100%; }
.h-full { height: 100%; }

/* 文本省略 */
.ellipsis {
  @include text-ellipsis(1);
}

.ellipsis-2 {
  @include text-ellipsis(2);
}

.ellipsis-3 {
  @include text-ellipsis(3);
}

/* 卡片样式 */
.card {
  background-color: $background-color;
  border-radius: $border-radius-lg;
  padding: $spacing-3;
  @include card-shadow;
}

/* 按钮样式 */
.btn {
  @include button-style;
}

.btn-secondary {
  @include button-style($secondary-color);
}

.btn-outline {
  @include button-style(transparent, $primary-color);
  border: 1rpx solid $primary-color;
}
