# 跨境融资微信小程序 API 接口文档

## 基本信息

- **API 版本**: v1.0
- **基础 URL**: `http://localhost:5000/api`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

对于需要用户登录才能访问的接口，必须在 HTTP 请求头中加入 `Authorization` 字段。

- **认证方式**: Bearer Token
- **Header 格式**: `Authorization: Bearer <your_session_token>`
- **Token 获取**: `token` 在调用 [12.1 微信登录](#121-微信登录) 接口成功后从返回数据中获得。

**示例**:
```bash
curl -X GET "http://localhost:5000/api/collections/my" \
  -H "Authorization: Bearer session_10"
```

## 通用返回格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

## 接口目录

- [1. 首页统计数据](#1-首页统计数据)
- [2. 银行机构接口](#2-银行机构接口)
- [3. 新闻接口](#3-新闻接口)
- [4. 热门问题接口](#4-热门问题接口)
- [5. 政策文件接口](#5-政策文件接口)
- [6. 政策解读接口](#6-政策解读接口)
- [7. 搜索接口](#7-搜索接口)
- [8. 咨询管理接口](#8-咨询管理接口)
- [9. 用户互动记录接口](#9-用户互动记录接口)
- [10. 收藏管理接口](#10-收藏管理接口)
- [11. 轮播图接口](#11-轮播图接口)
- [12. 认证接口](#12-认证接口)
- [13. 用户管理接口](#13-用户管理接口)
- [14. 统计分析接口](#14-统计分析接口)
- [15. 文件管理接口](#15-文件管理接口)

---

## 🏠 1. 首页统计数据

### 1.1 获取首页统计数据
**接口地址**: `GET /api/statistics/`

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "policyCount": 125,
    "visitCount": 8520,
    "consultCount": 456
  }
}
```

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/statistics/"
```

---

## 🏦 2. 银行机构接口

### 2.1 获取银行列表
**接口地址**: `GET /api/banks/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |
| random | boolean | 否 | 是否随机排序（换一换功能） |
| name | string | 否 | 银行名称筛选 |

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "中国银行重庆分行",
        "contact_person": "张经理",
        "phone": "023-********",
        "icon": "https://example.com/bank1.png",
        "address": "重庆市渝中区解放碑大街123号",
        "longitude": 106.551556,
        "latitude": 29.563009,
        "created_at": "2025-06-22T09:29:49"
      }
    ],
    "total": 6,
    "page": 1,
    "per_page": 10,
    "pages": 1
  }
}
```

**curl 示例**:
```bash
# 普通列表
curl -X GET "http://localhost:5000/api/banks/?page=1&per_page=10"

# 随机排序（换一换）
curl -X GET "http://localhost:5000/api/banks/?random=true&per_page=5"
```

### 2.2 获取银行详情
**接口地址**: `GET /api/banks/{id}`

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/banks/1"
```

### 2.3 创建银行
**接口地址**: `POST /api/banks/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | string | 是 | 银行名称 |
| contact_person | string | 是 | 联系人 |
| phone | string | 是 | 联系电话 |
| address | string | 是 | 地址 |
| longitude | float | 是 | 经度 |
| latitude | float | 是 | 纬度 |
| icon | string | 否 | 图标URL |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/banks/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "工商银行重庆分行",
    "contact_person": "李经理",
    "phone": "023-********",
    "address": "重庆市渝北区新牌坊大街100号",
    "longitude": 106.534892,
    "latitude": 29.575297
  }'
```

### 2.4 更新银行信息
**接口地址**: `PUT /api/banks/{id}`

**curl 示例**:
```bash
curl -X PUT "http://localhost:5000/api/banks/1" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "中国银行重庆分行(更新)",
    "contact_person": "张经理"
  }'
```

### 2.5 删除银行
**接口地址**: `DELETE /api/banks/{id}`

**curl 示例**:
```bash
curl -X DELETE "http://localhost:5000/api/banks/1"
```

### 2.6 搜索附近银行
**接口地址**: `GET /api/banks/nearby`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| longitude | float | 是 | 当前经度 |
| latitude | float | 是 | 当前纬度 |
| radius | float | 否 | 搜索半径(km)，默认10km |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/banks/nearby?longitude=106.551556&latitude=29.563009&radius=5"
```

---

## 📰 3. 新闻接口

### 3.1 获取新闻列表
**接口地址**: `GET /api/news/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |
| category | string | 否 | 分类筛选 |

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "items": [
      {
        "id": 1,
        "title": "《关于进一步优化外汇管理支持涉外业务发展的通知》解读",
        "category": "政策解读",
        "cover_img": "https://example.com/news1.jpg",
        "content": "新闻内容...",
        "view_count": 156,
        "like_count": 23,
        "collect_count": 12,
        "forward_count": 8,
        "publish_date": "2024-01-15T10:00:00",
        "created_at": "2025-06-22T09:29:49"
      }
    ],
    "total": 50,
    "page": 1,
    "per_page": 10,
    "pages": 5
  }
}
```

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/news/?page=1&per_page=10&category=政策解读"
```

### 3.2 获取新闻详情
**接口地址**: `GET /api/news/{id}`

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/news/1"
```

### 3.3 创建新闻
**接口地址**: `POST /api/news/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| title | string | 是 | 新闻标题 |
| category | string | 是 | 分类 |
| content | string | 是 | 新闻内容 |
| cover_img | string | 否 | 封面图片 |
| publish_date | string | 是 | 发布时间(ISO格式) |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/news/" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新政策解读",
    "category": "政策解读",
    "content": "新闻内容...",
    "publish_date": "2025-06-23T10:00:00"
  }'
```

### 3.4 更新新闻
**接口地址**: `PUT /api/news/{id}`

**curl 示例**:
```bash
curl -X PUT "http://localhost:5000/api/news/1" \
  -H "Content-Type: application/json" \
  -d '{"title": "更新标题"}'
```

### 3.5 删除新闻
**接口地址**: `DELETE /api/news/{id}`

**curl 示例**:
```bash
curl -X DELETE "http://localhost:5000/api/news/1"
```

### 3.6 新闻互动操作
**接口地址**: `POST /api/news/{id}/action`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 操作类型(like/collect/forward) |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/news/1/action" \
  -H "Authorization: Bearer <your_session_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "like"
  }'
```

### 3.7 获取新闻分类
**接口地址**: `GET /api/news/categories`

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "categories": ["政策解读", "市场动态", "业务指南"]
  }
}
```

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/news/categories"
```

---

## ❓ 4. 热门问题接口

### 4.1 获取FAQ列表
**接口地址**: `GET /api/faqs/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |
| category | string | 否 | 分类筛选 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/faqs/?page=1&category=外汇政策"
```

### 4.2 获取FAQ详情
**接口地址**: `GET /api/faqs/{id}`

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/faqs/1"
```

### 4.3 创建FAQ
**接口地址**: `POST /api/faqs/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| category | string | 是 | 分类 |
| question | string | 是 | 问题 |
| answer | string | 是 | 答案 |
| answer_date | string | 是 | 回答日期 |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/faqs/" \
  -H "Content-Type: application/json" \
  -d '{
    "category": "外汇政策",
    "question": "如何办理外汇业务？",
    "answer": "详细步骤...",
    "answer_date": "2025-06-23"
  }'
```

### 4.4 更新FAQ
**接口地址**: `PUT /api/faqs/{id}`

### 4.5 删除FAQ
**接口地址**: `DELETE /api/faqs/{id}`

### 4.6 FAQ互动操作
**接口地址**: `POST /api/faqs/{id}/action`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 操作类型(like/collect/forward) |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/faqs/1/action" \
  -H "Authorization: Bearer <your_session_token>" \
  -H "Content-Type: application/json" \
  -d '{"action": "like"}'
```

### 4.7 获取FAQ分类
**接口地址**: `GET /api/faqs/categories`

---

## 📋 5. 政策文件接口

### 5.1 获取政策文件列表
**接口地址**: `GET /api/policies/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |
| category1 | string | 否 | 一级分类 |
| category2 | string | 否 | 二级分类 |
| category3 | string | 否 | 三级分类 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/policies/?page=1&category1=外汇管理"
```

### 5.2 获取政策文件详情
**接口地址**: `GET /api/policies/{id}`

### 5.3 创建政策文件
**接口地址**: `POST /api/policies/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| title | string | 是 | 政策标题 |
| category1 | string | 是 | 一级分类 |
| category2 | string | 否 | 二级分类 |
| category3 | string | 否 | 三级分类 |
| content | string | 是 | 政策内容 |
| publish_date | string | 是 | 发布日期 |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/policies/" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新政策文件",
    "category1": "外汇管理",
    "category2": "跨境贸易",
    "content": "政策内容...",
    "publish_date": "2025-06-23T10:00:00"
  }'
```

### 5.4 更新政策文件
**接口地址**: `PUT /api/policies/{id}`

### 5.5 删除政策文件
**接口地址**: `DELETE /api/policies/{id}`

### 5.6 政策文件互动操作
**接口地址**: `POST /api/policies/{id}/action`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 操作类型(like/collect/forward) |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/policies/1/action" \
  -H "Authorization: Bearer <your_session_token>" \
  -H "Content-Type: application/json" \
  -d '{"action": "like"}'
```

### 5.7 获取政策分类
**接口地址**: `GET /api/policies/categories`

---

## 🎥 6. 政策解读接口

### 6.1 获取政策解读列表
**接口地址**: `GET /api/interpretations/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/interpretations/?page=1"
```

### 6.2 获取政策解读详情
**接口地址**: `GET /api/interpretations/{id}`

### 6.3 创建政策解读
**接口地址**: `POST /api/interpretations/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| title | string | 是 | 解读标题 |
| video_url | string | 否 | 视频链接 |
| content | string | 否 | 文字内容 |
| publish_date | string | 是 | 发布日期 |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/interpretations/" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新政策解读",
    "video_url": "https://example.com/video.mp4",
    "content": "解读内容...",
    "publish_date": "2025-06-23T10:00:00"
  }'
```

### 6.4 更新政策解读
**接口地址**: `PUT /api/interpretations/{id}`

### 6.5 删除政策解读
**接口地址**: `DELETE /api/interpretations/{id}`

### 6.6 政策解读互动操作
**接口地址**: `POST /api/interpretations/{id}/action`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| action | string | 是 | 操作类型(like/collect/forward) |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/interpretations/1/action" \
  -H "Authorization: Bearer <your_session_token>" \
  -H "Content-Type: application/json" \
  -d '{"action": "like"}'
```

---

## 🔍 7. 搜索接口

### 7.1 主搜索接口
**接口地址**: `GET /api/search/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | string | 否 | 搜索关键词 |
| type | string | 否 | 内容类型(all/policy/news/faq/interpretation) |
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "items": [
      {
        "type": "policy",
        "id": 1,
        "title": "政策标题",
        "content": "匹配内容...",
        "url": "/policies/1"
      }
    ],
    "total": 50,
    "page": 1,
    "per_page": 20,
    "pages": 3
  }
}
```

**curl 示例**:
```bash
# 全局搜索
curl -X GET "http://localhost:5000/api/search/?q=外汇政策"

# 按类型搜索
curl -X GET "http://localhost:5000/api/search/?q=外汇&type=policy"
```

### 7.2 全局搜索
**接口地址**: `GET /api/search/global`

### 7.3 搜索新闻
**接口地址**: `GET /api/search/news`

### 7.4 搜索政策
**接口地址**: `GET /api/search/policies`

### 7.5 搜索FAQ
**接口地址**: `GET /api/search/faqs`

### 7.6 搜索政策解读
**接口地址**: `GET /api/search/interpretations`

### 7.7 搜索建议
**接口地址**: `GET /api/search/suggestions`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | string | 是 | 搜索关键词 |
| limit | int | 否 | 建议数量，默认10 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/search/suggestions?q=外汇&limit=5"
```

---

## 💬 8. 咨询管理接口

### 8.1 获取咨询列表
**接口地址**: `GET /api/inquiries/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |
| type | string | 否 | 咨询类型(policy_demand/business_consult) |
| bank_id | int | 否 | 银行ID筛选 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/inquiries/?page=1&type=policy_demand"
```

### 8.2 获取咨询详情
**接口地址**: `GET /api/inquiries/{id}`

### 8.3 创建咨询
**接口地址**: `POST /api/inquiries/`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contact_name | string | 是 | 联系人姓名 |
| contact_phone | string | 是 | 联系电话 |
| content | string | 是 | 咨询内容 |
| type | string | 是 | 咨询类型 |
| bank_id | int | 否 | 银行ID(政策需求时必填) |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/inquiries/" \
  -H "Authorization: Bearer <your_session_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "contact_name": "张三",
    "contact_phone": "138********",
    "content": "咨询外汇政策",
    "type": "business_consult"
  }'
```

### 8.4 更新咨询
**接口地址**: `PUT /api/inquiries/{id}`

### 8.5 删除咨询
**接口地址**: `DELETE /api/inquiries/{id}`

### 8.6 获取用户咨询
**接口地址**: `GET /api/inquiries/user/{user_id}`
**认证**: `需要`
**说明**: 只能获取当前登录用户的咨询列表，路径中的 `user_id` 必须与 Token 中的用户ID一致。

### 8.7 获取银行咨询
**接口地址**: `GET /api/inquiries/bank/{bank_id}`

### 8.8 导出咨询数据
**接口地址**: `GET /api/inquiries/export`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| format | string | 否 | 导出格式(excel/csv)，默认excel |
| start_date | string | 否 | 开始日期 |
| end_date | string | 否 | 结束日期 |
| type | string | 否 | 咨询类型 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/inquiries/export?format=excel&start_date=2025-01-01&end_date=2025-12-31"
```

### 8.9 预览导出数据
**接口地址**: `GET /api/inquiries/export/preview`

---

## 👥 9. 用户互动记录接口

### 9.1 获取互动记录列表
**接口地址**: `GET /api/interactions/`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |
| item_type | string | 否 | 内容类型 |
| action | string | 否 | 操作类型 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/interactions/?action=like" \
  -H "Authorization: Bearer <your_session_token>"
```

### 9.2 获取互动记录详情
**接口地址**: `GET /api/interactions/{id}`
**认证**: `需要`

### 9.3 切换互动状态
**接口地址**: `POST /api/interactions/toggle`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| item_type | string | 是 | 内容类型(news/policy/faq/interpretation) |
| item_id | int | 是 | 内容ID |
| action | string | 是 | 操作类型(like/forward) |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/interactions/toggle" \
  -H "Authorization: Bearer <your_session_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "item_type": "news",
    "item_id": 1,
    "action": "like"
  }'
```

### 9.4 记录浏览行为
**接口地址**: `POST /api/interactions/view`
**认证**: `需要`
**说明**: 此接口为浏览量统计的唯一入口。只有已登录用户在首次浏览某内容时调用此接口，才会增加该内容的 `view_count`。重复调用不会重复计数。

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| item_type | string | 是 | 内容类型(news/policy/faq/interpretation) |
| item_id | int | 是 | 内容ID |

### 9.5 创建互动记录
**接口地址**: `POST /api/interactions/`
**认证**: `需要`

### 9.6 删除互动记录
**接口地址**: `DELETE /api/interactions/{id}`
**认证**: `需要`

### 9.7 获取用户互动记录
**接口地址**: `GET /api/interactions/user/{user_id}`
**认证**: `需要`

### 9.8 获取我的互动记录
**接口地址**: `GET /api/interactions/my`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| item_type | string | 否 | 内容类型 |
| action | string | 否 | 操作类型 |

### 9.9 检查互动状态
**接口地址**: `POST /api/interactions/check`
**认证**: `需要`

### 9.10 互动统计
**接口地址**: `GET /api/interactions/stats`
**认证**: `不需要`

---

## ⭐ 10. 收藏管理接口

### 10.1 获取收藏列表
**接口地址**: `GET /api/collections/`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |
| item_type | string | 否 | 内容类型 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/collections/?item_type=news" \
  -H "Authorization: Bearer <your_session_token>"
```

### 10.2 获取收藏详情
**接口地址**: `GET /api/collections/{id}`
**认证**: `需要`

### 10.3 切换收藏状态
**接口地址**: `POST /api/collections/toggle`
**认证**: `需要`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| item_type | string | 是 | 内容类型 |
| item_id | int | 是 | 内容ID |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/collections/toggle" \
  -H "Authorization: Bearer <your_session_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "item_type": "news",
    "item_id": 1
  }'
```

### 10.4 创建收藏
**接口地址**: `POST /api/collections/`
**认证**: `需要`

### 10.5 删除收藏
**接口地址**: `DELETE /api/collections/{id}`
**认证**: `需要`

### 10.6 获取用户收藏
**接口地址**: `GET /api/collections/user/{user_id}`
**认证**: `需要`

### 10.7 获取我的收藏
**接口地址**: `GET /api/collections/my`
**认证**: `需要`

### 10.8 检查收藏状态
**接口地址**: `POST /api/collections/check`
**认证**: `需要`

---

## 🎯 11. 轮播图接口

### 11.1 获取轮播图列表
**接口地址**: `GET /api/banners/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| is_active | boolean | 否 | 是否启用，默认true |

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "banners": [
      {
        "id": 1,
        "title": "重庆跨境融资政策解读",
        "image": "https://example.com/banner1.jpg",
        "link": "",
        "sort": 1,
        "is_active": true,
        "created_at": "2025-06-23T09:29:21"
      }
    ]
  }
}
```

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/banners/"
```

### 11.2 获取轮播图详情
**接口地址**: `GET /api/banners/{id}`

### 11.3 创建轮播图
**接口地址**: `POST /api/banners/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| title | string | 是 | 标题 |
| image | string | 是 | 图片URL |
| link | string | 否 | 链接地址 |
| sort | int | 否 | 排序，默认0 |
| is_active | boolean | 否 | 是否启用，默认true |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/banners/" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新轮播图",
    "image": "https://example.com/banner.jpg",
    "link": "https://example.com",
    "sort": 1
  }'
```

### 11.4 更新轮播图
**接口地址**: `PUT /api/banners/{id}`

### 11.5 删除轮播图
**接口地址**: `DELETE /api/banners/{id}`

---

## 🔐 12. 认证接口

### 12.1 微信登录
**接口地址**: `POST /api/auth/wechat/login`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| code | string | 是 | 微信授权码 |
| nickname | string | 否 | 微信昵称 |
| avatar | string | 否 | 微信头像 |

**返回示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "openid": "wx_user_001",
      "nickname": "张小明",
      "avatar": "https://example.com/avatar1.jpg",
      "created_at": "2025-06-22T09:29:49"
    },
    "token": "session_token_here"
  }
}
```

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/auth/wechat/login" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "wx_auth_code",
    "nickname": "张三",
    "avatar": "https://example.com/avatar.jpg"
  }'
```

### 12.2 检查登录状态
**接口地址**: `GET /api/auth/check`

### 12.3 退出登录
**接口地址**: `POST /api/auth/logout`

### 12.4 获取个人资料
**接口地址**: `GET /api/auth/profile`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| user_id | int | 是 | 用户ID |

### 12.5 更新个人资料
**接口地址**: `PUT /api/auth/profile`
**认证**: `需要`
**说明**: 更新当前登录用户的个人资料。

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| nickname | string | 否 | 新的昵称 |
| avatar | string | 否 | 新的头像URL |

### 12.6 获取用户活动
**接口地址**: `GET /api/auth/user/activities`

---

## 👤 13. 用户管理接口

### 13.1 获取用户列表
**接口地址**: `GET /api/users/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认20 |
| nickname | string | 否 | 昵称筛选 |

**curl 示例**:
```bash
curl -X GET "http://localhost:5000/api/users/?page=1&nickname=张"
```

### 13.2 获取用户详情
**接口地址**: `GET /api/users/{id}`

### 13.3 创建用户
**接口地址**: `POST /api/users/`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| openid | string | 是 | 微信OpenID |
| nickname | string | 否 | 微信昵称 |
| avatar | string | 否 | 微信头像 |

**curl 示例**:
```bash
curl -X POST "http://localhost:5000/api/users/" \
  -H "Content-Type: application/json" \
  -d '{
    "openid": "wx_user_123",
    "nickname": "新用户",
    "avatar": "https://example.com/avatar.jpg"
  }'
```

### 13.4 更新用户信息
**接口地址**: `PUT /api/users/{id}`

### 13.5 删除用户
**接口地址**: `DELETE /api/users/{id}`

### 13.6 根据OpenID查询用户
**接口地址**: `GET /api/users/openid/{openid}`

---

## 📊 14. 统计分析接口

### 14.1 获取基础统计
**接口地址**: `GET /api/stats/`

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "policyCount": 125,
    "visitCount": 8520,
    "consultCount": 456
  }
}
```

### 14.2 仪表板数据
**接口地址**: `GET /api/stats/dashboard`

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total_users": 1250,
    "total_news": 89,
    "total_policies": 156,
    "total_faqs": 234,
    "total_interpretations": 67,
    "total_inquiries": 445,
    "total_banks": 12,
    "month_interactions": 3421,
    "popular_content": [
      {
        "type": "news",
        "id": 1,
        "title": "热门新闻标题",
        "view_count": 1250
      }
    ]
  }
}
```

### 14.3 月度统计
**接口地址**: `GET /api/stats/monthly`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| year | int | 否 | 年份，默认当前年 |
| month | int | 否 | 月份，默认当前月 |

### 14.4 年度统计
**接口地址**: `GET /api/stats/yearly`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| year | int | 否 | 年份，默认当前年 |

### 14.5 内容统计
**接口地址**: `GET /api/stats/content`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| type | string | 否 | 内容类型 |
| days | int | 否 | 统计天数，默认30 |

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "content_stats": [
      {
        "type": "news",
        "total_count": 89,
        "total_views": 15420,
        "total_likes": 892,
        "avg_views": 173.3
      }
    ]
  }
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录过期 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用说明

1. 所有接口都支持跨域访问
2. 时间格式统一使用 ISO 8601 格式
3. 分页参数统一使用 page 和 per_page
4. 涉及用户个人信息和操作的接口需要登录认证（详见各接口说明）
5. 支持内容的增删改查和统计功能
6. 支持数据导出（Excel/CSV格式）

---

## 📁 15. 文件管理接口

### 15.1 文件上传
**接口地址**: `POST /api/files/upload`

**请求方式**: `multipart/form-data`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| file | File | 是 | 上传的文件 |
| type | string | 否 | 文件类型(image/video/document)，默认image |

**支持的文件格式**:
- **图片**: png, jpg, jpeg, gif, bmp, webp (最大10MB)
- **视频**: mp4, avi, mov, wmv, flv, webm, mkv (最大100MB)  
- **文档**: pdf, doc, docx, txt, xls, xlsx, ppt, pptx (最大50MB)

**返回示例**:
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "filename": "20250623_103045_a1b2c3d4.jpg",
    "original_filename": "photo.jpg",
    "file_type": "image",
    "file_size": 1048576,
    "access_url": "/api/files/image/20250623_103045_a1b2c3d4.jpg",
    "full_url": "http://localhost:5000/api/files/image/20250623_103045_a1b2c3d4.jpg",
    "upload_time": "2025-06-23T10:30:45.123456"
  }
}
```

**curl 示例**:
```bash
# 上传图片
curl -X POST "http://localhost:5000/api/files/upload" \
  -F "file=@photo.jpg" \
  -F "type=image"

# 上传视频
curl -X POST "http://localhost:5000/api/files/upload" \
  -F "file=@video.mp4" \
  -F "type=video"

# 上传文档
curl -X POST "http://localhost:5000/api/files/upload" \
  -F "file=@document.pdf" \
  -F "type=document"
```

### 15.2 文件访问
**接口地址**: `GET /api/files/{file_type}/{filename}`

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| file_type | string | 是 | 文件类型(image/video/document) |
| filename | string | 是 | 文件名 |

**返回**: 直接返回文件内容（二进制流）

**curl 示例**:
```bash
# 访问图片
curl "http://localhost:5000/api/files/image/20250623_103045_a1b2c3d4.jpg"

# 访问视频
curl "http://localhost:5000/api/files/video/20250623_103045_a1b2c3d4.mp4"

# 下载文档
curl -O "http://localhost:5000/api/files/document/20250623_103045_a1b2c3d4.pdf"
```

### 15.3 获取文件信息
**接口地址**: `GET /api/files/info/{file_type}/{filename}`

**返回示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "filename": "20250623_103045_a1b2c3d4.jpg",
    "file_type": "image",
    "file_size": 1048576,
    "created_time": "2025-06-23T10:30:45.123456",
    "modified_time": "2025-06-23T10:30:45.123456",
    "access_url": "/api/files/image/20250623_103045_a1b2c3d4.jpg",
    "full_url": "http://localhost:5000/api/files/image/20250623_103045_a1b2c3d4.jpg"
  }
}
```

**curl 示例**:
```bash
curl "http://localhost:5000/api/files/info/image/20250623_103045_a1b2c3d4.jpg"
```

### 15.4 删除文件
**接口地址**: `DELETE /api/files/delete/{file_type}/{filename}`

**返回示例**:
```json
{
  "code": 200,
  "message": "文件删除成功",
  "data": null
}
```

**curl 示例**:
```bash
curl -X DELETE "http://localhost:5000/api/files/delete/image/20250623_103045_a1b2c3d4.jpg"
```

### 15.5 使用说明

1. **文件命名规则**: 上传后的文件会自动重命名为 `时间戳_UUID.扩展名` 格式，确保文件名唯一
2. **文件存储路径**: 
   - 图片: `uploads/image/`
   - 视频: `uploads/video/`
   - 文档: `uploads/document/`
3. **安全性**: 所有文件名都经过安全处理，防止路径遍历攻击
4. **文件大小限制**: 
   - 图片最大10MB
   - 视频最大100MB
   - 文档最大50MB
5. **访问方式**: 上传成功后返回的 `access_url` 可直接用于前端展示或下载

---

*本文档更新时间：2025年6月30日* 