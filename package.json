{"name": "kjrz-miniprogram", "version": "1.0.0", "description": "重庆跨境融资服务小程序", "main": "main.js", "scripts": {"serve": "npm run dev:mp-weixin", "build": "npm run build:mp-weixin", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2", "@dcloudio/uni-mp-weixin": "^2.0.2", "@dcloudio/uni-ui": "^1.4.28", "vant-weapp": "^1.11.5", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^2.0.2", "@dcloudio/uni-template-compiler": "^2.0.2", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2", "@dcloudio/vue-cli-plugin-uni": "^2.0.2", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2", "@dcloudio/webpack-uni-mp-loader": "^2.0.2", "@dcloudio/webpack-uni-pages-loader": "^2.0.2", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "mini-css-extract-plugin": "^0.9.0", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4.4", "ios >= 9"]}