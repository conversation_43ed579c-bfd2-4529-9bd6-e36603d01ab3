<template>
  <view class="demo-page">
    <!-- 页面头部 -->
    <view class="header bg-primary text-white p-3 text-center">
      <text class="text-xl font-bold">TabBar同步演示页面</text>
    </view>

    <!-- 演示说明 -->
    <view class="content p-3">
      <view class="info-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">问题解决方案</text>
        <view class="info-list">
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 修复了点击切换页面后菜单样式不更新的问题</text>
          </view>
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 添加了页面显示时的状态同步机制</text>
          </view>
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 提供了tabbar-mixin供页面使用</text>
          </view>
          <view class="info-item">
            <text class="text-sm text-dark">✅ 增强了错误处理和状态恢复</text>
          </view>
        </view>
      </view>

      <!-- 当前状态 -->
      <view class="status-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">当前页面状态</text>
        <view class="status-info">
          <view class="status-item flex-between mb-2">
            <text class="text-base text-dark">当前页面路径:</text>
            <text class="text-sm text-gray">{{ currentPagePath }}</text>
          </view>
          <view class="status-item flex-between mb-2">
            <text class="text-base text-dark">对应Tab索引:</text>
            <text class="text-base text-primary font-bold">{{ currentTabIndex }}</text>
          </view>
          <view class="status-item flex-between">
            <text class="text-base text-dark">页面显示次数:</text>
            <text class="text-base text-secondary font-bold">{{ showCount }}</text>
          </view>
        </view>
      </view>

      <!-- 测试功能 -->
      <view class="test-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">测试功能</text>
        
        <!-- 手动同步按钮 -->
        <view class="test-section mb-3">
          <text class="text-base text-dark mb-2">手动同步TabBar状态:</text>
          <view class="button-group flex-row justify-around">
            <button 
              class="test-btn"
              @click="syncTabbarState"
            >
              同步状态
            </button>
            <button 
              class="test-btn"
              @click="resetTabbarState"
            >
              重置状态
            </button>
          </view>
        </view>

        <!-- 状态设置 -->
        <view class="test-section">
          <text class="text-base text-dark mb-2">设置特定Tab状态:</text>
          <view class="button-group flex-row justify-around">
            <button 
              class="state-btn"
              :class="{ active: currentTabIndex === 0 }"
              @click="setTabbarState(0)"
            >
              首页
            </button>
            <button 
              class="state-btn"
              :class="{ active: currentTabIndex === 1 }"
              @click="setTabbarState(1)"
            >
              政策
            </button>
            <button 
              class="state-btn"
              :class="{ active: currentTabIndex === 2 }"
              @click="setTabbarState(2)"
            >
              咨询
            </button>
            <button 
              class="state-btn"
              :class="{ active: currentTabIndex === 3 }"
              @click="setTabbarState(3)"
            >
              我的
            </button>
          </view>
        </view>
      </view>

      <!-- 技术说明 -->
      <view class="tech-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">技术实现</text>
        <view class="tech-list">
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">1. 立即状态更新:</text>
            <text class="text-xs text-gray block mt-1">点击时立即更新状态，不等待页面跳转完成</text>
          </view>
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">2. 事件通信机制:</text>
            <text class="text-xs text-gray block mt-1">使用uni.$emit和uni.$on进行组件间通信</text>
          </view>
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">3. 页面生命周期:</text>
            <text class="text-xs text-gray block mt-1">在onShow和onLoad中自动同步状态</text>
          </view>
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">4. Mixin复用:</text>
            <text class="text-xs text-gray block mt-1">提供tabbar-mixin供所有tabbar页面使用</text>
          </view>
          <view class="tech-item">
            <text class="text-sm text-dark font-bold">5. 错误恢复:</text>
            <text class="text-xs text-gray block mt-1">跳转失败时自动恢复之前的状态</text>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="usage-card bg-white rounded-lg p-3">
        <text class="text-lg font-bold text-primary mb-2">使用说明</text>
        <view class="usage-list">
          <view class="usage-item mb-2">
            <text class="text-sm text-dark font-bold">在页面中引入mixin:</text>
            <view class="code-block bg-gray p-2 rounded mt-1">
              <text class="text-xs text-dark">import tabbarMixin from '@/mixins/tabbar-mixin.js'</text>
            </view>
          </view>
          <view class="usage-item mb-2">
            <text class="text-sm text-dark font-bold">在页面中使用mixin:</text>
            <view class="code-block bg-gray p-2 rounded mt-1">
              <text class="text-xs text-dark">mixins: [tabbarMixin]</text>
            </view>
          </view>
          <view class="usage-item">
            <text class="text-sm text-dark font-bold">自动功能:</text>
            <text class="text-xs text-gray block mt-1">页面显示时会自动同步TabBar状态，无需手动调用</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部占位 -->
    <view class="tabbar-placeholder"></view>

    <!-- 自定义TabBar -->
    <custom-tab-bar ref="tabbar" />
  </view>
</template>

<script>
import CustomTabBar from '@/custom-tab-bar/index.vue'
import tabbarMixin from '@/mixins/tabbar-mixin.js'

export default {
  name: 'TabbarSyncDemo',
  components: {
    CustomTabBar
  },
  mixins: [tabbarMixin], // 使用tabbar同步mixin
  
  data() {
    return {
      currentPagePath: '',
      currentTabIndex: -1,
      showCount: 0
    }
  },
  
  onShow() {
    // 更新显示次数
    this.showCount++;
    // 更新当前页面信息
    this.updateCurrentPageInfo();
  },
  
  onLoad() {
    // 初始化页面信息
    this.updateCurrentPageInfo();
  },
  
  methods: {
    // 更新当前页面信息
    updateCurrentPageInfo() {
      try {
        const pages = getCurrentPages();
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          this.currentPagePath = '/' + currentPage.route;
          this.currentTabIndex = this.tabbarPages[this.currentPagePath] || -1;
        }
      } catch (error) {
        console.error('获取页面信息失败:', error);
      }
    },
    
    // 手动同步TabBar状态
    syncTabbarState() {
      this.updateTabbarState();
      uni.showToast({
        title: '状态已同步',
        icon: 'success',
        duration: 1500
      });
    },
    
    // 重置TabBar状态
    resetTabbarState() {
      if (this.$refs.tabbar && this.$refs.tabbar.initCurrentTab) {
        this.$refs.tabbar.initCurrentTab();
        this.updateCurrentPageInfo();
        uni.showToast({
          title: '状态已重置',
          icon: 'success',
          duration: 1500
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.demo-page {
  min-height: 100vh;
  background-color: $gray-light;
}

.content {
  padding-bottom: 200rpx; // 为tabbar留出空间
}

.info-card,
.status-card,
.test-card,
.tech-card,
.usage-card {
  @include card-shadow('md');
}

.test-btn,
.state-btn {
  @include button-style($gray-light, $text-color, 'small');
  min-width: 120rpx;
  
  &.active {
    @include button-style($primary-color, white, 'small');
  }
}

.code-block {
  font-family: 'Courier New', monospace;
  overflow-x: auto;
}

.tabbar-placeholder {
  height: 200rpx; // 为tabbar留出空间
}

// 响应式适配
@include respond-to('md') {
  .button-group {
    justify-content: space-between;
  }
}
</style>
