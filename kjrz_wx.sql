/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80033
 Source Host           : localhost:3306
 Source Schema         : kjrz_wx

 Target Server Type    : MySQL
 Target Server Version : 80033
 File Encoding         : 65001

 Date: 23/06/2025 09:32:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for bank
-- ----------------------------
DROP TABLE IF EXISTS `bank`;
CREATE TABLE `bank`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '银行名称',
  `contact_person` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系人',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '电话',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标URL',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '地址',
  `longitude` decimal(9, 6) NOT NULL COMMENT '经度',
  `latitude` decimal(9, 6) NOT NULL COMMENT '纬度',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '外汇业务办理银行' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bank
-- ----------------------------
INSERT INTO `bank` VALUES (1, '中国银行重庆分行', '张经理', '023-********', 'https://example.com/bank1.png', '重庆市渝中区解放碑大街123号', 106.551556, 29.563009, '2025-06-22 09:29:49');
INSERT INTO `bank` VALUES (2, '建设银行重庆分行', '李经理', '023-********', 'https://example.com/bank2.png', '重庆市江北区观音桥步行街456号', 106.534892, 29.575297, '2025-06-22 09:29:49');
INSERT INTO `bank` VALUES (3, '工商银行重庆分行', '王经理', '023-********', 'https://example.com/bank3.png', '重庆市南岸区南坪正街789号', 106.560364, 29.523528, '2025-06-22 09:29:49');
INSERT INTO `bank` VALUES (4, '农业银行重庆分行', '刘经理', '023-********', 'https://example.com/bank4.png', '重庆市沙坪坝区三峡广场101号', 106.456878, 29.541224, '2025-06-22 09:29:49');
INSERT INTO `bank` VALUES (5, '交通银行重庆分行', '陈经理', '023-********', 'https://example.com/bank5.png', '重庆市九龙坡区杨家坪步行街202号', 106.510357, 29.510081, '2025-06-22 09:29:49');
INSERT INTO `bank` VALUES (6, '测试银行', '测试联系人', '023-********', NULL, '测试地址', 106.550700, 29.564700, '2025-06-22 10:25:08');

-- ----------------------------
-- Table structure for banner
-- ----------------------------
DROP TABLE IF EXISTS `banner`;
CREATE TABLE `banner`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '图片URL',
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '链接地址',
  `sort` int(0) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '轮播图' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banner
-- ----------------------------
INSERT INTO `banner` VALUES (1, '重庆跨境融资政策解读', 'https://example.com/banner1.jpg', '', 1, 1, '2025-06-23 09:29:21');
INSERT INTO `banner` VALUES (2, '外汇管理便民措施', 'https://example.com/banner2.jpg', '', 2, 1, '2025-06-23 09:29:21');
INSERT INTO `banner` VALUES (3, '重庆自贸区金融创新', 'https://example.com/banner3.jpg', '', 3, 1, '2025-06-23 09:29:21');

-- ----------------------------
-- Table structure for faq
-- ----------------------------
DROP TABLE IF EXISTS `faq`;
CREATE TABLE `faq`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '一级分类',
  `question` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '提问',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '解答',
  `like_count` int(0) NULL DEFAULT 0,
  `view_count` int(0) NULL DEFAULT 0,
  `collect_count` int(0) NULL DEFAULT 0,
  `forward_count` int(0) NULL DEFAULT 0,
  `answer_date` date NOT NULL COMMENT '回答时间',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '热门问题' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of faq
-- ----------------------------
INSERT INTO `faq` VALUES (1, '外汇政策', '企业办理外汇收支需要哪些材料？', '企业办理外汇收支一般需要以下材料：<br/>1. 企业营业执照副本<br/>2. 组织机构代码证<br/>3. 税务登记证<br/>4. 开户许可证<br/>5. 相关业务合同或凭证<br/>6. 其他外汇管理部门要求的材料<br/><br/>具体材料要求可能因业务类型而异，建议事先咨询相关银行或外汇管理部门。', 45, 234, 23, 12, '2024-01-15', '2025-06-22 09:29:49');
INSERT INTO `faq` VALUES (2, '跨境融资', '什么是全口径跨境融资宏观审慎管理？', '全口径跨境融资宏观审慎管理是指：<br/>1. 覆盖银行、企业等各类市场主体<br/>2. 涵盖本外币各类跨境融资<br/>3. 建立资本流动宏观审慎管理框架<br/>4. 实施跨境融资风险加权余额上限管理<br/><br/>该管理模式有助于：<br/>• 防范系统性金融风险<br/>• 促进跨境融资便利化<br/>• 维护外汇市场稳定', 38, 189, 19, 8, '2024-01-18', '2025-06-22 09:29:49');
INSERT INTO `faq` VALUES (3, '业务办理', '如何办理境外直接投资外汇登记？', '境外直接投资外汇登记办理流程：<br/>1. <strong>准备材料</strong><br/>   • 境外投资外汇登记业务申请表<br/>   • 营业执照<br/>   • 境外投资项目核准或备案文件<br/>   • 境外投资相关合同协议<br/><br/>2. <strong>提交申请</strong><br/>   • 登录外汇管理局官网<br/>   • 在线填报申请信息<br/>   • 上传相关材料<br/><br/>3. <strong>审核办理</strong><br/>   • 外汇局审核材料<br/>   • 必要时进行现场核查<br/>   • 出具登记凭证', 52, 267, 31, 15, '2024-01-20', '2025-06-22 09:29:49');
INSERT INTO `faq` VALUES (4, '政策咨询', '跨境电商收付汇有什么便利化措施？', '跨境电商收付汇便利化措施包括：<br/><br/><strong>收汇便利化：</strong><br/>• 简化小额交易审核<br/>• 支持第三方支付机构代为收汇<br/>• 允许净额结算<br/><br/><strong>付汇便利化：</strong><br/>• 优化购付汇流程<br/>• 支持集中支付<br/>• 简化单证审核<br/><br/><strong>结售汇便利化：</strong><br/>• 扩大结售汇主体范围<br/>• 简化结售汇手续<br/>• 提高结售汇效率', 29, 156, 18, 7, '2024-01-25', '2025-06-22 09:29:49');
INSERT INTO `faq` VALUES (5, '风险管理', '企业如何防范跨境融资汇率风险？', '企业防范跨境融资汇率风险的主要措施：<br/><br/><strong>1. 自然对冲</strong><br/>• 资产负债币种匹配<br/>• 收支币种匹配<br/>• 期限结构匹配<br/><br/><strong>2. 金融工具对冲</strong><br/>• 外汇远期<br/>• 外汇期权<br/>• 货币互换<br/><br/><strong>3. 操作策略</strong><br/>• 分批融资<br/>• 动态调整<br/>• 专业团队管理<br/><br/><strong>4. 政策工具</strong><br/>• 跨境人民币融资<br/>• 本币结算<br/>• 政策性保险', 41, 203, 26, 11, '2024-01-30', '2025-06-22 09:29:49');
INSERT INTO `faq` VALUES (6, '测试分类', '这是测试问题？', '这是测试答案', 0, 0, 0, 0, '2025-06-22', '2025-06-22 10:22:49');
INSERT INTO `faq` VALUES (7, '测试分类', '这是测试问题？', '这是测试答案', 0, 0, 0, 0, '2025-06-22', '2025-06-22 10:25:00');

-- ----------------------------
-- Table structure for inquiry
-- ----------------------------
DROP TABLE IF EXISTS `inquiry`;
CREATE TABLE `inquiry`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `user_id` int(0) NULL DEFAULT NULL COMMENT '关联用户',
  `contact_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '留言人名称',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系电话',
  `content` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '留言内容',
  `type` enum('policy_demand','business_consult') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '咨询类型',
  `bank_id` int(0) NULL DEFAULT NULL COMMENT '银行对象ID(政策需求时必填)',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `bank_id`(`bank_id`) USING BTREE,
  CONSTRAINT `inquiry_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `inquiry_ibfk_2` FOREIGN KEY (`bank_id`) REFERENCES `bank` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '咨询留言' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inquiry
-- ----------------------------
INSERT INTO `inquiry` VALUES (1, 1, '张小明', '***********', '我们公司想要进行境外投资，请问需要办理哪些外汇手续？投资金额大概500万美元，目标是在新加坡设立子公司。希望了解具体的申请流程和所需材料。', 'business_consult', NULL, '2025-06-22 09:29:49');
INSERT INTO `inquiry` VALUES (2, 2, '李小红', '***********', '我们是一家跨境电商企业，在办理跨境收付汇时遇到一些问题。希望能够得到专业指导，特别是关于第三方支付平台的收汇政策。', 'business_consult', NULL, '2025-06-22 09:29:49');
INSERT INTO `inquiry` VALUES (3, 3, '王小华', '***********', '我公司急需跨境融资1000万元用于扩大生产，希望建设银行能够提供相关的融资产品介绍和申请指导。', 'policy_demand', 2, '2025-06-22 09:29:49');
INSERT INTO `inquiry` VALUES (4, 4, '刘小强', '***********', '关于全口径跨境融资宏观审慎管理政策，我们公司在理解和执行方面有一些疑问，希望能够得到详细解答。', 'business_consult', NULL, '2025-06-22 09:29:49');
INSERT INTO `inquiry` VALUES (5, 5, '陈小美', '***********', '我们公司计划在中国银行办理外汇套期保值业务，希望了解具体的业务流程和风险控制措施。', 'policy_demand', 1, '2025-06-22 09:29:49');
INSERT INTO `inquiry` VALUES (6, NULL, '赵大伟', '***********', '作为一家制造业企业，我们希望了解如何利用跨境人民币结算政策降低汇率风险，请提供相关政策咨询。', 'business_consult', NULL, '2025-06-22 09:29:49');
INSERT INTO `inquiry` VALUES (7, NULL, '孙小芳', '***********', '我们公司在工商银行开立了外汇账户，现在想要了解外汇资金集中运营管理的相关政策和操作流程。', 'policy_demand', 3, '2025-06-22 09:29:49');
INSERT INTO `inquiry` VALUES (8, NULL, '测试联系人', '***********', '这是测试咨询内容', 'business_consult', NULL, '2025-06-22 10:23:08');
INSERT INTO `inquiry` VALUES (9, NULL, '测试联系人', '***********', '这是测试咨询内容', 'business_consult', NULL, '2025-06-22 10:25:18');

-- ----------------------------
-- Table structure for news
-- ----------------------------
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '封面图片',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '富文本内容',
  `view_count` int(0) NULL DEFAULT 0 COMMENT '浏览量',
  `like_count` int(0) NULL DEFAULT 0 COMMENT '点赞量',
  `collect_count` int(0) NULL DEFAULT 0 COMMENT '收藏量',
  `forward_count` int(0) NULL DEFAULT 0 COMMENT '转发量',
  `publish_date` datetime(0) NOT NULL COMMENT '发布时间',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '要闻动态' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of news
-- ----------------------------
INSERT INTO `news` VALUES (1, '政策解读', '《关于进一步优化外汇管理支持涉外业务发展的通知》解读', 'https://example.com/news1.jpg', '<p>近日，国家外汇管理局发布《关于进一步优化外汇管理支持涉外业务发展的通知》，旨在进一步便利银行和企业办理外汇业务...</p><p>主要内容包括：</p><ul><li>简化跨境人民币业务流程</li><li>优化外汇收支便利化措施</li><li>完善跨境投融资外汇管理</li></ul>', 156, 23, 12, 8, '2024-01-15 10:00:00', '2025-06-22 09:29:49');
INSERT INTO `news` VALUES (2, '市场动态', '2024年一季度重庆跨境融资市场分析报告', 'https://example.com/news2.jpg', '<p>据重庆市商务委统计，2024年一季度重庆市跨境融资规模同比增长15.8%，呈现良好发展态势...</p><p>主要特点：</p><ul><li>融资规模稳步增长</li><li>融资结构不断优化</li><li>风险管控能力提升</li></ul>', 243, 35, 18, 15, '2024-01-20 14:30:00', '2025-06-22 09:29:49');
INSERT INTO `news` VALUES (3, '业务指南', '企业境外投资外汇登记办理指南', 'https://example.com/news3.jpg', '<p>为帮助企业更好地了解境外投资外汇登记相关政策和办理流程，现将主要内容整理如下...</p><p>办理流程：</p><ol><li>准备相关材料</li><li>在线填报申请</li><li>提交审核材料</li><li>领取登记证书</li></ol>', 189, 28, 16, 11, '2024-01-25 09:15:00', '2025-06-22 09:29:49');
INSERT INTO `news` VALUES (4, '政策解读', '跨境电商外汇收支便利化政策解读', 'https://example.com/news4.jpg', '<p>为支持跨境电商健康快速发展，外汇管理部门推出多项便利化措施...</p><p>主要措施包括：</p><ul><li>简化收付汇手续</li><li>优化结售汇服务</li><li>完善风险管理</li></ul>', 201, 31, 14, 9, '2024-01-30 16:45:00', '2025-06-22 09:29:49');
INSERT INTO `news` VALUES (5, '市场动态', '重庆自贸区金融创新案例分享', 'https://example.com/news5.jpg', '<p>重庆自贸区在金融创新方面取得显著成效，推出多项创新举措...</p><p>创新亮点：</p><ul><li>跨境人民币创新业务试点</li><li>外债便利化额度试点</li><li>资本项目收入支付便利化试点</li></ul>', 167, 22, 10, 6, '2024-02-05 11:20:00', '2025-06-22 09:29:49');
INSERT INTO `news` VALUES (6, '测试分类', '测试新闻标题', 'https://example.com/news.jpg', '这是测试新闻内容', 0, 0, 0, 0, '2025-06-22 18:22:24', '2025-06-22 10:22:29');
INSERT INTO `news` VALUES (7, '测试分类', '测试新闻标题', 'https://example.com/news.jpg', '这是测试新闻内容', 9, 0, 0, 0, '2025-06-22 18:24:34', '2025-06-22 10:24:39');

-- ----------------------------
-- Table structure for policy_document
-- ----------------------------
DROP TABLE IF EXISTS `policy_document`;
CREATE TABLE `policy_document`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `category1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '一级分类',
  `category2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二级分类',
  `category3` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '三级分类',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '富文本内容(含附件链接)',
  `view_count` int(0) NULL DEFAULT 0,
  `like_count` int(0) NULL DEFAULT 0,
  `collect_count` int(0) NULL DEFAULT 0,
  `forward_count` int(0) NULL DEFAULT 0,
  `publish_date` datetime(0) NOT NULL,
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '政策文件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policy_document
-- ----------------------------
INSERT INTO `policy_document` VALUES (1, '国家外汇管理局关于进一步促进跨境贸易投资便利化的通知', '外汇管理', '跨境贸易', '便民措施', '<p>为深入贯彻党中央、国务院关于稳外贸稳外资的决策部署...</p><p>具体措施：</p><ol><li>优化外汇账户管理</li><li>便利跨境人民币使用</li><li>完善外债管理政策</li></ol><p>附件：<a href=\"/files/policy1.pdf\">政策原文下载</a></p>', 312, 45, 28, 19, '2024-01-10 08:00:00', '2025-06-22 09:29:49');
INSERT INTO `policy_document` VALUES (2, '重庆市促进跨境融资发展实施细则', '地方政策', '跨境融资', '执行细则', '<p>根据国家相关政策，结合重庆实际，制定本实施细则...</p><p>主要内容：</p><ul><li>适用范围和条件</li><li>办理流程和材料</li><li>风险防控措施</li><li>监督管理要求</li></ul><p>附件：<a href=\"/files/policy2.pdf\">实施细则全文</a></p>', 256, 38, 22, 14, '2024-01-12 14:00:00', '2025-06-22 09:29:49');
INSERT INTO `policy_document` VALUES (3, '银行业金融机构外汇业务管理办法', '金融监管', '银行业务', '管理办法', '<p>为规范银行业金融机构外汇业务经营行为...</p><p>管理要求：</p><ol><li>业务准入条件</li><li>内控制度建设</li><li>风险管理要求</li><li>监督检查机制</li></ol><p>附件：<a href=\"/files/policy3.pdf\">管理办法原文</a></p>', 198, 29, 17, 12, '2024-01-18 10:30:00', '2025-06-22 09:29:49');
INSERT INTO `policy_document` VALUES (4, '企业对外投资备案管理办法', '对外投资', '备案管理', '管理流程', '<p>为加强和规范企业对外投资备案管理...</p><p>备案流程：</p><ol><li>在线申报</li><li>材料审核</li><li>现场核查</li><li>备案确认</li></ol><p>附件：<a href=\"/files/policy4.pdf\">备案表格下载</a></p>', 234, 33, 20, 11, '2024-01-22 15:45:00', '2025-06-22 09:29:49');
INSERT INTO `policy_document` VALUES (5, '跨境人民币业务管理暂行办法', '人民币国际化', '跨境结算', '暂行办法', '<p>为促进跨境人民币业务健康发展...</p><p>业务范围：</p><ul><li>跨境贸易人民币结算</li><li>对外直接投资人民币结算</li><li>外商直接投资人民币结算</li><li>其他跨境人民币业务</li></ul><p>附件：<a href=\"/files/policy5.pdf\">办法全文</a></p>', 287, 41, 25, 16, '2024-01-28 09:15:00', '2025-06-22 09:29:49');
INSERT INTO `policy_document` VALUES (6, '测试政策文件', '外汇管理', '融资管理', '跨境业务', '这是测试政策文件内容', 0, 0, 0, 0, '2025-06-22 00:00:00', '2025-06-22 10:22:39');
INSERT INTO `policy_document` VALUES (7, '测试政策文件', '外汇管理', '融资管理', '跨境业务', '这是测试政策文件内容', 0, 0, 0, 0, '2025-06-22 00:00:00', '2025-06-22 10:24:49');

-- ----------------------------
-- Table structure for policy_interpretation
-- ----------------------------
DROP TABLE IF EXISTS `policy_interpretation`;
CREATE TABLE `policy_interpretation`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '视频链接',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '富文本内容',
  `view_count` int(0) NULL DEFAULT 0,
  `like_count` int(0) NULL DEFAULT 0,
  `collect_count` int(0) NULL DEFAULT 0,
  `forward_count` int(0) NULL DEFAULT 0,
  `publish_date` datetime(0) NOT NULL,
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '政策解答' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policy_interpretation
-- ----------------------------
INSERT INTO `policy_interpretation` VALUES (1, '2024年外汇管理新政策解读', 'https://example.com/video1.mp4', '<p>本期政策解读主要针对2024年新颁布的外汇管理政策进行详细解析...</p><h3>主要内容：</h3><ul><li>政策背景和意义</li><li>具体措施解读</li><li>对企业的影响</li><li>操作建议</li></ul><p>通过本次解读，帮助企业更好地理解和运用新政策。</p>', 445, 67, 34, 22, '2024-01-16 10:00:00', '2025-06-22 09:29:49');
INSERT INTO `policy_interpretation` VALUES (2, '跨境融资实务操作指南', 'https://example.com/video2.mp4', '<p>详细介绍跨境融资的实务操作流程和注意事项...</p><h3>重点内容：</h3><ol><li>融资前期准备</li><li>申请流程详解</li><li>常见问题解答</li><li>风险防控要点</li></ol><p>结合实际案例，为企业提供具体的操作指导。</p>', 356, 53, 28, 18, '2024-01-23 14:30:00', '2025-06-22 09:29:49');
INSERT INTO `policy_interpretation` VALUES (3, '外汇收支便民措施详解', 'https://example.com/video3.mp4', '<p>全面解读最新的外汇收支便民措施...</p><h3>解读要点：</h3><ul><li>简化审核流程</li><li>提高办事效率</li><li>减少申报材料</li><li>优化服务体验</li></ul><p>让企业充分享受政策红利，提升办事便利度。</p>', 298, 44, 25, 14, '2024-02-01 09:15:00', '2025-06-22 09:29:49');
INSERT INTO `policy_interpretation` VALUES (4, '重庆自贸区金融创新政策解读', 'https://example.com/video4.mp4', '<p>专门针对重庆自贸区的金融创新政策进行深入解读...</p><h3>创新亮点：</h3><ul><li>跨境投融资便利化</li><li>外债管理创新</li><li>跨境人民币业务创新</li><li>金融服务贸易创新</li></ul><p>为自贸区内企业提供专业的政策指导。</p>', 387, 58, 31, 20, '2024-02-08 11:45:00', '2025-06-22 09:29:49');
INSERT INTO `policy_interpretation` VALUES (5, '企业境外投资合规要点', 'https://example.com/video5.mp4', '<p>从合规角度详细讲解企业境外投资的各项要求...</p><h3>合规要点：</h3><ol><li>投资主体资格</li><li>投资项目审批</li><li>资金来源合规</li><li>后续管理义务</li></ol><p>帮助企业规范开展境外投资，避免合规风险。</p>', 267, 41, 22, 13, '2024-02-15 15:20:00', '2025-06-22 09:29:49');
INSERT INTO `policy_interpretation` VALUES (6, '测试政策解答', NULL, '这是测试政策解答内容', 0, 0, 0, 0, '2025-06-22 00:00:00', '2025-06-22 10:23:16');
INSERT INTO `policy_interpretation` VALUES (7, '测试政策解答', NULL, '这是测试政策解答内容', 0, 0, 0, 0, '2025-06-22 00:00:00', '2025-06-22 10:25:26');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '微信唯一标识',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信头像URL',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `openid`(`openid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '微信用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'wx_user_001', '张小明', 'https://example.com/avatar1.jpg', '2025-06-22 09:29:49');
INSERT INTO `user` VALUES (2, 'wx_user_002', '李小红', 'https://example.com/avatar2.jpg', '2025-06-22 09:29:49');
INSERT INTO `user` VALUES (3, 'wx_user_003', '王小华', 'https://example.com/avatar3.jpg', '2025-06-22 09:29:49');
INSERT INTO `user` VALUES (4, 'wx_user_004', '刘小强', 'https://example.com/avatar4.jpg', '2025-06-22 09:29:49');
INSERT INTO `user` VALUES (5, 'wx_user_005', '陈小美', 'https://example.com/avatar5.jpg', '2025-06-22 09:29:49');
INSERT INTO `user` VALUES (6, 'test_openid_1750587730', '更新的用户名', 'https://example.com/avatar.jpg', '2025-06-22 10:22:15');
INSERT INTO `user` VALUES (7, 'mock_openid_test_code', '测试用户', 'https://example.com/avatar.jpg', '2025-06-22 10:23:41');

-- ----------------------------
-- Table structure for user_collection
-- ----------------------------
DROP TABLE IF EXISTS `user_collection`;
CREATE TABLE `user_collection`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `user_id` int(0) NOT NULL,
  `item_type` enum('news','policy','faq','interpretation') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收藏类型',
  `item_id` int(0) NOT NULL COMMENT '收藏项ID',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `user_collection_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户收藏记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_collection
-- ----------------------------
INSERT INTO `user_collection` VALUES (1, 1, 'news', 1, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (2, 1, 'news', 3, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (3, 1, 'policy', 1, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (4, 1, 'faq', 1, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (5, 2, 'news', 2, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (6, 2, 'policy', 2, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (7, 2, 'interpretation', 1, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (8, 3, 'news', 1, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (9, 3, 'news', 4, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (10, 3, 'faq', 2, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (11, 3, 'faq', 3, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (12, 4, 'policy', 3, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (13, 4, 'policy', 4, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (14, 4, 'interpretation', 2, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (15, 5, 'news', 5, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (16, 5, 'faq', 4, '2025-06-22 09:29:49');
INSERT INTO `user_collection` VALUES (17, 5, 'interpretation', 3, '2025-06-22 09:29:49');

-- ----------------------------
-- Table structure for user_interaction
-- ----------------------------
DROP TABLE IF EXISTS `user_interaction`;
CREATE TABLE `user_interaction`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `user_id` int(0) NOT NULL,
  `item_type` enum('news','policy','faq','interpretation') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '互动类型',
  `item_id` int(0) NOT NULL COMMENT '互动项ID',
  `action` enum('view','like','collect','forward') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '互动动作',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `user_interaction_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户互动记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_interaction
-- ----------------------------
INSERT INTO `user_interaction` VALUES (1, 1, 'news', 1, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (2, 1, 'news', 1, 'like', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (3, 1, 'news', 1, 'collect', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (4, 1, 'news', 3, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (5, 1, 'policy', 1, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (6, 1, 'policy', 1, 'like', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (7, 1, 'faq', 1, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (8, 2, 'news', 2, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (9, 2, 'news', 2, 'like', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (10, 2, 'policy', 2, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (11, 2, 'interpretation', 1, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (12, 2, 'interpretation', 1, 'like', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (13, 3, 'news', 1, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (14, 3, 'news', 4, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (15, 3, 'news', 4, 'forward', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (16, 3, 'faq', 2, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (17, 3, 'faq', 3, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (18, 3, 'faq', 3, 'like', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (19, 4, 'policy', 3, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (20, 4, 'policy', 4, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (21, 4, 'policy', 4, 'collect', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (22, 4, 'interpretation', 2, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (23, 5, 'news', 5, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (24, 5, 'faq', 4, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (25, 5, 'faq', 4, 'like', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (26, 5, 'interpretation', 3, 'view', '2025-06-22 09:29:49');
INSERT INTO `user_interaction` VALUES (27, 5, 'interpretation', 3, 'forward', '2025-06-22 09:29:49');

SET FOREIGN_KEY_CHECKS = 1;
