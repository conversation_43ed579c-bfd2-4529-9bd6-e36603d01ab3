{"version": 3, "sources": ["uni-app:///pages/faq/detail.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/faq/detail.vue?ca7f", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/faq/detail.vue?a649", "uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/faq/detail.vue?79f3", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/faq/detail.vue?6b08", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/faq/detail.vue?ab28", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/faq/detail.vue?6719"], "names": ["data", "faqDetail", "faqId", "isLiked", "isCollected", "onLoad", "methods", "loadFAQDetail", "api", "res", "console", "uni", "title", "icon", "checkStatus", "token", "params", "item_type", "item_id", "Promise", "actions", "likeRes", "collectRes", "recordView", "toggleLike", "action", "toggleCollect", "onShareAppMessage", "path", "imageUrl", "promptLogin", "content", "success", "url", "formatDate", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA8CA;AAAA;AAAA;AAAA,eAEA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACAC,aACAX;kBAAAY;gBAAA,KACAZ,2CACA;cAAA;gBAAA;gBAAA;gBAHAa;gBAAAC;gBAKA;kBACA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAP;kBACAS;kBACAC;kBACA;gBACA;cAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAR;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAT;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAP;kBACAiB;kBACAR;kBACAC;gBACA;cAAA;gBAJAT;gBAMA;gBACA;gBAEAE;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAX;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAP;kBACAS;kBACAC;gBACA;cAAA;gBAHAT;gBAKA;gBACA;gBAEAE;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiB;MACA;QACAf;QACAgB;QACAC;MACA;IACA;IAEAC;MACAnB;QACAC;QACAmB;QACAC;UACA;YACArB;cACAsB;YACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/MA;AAAA;AAAA;AAAA;AAA4oD,CAAgB,i/CAAG,EAAC,C;;;;;;;;;;;ACAhqD;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k0BAAG,EAAC,C", "file": "pages/faq/detail.js", "sourcesContent": ["<template>\r\n  <view class=\"faq-detail-page\">\r\n    <view v-if=\"faqDetail\" class=\"content\">\r\n      \r\n      <view class=\"qa-card\">\r\n        <view class=\"question-section\">\r\n          <rich-text :nodes=\"faqDetail.question\"></rich-text>\r\n        </view>\r\n        <view class=\"answer-section\">\r\n          <rich-text :nodes=\"faqDetail.answer\"></rich-text>\r\n        </view>\r\n        <view class=\"meta-info\">\r\n          <text class=\"meta-item\">回答时间：{{ formatDate(faqDetail.answer_date) }}</text>\r\n          <text class=\"meta-item\">{{ faqDetail.view_count || 0 }}次浏览</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"disclaimer\">\r\n        说明：回答基于当时有效的外汇管理政策，如外汇管理政策发生变化，以最新政策为准，如有疑问请咨询外汇局或商业银行。\r\n      </view>\r\n      \r\n    </view>\r\n\r\n    <!-- 悬浮操作栏 -->\r\n    <view v-if=\"faqDetail\" class=\"action-bar-sticky\">\r\n      <button @click=\"toggleLike\" class=\"action-btn\" :class=\"{ active: isLiked }\">\r\n        <text class=\"icon\">👍</text>\r\n        <text class=\"action-text\">{{ faqDetail.like_count > 0 ? faqDetail.like_count : '点赞' }}</text>\r\n      </button>\r\n      <button @click=\"toggleCollect\" class=\"action-btn\" :class=\"{ active: isCollected }\">\r\n        <text class=\"icon\">{{ isCollected ? '⭐' : '☆' }}</text>\r\n        <text class=\"action-text\">{{ faqDetail.collect_count > 0 ? faqDetail.collect_count : '收藏' }}</text>\r\n      </button>\r\n      <button open-type=\"share\" class=\"action-btn\">\r\n        <text class=\"icon\">📤</text>\r\n        <text class=\"action-text\">{{ faqDetail.forward_count > 0 ? faqDetail.forward_count : '分享' }}</text>\r\n      </button>\r\n    </view>\r\n\r\n    <view v-else class=\"loading\">\r\n      <text>加载中...</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/utils/api.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      faqDetail: null,\r\n      faqId: null,\r\n      isLiked: false,\r\n      isCollected: false,\r\n    }\r\n  },\r\n\r\n  onLoad(options) {\r\n    this.faqId = options.id\r\n    if (this.faqId) {\r\n      this.loadFAQDetail()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    async loadFAQDetail() {\r\n      try {\r\n        const res = await api.getFaqDetail(this.faqId)\r\n        this.faqDetail = res.data\r\n\r\n        // 详情加载成功后\r\n        this.recordView()\r\n        this.checkStatus()\r\n      } catch (error) {\r\n        console.error('加载FAQ详情失败:', error)\r\n        uni.showToast({\r\n          title: '加载失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    async checkStatus() {\r\n      const token = uni.getStorageSync('token');\r\n      if (!token) return;\r\n\r\n      try {\r\n        const params = {\r\n          item_type: 'faq',\r\n          item_id: parseInt(this.faqId)\r\n        };\r\n        const [likeRes, collectRes] = await Promise.all([\r\n          api.checkInteractionStatus({ ...params, actions: ['like'] }),\r\n          api.checkCollectionStatus(params)\r\n        ]);\r\n\r\n        if (likeRes.data.like) {\r\n          this.isLiked = true;\r\n        }\r\n        if (collectRes.data.is_collected) {\r\n          this.isCollected = true;\r\n        }\r\n      } catch (error) {\r\n        console.error('检查状态失败:', error);\r\n      }\r\n    },\r\n\r\n    async recordView() {\r\n      const token = uni.getStorageSync('token');\r\n      if (!token) {\r\n        return; // 未登录，不记录\r\n      }\r\n      \r\n      try {\r\n        await api.recordView({\r\n          item_type: 'faq',\r\n          item_id: parseInt(this.faqId)\r\n          // 后端会从token中解析user_id\r\n        });\r\n        if (this.faqDetail) {\r\n          this.faqDetail.view_count++;\r\n        }\r\n      } catch (error) {\r\n        console.error('记录浏览失败:', error);\r\n      }\r\n    },\r\n\r\n    async toggleLike() {\r\n      const token = uni.getStorageSync('token');\r\n      if (!token) {\r\n        this.promptLogin('点赞');\r\n        return;\r\n      }\r\n      try {\r\n        const res = await api.toggleInteraction({\r\n          action: 'like',\r\n          item_type: 'faq',\r\n          item_id: parseInt(this.faqId)\r\n        });\r\n        \r\n        this.isLiked = res.data.action === 'added';\r\n        this.faqDetail.like_count = res.data.current_count;\r\n        \r\n        uni.showToast({\r\n          title: res.data.message,\r\n          icon: 'none'\r\n        });\r\n      } catch (error) {\r\n        console.error('点赞操作失败:', error);\r\n      }\r\n    },\r\n\r\n    async toggleCollect() {\r\n      const token = uni.getStorageSync('token');\r\n      if (!token) {\r\n        this.promptLogin('收藏');\r\n        return;\r\n      }\r\n      try {\r\n        const res = await api.toggleCollection({\r\n          item_type: 'faq',\r\n          item_id: parseInt(this.faqId)\r\n        });\r\n        \r\n        this.isCollected = res.data.action === 'added';\r\n        this.faqDetail.collect_count = res.data.current_collect_count;\r\n        \r\n        uni.showToast({\r\n          title: res.data.message,\r\n          icon: 'none'\r\n        });\r\n      } catch (error) {\r\n        console.error('收藏操作失败:', error);\r\n      }\r\n    },\r\n\r\n    onShareAppMessage() {\r\n      return {\r\n        title: this.faqDetail.question,\r\n        path: `/pages/faq/detail?id=${this.faqId}`,\r\n        imageUrl: '' \r\n      };\r\n    },\r\n\r\n    promptLogin(action) {\r\n      uni.showModal({\r\n        title: '请先登录',\r\n        content: `登录后才能${action}哦`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.switchTab({\r\n              url: '/pages/profile/index'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    formatDate(dateStr) {\r\n      const date = new Date(dateStr)\r\n      const year = date.getFullYear()\r\n      const month = ('0' + (date.getMonth() + 1)).slice(-2)\r\n      const day = ('0' + date.getDate()).slice(-2)\r\n      return `${year}年${month}月${day}日`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.faq-detail-page {\r\n  background-color: #f4f5f7;\r\n  min-height: 100vh;\r\n  padding: 30rpx;\r\n  padding-bottom: 180rpx; // 为悬浮按钮留出空间\r\n}\r\n\r\n.content {\r\n  width: 100%;\r\n}\r\n\r\n.qa-card {\r\n  background: #ffffff;\r\n  border-radius: 16rpx;\r\n  padding: 40rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.question-section, .answer-section {\r\n  position: relative;\r\n  padding-left: 60rpx;\r\n  line-height: 1.7;\r\n}\r\n\r\n.question-section {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.answer-section {\r\n  font-size: 30rpx;\r\n  color: #555;\r\n  padding-bottom: 40rpx;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.question-section::before, .answer-section::before {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #fff;\r\n}\r\n\r\n.question-section::before {\r\n  content: '问';\r\n  background-color: #007bff;\r\n}\r\n\r\n.answer-section::before {\r\n  content: '答';\r\n  background-color: #28a745;\r\n}\r\n\r\n.meta-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding-top: 30rpx;\r\n  font-size: 26rpx;\r\n  color: #999;\r\n}\r\n\r\n.disclaimer {\r\n  margin-top: 40rpx;\r\n  padding: 20rpx;\r\n  font-size: 24rpx;\r\n  color: #aaa;\r\n  line-height: 1.6;\r\n  text-align: center;\r\n}\r\n\r\n.loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200rpx;\r\n  color: #999;\r\n}\r\n\r\n.action-bar-sticky {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 20rpx 0;\r\n  padding-bottom: constant(safe-area-inset-bottom);\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10px);\r\n  border-top: 1rpx solid #f0f0f0;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 100;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: none;\r\n  border: none;\r\n  padding: 10rpx 0;\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  line-height: 1.5;\r\n  transition: color 0.2s ease-in-out, transform 0.1s ease;\r\n\r\n  &::after {\r\n    border: none;\r\n  }\r\n  \r\n  &:active {\r\n    transform: scale(0.9);\r\n  }\r\n  \r\n  &.active {\r\n    color: #DC143C;\r\n  }\r\n  \r\n  .icon {\r\n    font-size: 44rpx;\r\n    margin-bottom: 6rpx;\r\n    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);\r\n    opacity: 0.7;\r\n    filter: grayscale(80%);\r\n  }\r\n  \r\n  &.active .icon {\r\n    transform: scale(1.1);\r\n    opacity: 1;\r\n    filter: grayscale(0%);\r\n  }\r\n  \r\n  .action-text {\r\n    font-size: 24rpx;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=72665da3&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=72665da3&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051462\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/faq/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=72665da3&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=72665da3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"72665da3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/faq/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=72665da3&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.faqDetail ? _vm.formatDate(_vm.faqDetail.answer_date) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\""], "sourceRoot": ""}