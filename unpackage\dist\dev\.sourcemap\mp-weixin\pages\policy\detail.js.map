{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/policy/detail.vue?a0e9", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/policy/detail.vue?eaac", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/policy/detail.vue?a1aa", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/policy/detail.vue?78b6", "uni-app:///pages/policy/detail.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/policy/detail.vue?e691", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/policy/detail.vue?104f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "policyDetail", "policyId", "isLiked", "isCollected", "guideSteps", "title", "desc", "onLoad", "methods", "loadPolicyDetail", "api", "res", "console", "uni", "icon", "checkStatus", "token", "params", "item_type", "item_id", "Promise", "actions", "likeRes", "collectRes", "recordView", "userInfo", "toggleLike", "action", "toggleCollect", "consultPolicy", "url", "promptLogin", "content", "success", "formatDate", "openAttachment", "filePath", "showMenu", "fail", "onShareAppMessage", "path", "imageUrl"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC2Dr4B;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA;IAEA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAR;kBACAS;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACAC,aACAV;kBAAAW;gBAAA,KACAX,2CACA;cAAA;gBAAA;gBAAA;gBAHAY;gBAAAC;gBAKA;kBACA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAS;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAb;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAF;kBACAQ;kBACAC;kBACA;gBACA;cAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAV;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAN;kBACAiB;kBACAT;kBACAC;gBACA;cAAA;gBAJAR;gBAMA;gBACA;gBAEAE;kBACAR;kBACAS;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAZ;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAN;kBACAQ;kBACAC;gBACA;cAAA;gBAHAR;gBAKA;gBACA;gBAEAE;kBACAR;kBACAS;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiB;MACAhB;QACAiB;MACA;IACA;IAEAC;MACAlB;QACAR;QACA2B;QACAC;UACA;YACApB;cACAiB;YACA;UACA;QACA;MACA;IACA;IAEAI;MACA;IACA;IAEAC;MACA;MACAtB;QAAAR;MAAA;MACAQ;QACAiB;QACAG;UACA;UACApB;YACAuB;YACAC;YACAJ;cACApB;YACA;YACAyB;cACAzB;cACAA;gBACAR;gBACAS;cACA;cACAF;YACA;UACA;QACA;QACA0B;UACAzB;UACAA;YACAR;YACAS;UACA;UACAF;QACA;MACA;IACA;IAEA2B;MACA;QACAlC;QACAmC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1RA;AAAA;AAAA;AAAA;AAA4oD,CAAgB,i/CAAG,EAAC,C;;;;;;;;;;;ACAhqD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/policy/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/policy/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=f4b705b6&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=f4b705b6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f4b705b6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/policy/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=f4b705b6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.policyDetail\n    ? _vm.formatDate(_vm.policyDetail.publish_date)\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"policy-detail\">\n    <view v-if=\"policyDetail\" class=\"content\">\n    \n\n      <!-- 政策内容 -->\n      <view class=\"policy-container\">\n        <!-- 标题部分 -->\n        <view class=\"title-section\">\n          <view class=\"title\">{{ policyDetail.title }}</view>\n          <view class=\"meta-info\">\n            <text class=\"publish-date\">{{ formatDate(policyDetail.publish_date) }}</text>\n            <text class=\"stats-bar\"> | {{ policyDetail.view_count }}人阅读</text>\n          </view>\n        </view>\n\n        <!-- 分隔线 -->\n        <view class=\"divider\"></view>\n\n        <!-- 正文部分 -->\n        <view class=\"content-section\">\n          <rich-text :nodes=\"policyDetail.content\" class=\"content-text\"></rich-text>\n        </view>\n      </view>\n\n      <!-- 附件 -->\n      <view v-if=\"policyDetail.attachment_url\" class=\"attachment-section\">\n        <view class=\"attachment-title\">附件：</view>\n        <view class=\"attachment-item\" @click=\"openAttachment\">\n          <text class=\"attachment-icon\">📎</text>\n          <text class=\"attachment-name\">{{ policyDetail.attachment_name || '点击查看附件' }}</text>\n        </view>\n      </view>\n\n    </view>\n    \n    <!-- 悬浮操作栏 -->\n    <view v-if=\"policyDetail\" class=\"action-bar-sticky\">\n      <button @click=\"toggleLike\" class=\"action-btn\" :class=\"{ active: isLiked }\">\n        <text class=\"icon\">👍</text>\n        <text class=\"action-text\">{{ policyDetail.like_count > 0 ? policyDetail.like_count : '点赞' }}</text>\n      </button>\n      <button @click=\"toggleCollect\" class=\"action-btn\" :class=\"{ active: isCollected }\">\n        <text class=\"icon\">{{ isCollected ? '⭐' : '☆' }}</text>\n        <text class=\"action-text\">{{ policyDetail.collect_count > 0 ? policyDetail.collect_count : '收藏' }}</text>\n      </button>\n      <button open-type=\"share\" class=\"action-btn\">\n        <text class=\"icon\">📤</text>\n        <text class=\"action-text\">{{ policyDetail.forward_count > 0 ? policyDetail.forward_count : '分享' }}</text>\n      </button>\n    </view>\n\n    <view v-else class=\"loading\">\n      <text>加载中...</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport api from '@/utils/api.js'\n\nexport default {\n  data() {\n    return {\n      policyDetail: null,\n      policyId: null,\n      isLiked: false,\n      isCollected: false,\n      guideSteps: [\n        {\n          title: '准备材料',\n          desc: '根据业务类型准备相应的申请材料和证明文件'\n        },\n        {\n          title: '提交申请',\n          desc: '通过银行或外汇管理部门指定渠道提交申请'\n        },\n        {\n          title: '审核批准',\n          desc: '相关部门进行审核，符合条件的予以批准'\n        },\n        {\n          title: '办理业务',\n          desc: '获得批准后按照规定办理相关业务手续'\n        }\n      ]\n    }\n  },\n\n  onLoad(options) {\n    this.policyId = options.id\n    if (this.policyId) {\n      this.loadPolicyDetail()\n    }\n  },\n\n  methods: {\n    async loadPolicyDetail() {\n      try {\n        const res = await api.getPolicyDetail(this.policyId)\n        this.policyDetail = res.data\n        \n        // 详情加载成功后，开始检查状态和记录浏览\n        this.recordView()\n        this.checkStatus()\n      } catch (error) {\n        console.error('加载政策详情失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    async checkStatus() {\n      const token = uni.getStorageSync('token');\n      if (!token) return;\n\n      try {\n        const params = {\n          item_type: 'policy',\n          item_id: parseInt(this.policyId)\n        };\n        const [likeRes, collectRes] = await Promise.all([\n          api.checkInteractionStatus({ ...params, actions: ['like'] }),\n          api.checkCollectionStatus(params)\n        ]);\n\n        if (likeRes.data.like) {\n          this.isLiked = true;\n        }\n        if (collectRes.data.is_collected) {\n          this.isCollected = true;\n        }\n      } catch (error) {\n        console.error('检查状态失败:', error);\n      }\n    },\n\n    async recordView() {\n      const token = uni.getStorageSync('token');\n      if (!token) {\n        return; // 未登录，不记录\n      }\n      \n      const userInfo = uni.getStorageSync('userInfo');\n      if (!userInfo || !userInfo.id) {\n        console.error('无法获取用户信息，无法记录浏览');\n        return;\n      }\n\n      try {\n        await api.recordView({\n          item_type: 'policy',\n          item_id: parseInt(this.policyId)\n          // 后端会从token中解析user_id，因此前端无需传递\n        });\n        if (this.policyDetail) {\n          this.policyDetail.view_count++;\n        }\n      } catch (error) {\n        console.error('记录浏览失败:', error);\n      }\n    },\n\n    async toggleLike() {\n      const token = uni.getStorageSync('token');\n      if (!token) {\n        this.promptLogin('点赞');\n        return;\n      }\n\n      try {\n        const res = await api.toggleInteraction({\n          action: 'like',\n          item_type: 'policy',\n          item_id: parseInt(this.policyId)\n        });\n        \n        this.isLiked = res.data.action === 'added';\n        this.policyDetail.like_count = res.data.current_count;\n        \n        uni.showToast({\n          title: res.data.message,\n          icon: 'none'\n        });\n      } catch (error) {\n        console.error('点赞操作失败:', error);\n      }\n    },\n\n    async toggleCollect() {\n      const token = uni.getStorageSync('token');\n      if (!token) {\n        this.promptLogin('收藏');\n        return;\n      }\n\n      try {\n        const res = await api.toggleCollection({\n          item_type: 'policy',\n          item_id: parseInt(this.policyId)\n        });\n        \n        this.isCollected = res.data.action === 'added';\n        this.policyDetail.collect_count = res.data.current_count;\n        \n        uni.showToast({\n          title: res.data.message,\n          icon: 'none'\n        });\n      } catch (error) {\n        console.error('收藏操作失败:', error);\n      }\n    },\n\n    consultPolicy() {\n      uni.switchTab({\n        url: '/pages/consultation/index'\n      })\n    },\n\n    promptLogin(action) {\n      uni.showModal({\n        title: '请先登录',\n        content: `登录后才能${action}哦`,\n        success: (res) => {\n          if (res.confirm) {\n            uni.switchTab({\n              url: '/pages/profile/index'\n            });\n          }\n        }\n      });\n    },\n\n    formatDate(dateStr) {\n      return new Date(dateStr).toLocaleDateString()\n    },\n\n    openAttachment() {\n      if (!this.policyDetail.attachment_url) return;\n      uni.showLoading({ title: '正在打开附件' });\n      uni.downloadFile({\n        url: this.policyDetail.attachment_url,\n        success: (res) => {\n          const filePath = res.tempFilePath;\n          uni.openDocument({\n            filePath: filePath,\n            showMenu: true,\n            success: () => {\n              uni.hideLoading();\n            },\n            fail: (err) => {\n              uni.hideLoading();\n              uni.showToast({\n                title: '打开附件失败',\n                icon: 'none'\n              });\n              console.error('打开附件失败', err);\n            }\n          });\n        },\n        fail: (err) => {\n          uni.hideLoading();\n          uni.showToast({\n            title: '下载附件失败',\n            icon: 'none'\n          });\n          console.error('下载附件失败', err);\n        }\n      });\n    },\n\n    onShareAppMessage() {\n      return {\n        title: this.policyDetail.title,\n        path: `/pages/policy/detail?id=${this.policyId}`,\n        imageUrl: '', // 你可以在这里设置一个默认的分享图片\n      };\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.policy-detail {\n  background: linear-gradient(180deg, #f0f9ff 0%, #f8fafc 40%, #f1f5f9 100%);\n  min-height: 100vh;\n  position: relative;\n}\n\n/* 顶部蓝色渐变背景 */\n.policy-detail::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 200rpx;\n  background: linear-gradient(180deg, \n    #1E90FF 0%, \n    #007bff 30%, \n    #60a5fa 60%, \n    rgba(96, 165, 250, 0.6) 80%, \n    rgba(147, 197, 253, 0.3) 90%, \n    transparent 100%\n  );\n  z-index: 1;\n}\n\n/* 重庆山城剪影装饰 */\n.policy-detail::after {\n  content: '';\n  position: absolute;\n  top: 140rpx;\n  left: 0;\n  right: 0;\n  height: 80rpx;\n  background: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E\") repeat-x;\n  background-size: 1200rpx 80rpx;\n  z-index: 2;\n  opacity: 0.7;\n}\n\n.content {\n  padding: 40rpx 30rpx 180rpx; /* 增加底部 padding 为悬浮按钮留出空间 */\n  position: relative;\n  z-index: 3;\n}\n\n.policy-container {\n  background: #ffffff;\n  border: 2rpx solid #e5e7eb;\n  border-radius: 16rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 30rpx;\n}\n\n.title-section {\n  margin-bottom: 30rpx;\n}\n\n.divider {\n  height: 2rpx;\n  background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #e5e7eb 80%, transparent 100%);\n  margin: 30rpx 0;\n}\n\n.title {\n  font-size: 44rpx;\n  font-weight: 600;\n  color: #2c3e50;\n  line-height: 1.4;\n  margin-bottom: 30rpx;\n  text-align: justify;\n}\n\n.meta-info {\n  color: #888;\n  font-size: 26rpx;\n}\n\n.stats-bar {\n  display: inline;\n  margin-left: 10rpx;\n}\n\n.content-section {\n  margin-top: 20rpx;\n}\n\n.content-text {\n  font-size: 32rpx;\n  line-height: 1.8;\n  color: #34495e;\n}\n\n.attachment-section {\n  margin-top: 40rpx;\n  padding: 30rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n}\n\n.attachment-title {\n  font-size: 30rpx;\n  color: #666;\n  margin-bottom: 20rpx;\n}\n\n.attachment-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background-color: #e9ecef;\n  border-radius: 8rpx;\n}\n\n.attachment-icon {\n  font-size: 36rpx;\n  margin-right: 16rpx;\n}\n\n.attachment-name {\n  color: #007bff;\n  font-size: 28rpx;\n  text-decoration: underline;\n}\n\n.action-bar-sticky {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: space-around;\n  padding: 20rpx 0;\n  padding-bottom: constant(safe-area-inset-bottom);\n  padding-bottom: env(safe-area-inset-bottom);\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 100;\n}\n\n.action-btn {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: none;\n  border: none;\n  padding: 10rpx 0;\n  font-size: 24rpx;\n  color: #666;\n  line-height: 1.5;\n  transition: color 0.2s ease-in-out, transform 0.1s ease;\n\n  &::after {\n    border: none;\n  }\n  \n  &:active {\n    transform: scale(0.9);\n  }\n  \n  &.active {\n    color: #3b82f6;\n  }\n  \n  .icon {\n    font-size: 44rpx;\n    margin-bottom: 6rpx;\n    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);\n    opacity: 0.7;\n    filter: grayscale(80%);\n  }\n  \n  &.active .icon {\n    transform: scale(1.1);\n    opacity: 1;\n    filter: grayscale(0%);\n  }\n  \n  .action-text {\n    font-size: 24rpx;\n  }\n}\n\n.loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200rpx;\n  color: #999;\n}\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=f4b705b6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=f4b705b6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051449\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}