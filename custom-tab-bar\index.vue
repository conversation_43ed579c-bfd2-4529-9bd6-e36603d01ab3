<template>
  <view class="custom-tab-bar">
    <!-- 顶部分割线 -->
    <image
      class="tab-top-line"
      src="/static/images/tabbar-top-line.png"
      mode="widthFix"
    />

    <!-- 标签栏内容 -->
    <view class="tab-content flex-row justify-around align-center">
      <view
        v-for="(item, index) in tabList"
        :key="index"
        class="tab-item flex-col align-center"
        :class="{ active: currentTab === index }"
        @tap="switchTab(index)"
      >
        <image
          class="tab-icon"
          :src="currentTab === index ? item.activeIcon : item.icon"
          mode="aspectFit"
        />
        <text
          class="tab-text text-xs text-center"
          :class="{ 'text-primary': currentTab === index, 'text-gray': currentTab !== index }"
        >
          {{ item.text }}
        </text>
      </view>
    </view>

    <!-- 底部背景 -->
    <image
      class="tab-bottom-bg"
      src="/static/images/tabbar-bottom-bg.png"
      mode="widthFix"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 0,
      tabList: [
        {
          icon: '/static/icons/home.svg',
          activeIcon: '/static/icons/home-active.svg',
          text: '首页',
          pagePath: '/pages/index/index'
        },
        {
          icon: '/static/icons/policy.svg',
          activeIcon: '/static/icons/policy-active.svg',
          text: '政策',
          pagePath: '/pages/policy/index'
        },
        {
          icon: '/static/icons/consult.svg',
          activeIcon: '/static/icons/consult-active.svg',
          text: '咨询',
          pagePath: '/pages/consultation/index'
        },
        {
          icon: '/static/icons/profile.svg',
          activeIcon: '/static/icons/profile-active.svg',
          text: '个人中心',
          pagePath: '/pages/profile/index'
        }
      ]
    }
  },

  watch: {
    $route: {
      handler(to) {
        if (!to || !to.path) return;
        const targetIndex = this.tabList.findIndex(item => item.pagePath === to.path);
        if (targetIndex !== -1) {
          this.currentTab = targetIndex;
        }
      },
      immediate: true, // 立即执行一次，初始化状态
    }
  },
  
  mounted() {
    // 初始化当前tab状态
    this.initCurrentTab();
  },

  methods: {
    // 初始化当前tab状态
    initCurrentTab() {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const currentRoute = '/' + currentPage.route;
        const targetIndex = this.tabList.findIndex(item => item.pagePath === currentRoute);
        if (targetIndex !== -1) {
          this.currentTab = targetIndex;
        }
      }
    },

    // 更新选中状态（供外部调用）
    updateSelected(index) {
      if (typeof index === 'number' && index >= 0 && index < this.tabList.length) {
        this.currentTab = index;
      }
    },

    // 切换tab
    switchTab(index) {
      if (this.currentTab === index) return;

      const tabItem = this.tabList[index];

      // 添加点击反馈
      uni.vibrateShort({
        type: 'light'
      });

      uni.switchTab({
        url: tabItem.pagePath,
        success: () => {
          this.currentTab = index;
        },
        fail: (err) => {
          console.error('切换Tab失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    // 获取当前tab索引
    getCurrentTabIndex() {
      return this.currentTab;
    },

    // 获取tab配置
    getTabConfig(index) {
      return this.tabList[index] || null;
    }
  }
}
</script>

<style lang="scss" scoped>
// 引入SCSS变量和混合器
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 166rpx;
  background-color: $background-color;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  @include flex-column;
}

.tab-top-line {
  width: 100%;
  height: 2rpx;
  flex-shrink: 0;
}

.tab-content {
  flex: 1;
  width: 100%;
  padding: $spacing-2 $spacing-1;
  min-height: 80rpx;
}

.tab-item {
  width: 188rpx;
  height: 80rpx;
  cursor: pointer;
  transition: $transition-fast;

  &:active {
    transform: scale(0.95);
    opacity: 0.8;
  }

  &.active {
    .tab-icon {
      transform: scale(1.05);
    }

    .tab-text {
      font-weight: 600;
    }
  }
}

.tab-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: $spacing-1;
  transition: $transition-fast;
}

.tab-text {
  width: 100%;
  height: 28rpx;
  line-height: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  transition: $transition-fast;
}

.tab-bottom-bg {
  width: 100%;
  height: 68rpx;
  flex-shrink: 0;
}

// 适配安全区域
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .custom-tab-bar {
    padding-bottom: calc(env(safe-area-inset-bottom) + 0rpx);
  }
}

// 响应式适配
@include respond-to('sm') {
  .custom-tab-bar {
    height: 180rpx;
  }

  .tab-content {
    padding: $spacing-3 $spacing-2;
  }

  .tab-icon {
    width: 56rpx;
    height: 56rpx;
  }

  .tab-text {
    font-size: $text-sm;
  }
}
</style>