<template>
  <view class="custom-tab-bar">
    <view 
      v-for="(item, index) in tabList" 
      :key="index"
      class="tab-item"
      :class="{ active: currentTab === index }"
      @tap="switchTab(index)"
    >
      <view class="tab-icon">{{ item.icon }}</view>
      <view class="tab-text">{{ item.text }}</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 0,
      tabList: [
        {
          icon: '🏠',
          text: '首页',
          pagePath: '/pages/index/index'
        },
        {
          icon: '📋',
          text: '政策',
          pagePath: '/pages/policy/index'
        },
        {
          icon: '💬',
          text: '咨询',
          pagePath: '/pages/consultation/index'
        },
        {
          icon: '👤',
          text: '个人中心',
          pagePath: '/pages/profile/index'
        }
      ]
    }
  },

  watch: {
    $route: {
      handler(to) {
        if (!to || !to.path) return;
        const targetIndex = this.tabList.findIndex(item => item.pagePath === to.path);
        if (targetIndex !== -1) {
          this.currentTab = targetIndex;
        }
      },
      immediate: true, // 立即执行一次，初始化状态
    }
  },
  
  methods: {
    updateSelected(index) {
      if (typeof index === 'number') {
        this.currentTab = index
      }
    },
    
    switchTab(index) {
      if (this.currentTab === index) return
      
      const tabItem = this.tabList[index]
      
      uni.switchTab({
        url: tabItem.pagePath,
        fail: (err) => {
          console.error('切换Tab失败:', err)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: #FFFFFF;
  border-top: 1rpx solid #E5E5E5;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 5rpx;
  transition: all 0.3s ease;
  
  &.active {
    .tab-icon {
      transform: scale(1.1);
    }
    
    .tab-text {
      color: #1E90FF;
      font-weight: 600;
    }
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.tab-icon {
  font-size: 48rpx;
  line-height: 1;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.tab-text {
  font-size: 22rpx;
  color: #999999;
  line-height: 1;
  transition: all 0.3s ease;
  text-align: center;
}
</style> 