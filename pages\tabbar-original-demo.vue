<template>
  <view class="demo-page">
    <!-- 页面头部 -->
    <view class="header bg-primary text-white p-3 text-center">
      <text class="text-xl font-bold">原设计底部菜单栏演示</text>
    </view>

    <!-- 演示说明 -->
    <view class="content p-3">
      <view class="info-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">设计还原说明</text>
        <view class="info-list">
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 完全按照原设计的HTML结构和CSS样式</text>
          </view>
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 使用原设计的类名：group_7, image_3, list_1等</text>
          </view>
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 保持原设计的尺寸：750rpx × 166rpx</text>
          </view>
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 图片资源已本地化，提高加载速度</text>
          </view>
          <view class="info-item">
            <text class="text-sm text-dark">✅ 集成SCSS变量，便于后期维护</text>
          </view>
        </view>
      </view>

      <!-- 当前状态 -->
      <view class="status-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">当前状态</text>
        <view class="status-info">
          <view class="status-item flex-between mb-2">
            <text class="text-base text-dark">当前选中Tab:</text>
            <text class="text-base text-primary font-bold">{{ getCurrentTabName() }}</text>
          </view>
          <view class="status-item flex-between mb-2">
            <text class="text-base text-dark">Tab索引:</text>
            <text class="text-base text-primary font-bold">{{ getCurrentTabIndex() }}</text>
          </view>
          <view class="status-item flex-between">
            <text class="text-base text-dark">激活颜色:</text>
            <text class="text-sm" style="color: rgba(31,115,255,1.000000);">rgba(31,115,255,1.000000)</text>
          </view>
        </view>
      </view>

      <!-- Tab配置信息 -->
      <view class="config-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">Tab配置信息</text>
        <view class="config-list">
          <view 
            v-for="(item, index) in tabConfig" 
            :key="index"
            class="config-item p-2 mb-2 rounded"
            :class="{ 'bg-primary': index === 2, 'bg-gray': index !== 2 }"
          >
            <view class="flex-between align-center">
              <view class="flex-row align-center">
                <image 
                  class="config-icon mr-2" 
                  :src="item.lanhuimage0"
                  mode="aspectFit"
                />
                <view class="config-info">
                  <text 
                    class="text-sm block mb-1"
                    :class="{ 'text-white': index === 2, 'text-dark': index !== 2 }"
                  >
                    {{ item.lanhutext0 }}
                  </text>
                  <text 
                    class="text-xs"
                    :style="{ color: item.lanhufontColor0 }"
                  >
                    {{ item.lanhufontColor0 }}
                  </text>
                </view>
              </view>
              <text 
                class="text-xs"
                :class="{ 'text-white': index === 2, 'text-gray': index !== 2 }"
              >
                索引: {{ index }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 样式对比 -->
      <view class="compare-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">样式对比</text>
        
        <!-- 原设计样式 -->
        <view class="compare-section mb-3">
          <text class="text-base text-dark mb-2">原设计样式特点:</text>
          <view class="feature-list">
            <view class="feature-item mb-1">
              <text class="text-sm text-gray">• 固定定位：position: absolute</text>
            </view>
            <view class="feature-item mb-1">
              <text class="text-sm text-gray">• 精确尺寸：750rpx × 166rpx</text>
            </view>
            <view class="feature-item mb-1">
              <text class="text-sm text-gray">• 三层结构：顶部线条 + 内容区 + 底部背景</text>
            </view>
            <view class="feature-item mb-1">
              <text class="text-sm text-gray">• 图标居中：margin-left: 70rpx</text>
            </view>
            <view class="feature-item">
              <text class="text-sm text-gray">• 动态颜色：根据状态切换颜色</text>
            </view>
          </view>
        </view>

        <!-- 优化改进 -->
        <view class="compare-section">
          <text class="text-base text-dark mb-2">优化改进:</text>
          <view class="improvement-list">
            <view class="improvement-item mb-1">
              <text class="text-sm text-primary">✓ 改为fixed定位，适配更多场景</text>
            </view>
            <view class="improvement-item mb-1">
              <text class="text-sm text-primary">✓ 添加安全区域适配</text>
            </view>
            <view class="improvement-item mb-1">
              <text class="text-sm text-primary">✓ 集成SCSS变量系统</text>
            </view>
            <view class="improvement-item mb-1">
              <text class="text-sm text-primary">✓ 添加点击动画效果</text>
            </view>
            <view class="improvement-item">
              <text class="text-sm text-primary">✓ 完善的状态管理</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 技术细节 -->
      <view class="tech-card bg-white rounded-lg p-3">
        <text class="text-lg font-bold text-primary mb-2">技术实现细节</text>
        <view class="tech-list">
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">数据结构:</text>
            <text class="text-xs text-gray block mt-1">使用loopData0数组，包含图标路径、文字、颜色等配置</text>
          </view>
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">状态管理:</text>
            <text class="text-xs text-gray block mt-1">通过updateTabState方法动态更新图标和颜色</text>
          </view>
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">图片资源:</text>
            <text class="text-xs text-gray block mt-1">所有图片已下载到/static/images/tabbar/目录</text>
          </view>
          <view class="tech-item">
            <text class="text-sm text-dark font-bold">样式系统:</text>
            <text class="text-xs text-gray block mt-1">保持原设计样式，同时集成SCSS变量</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部占位 -->
    <view class="tabbar-placeholder"></view>

    <!-- 自定义TabBar -->
    <custom-tab-bar ref="tabbar" />
  </view>
</template>

<script>
import CustomTabBar from '@/custom-tab-bar/index.vue'

export default {
  name: 'TabbarOriginalDemo',
  components: {
    CustomTabBar
  },
  data() {
    return {
      tabConfig: [
        {
          lanhuimage0: '/static/images/tabbar/home-icon.png',
          lanhutext0: '首页',
          lanhufontColor0: 'rgba(147,152,160,1.000000)',
        },
        {
          lanhuimage0: '/static/images/tabbar/policy-icon.png',
          lanhutext0: '政策',
          lanhufontColor0: 'rgba(147,152,160,1.000000)',
        },
        {
          lanhuimage0: '/static/images/tabbar/consult-icon-active.png',
          lanhutext0: '咨询',
          lanhufontColor0: 'rgba(31,115,255,1.000000)',
        },
        {
          lanhuimage0: '/static/images/tabbar/profile-icon.png',
          lanhutext0: '我的',
          lanhufontColor0: 'rgba(147,152,160,1.000000)',
        }
      ]
    }
  },
  
  methods: {
    getCurrentTabIndex() {
      return this.$refs.tabbar ? this.$refs.tabbar.getCurrentTabIndex() : 2;
    },
    
    getCurrentTabName() {
      const index = this.getCurrentTabIndex();
      return this.tabConfig[index] ? this.tabConfig[index].lanhutext0 : '咨询';
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.demo-page {
  min-height: 100vh;
  background-color: $gray-light;
}

.content {
  padding-bottom: 200rpx; // 为tabbar留出空间
}

.info-card,
.status-card,
.config-card,
.compare-card,
.tech-card {
  @include card-shadow('md');
}

.config-icon {
  width: 32rpx;
  height: 32rpx;
}

.config-info {
  .block {
    display: block;
  }
}

.tabbar-placeholder {
  height: 200rpx; // 为tabbar留出空间
}

// 响应式适配
@include respond-to('md') {
  .status-info,
  .config-list {
    .flex-between {
      justify-content: space-between;
    }
  }
}
</style>
