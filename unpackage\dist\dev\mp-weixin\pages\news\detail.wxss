.news-detail-page.data-v-6803dca4 {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding-bottom: 180rpx;
}
.content.data-v-6803dca4 {
  padding: 30rpx;
}
.article-card.data-v-6803dca4 {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.article-header.data-v-6803dca4 {
  margin-bottom: 30rpx;
}
.article-title.data-v-6803dca4 {
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1.4;
  color: #333;
  margin-bottom: 20rpx;
}
.article-meta.data-v-6803dca4 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}
.category.data-v-6803dca4 {
  background: #3b82f6;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.publish-date.data-v-6803dca4 {
  color: #999;
  font-size: 26rpx;
}
.article-stats.data-v-6803dca4 {
  display: flex;
  gap: 30rpx;
  font-size: 26rpx;
  color: #888;
}
.cover-image.data-v-6803dca4 {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}
.article-body.data-v-6803dca4 {
  font-size: 32rpx;
  line-height: 1.8;
  color: #333;
}
.loading.data-v-6803dca4 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
}
.action-bar-sticky.data-v-6803dca4 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.action-btn.data-v-6803dca4 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  transition: color 0.2s ease-in-out, -webkit-transform 0.1s ease;
  transition: color 0.2s ease-in-out, transform 0.1s ease;
  transition: color 0.2s ease-in-out, transform 0.1s ease, -webkit-transform 0.1s ease;
}
.action-btn.data-v-6803dca4::after {
  border: none;
}
.action-btn.data-v-6803dca4:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.action-btn.active.data-v-6803dca4 {
  color: #3b82f6;
}
.action-btn .icon.data-v-6803dca4 {
  font-size: 44rpx;
  margin-bottom: 6rpx;
  transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  opacity: 0.7;
  -webkit-filter: grayscale(80%);
          filter: grayscale(80%);
}
.action-btn.active .icon.data-v-6803dca4 {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  opacity: 1;
  -webkit-filter: grayscale(0%);
          filter: grayscale(0%);
}
.action-btn .action-text.data-v-6803dca4 {
  font-size: 24rpx;
}

