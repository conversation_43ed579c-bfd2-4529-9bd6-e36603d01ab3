{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/consultation/index.vue?541d", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/consultation/index.vue?28b7", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/consultation/index.vue?e409", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/consultation/index.vue?6a5c", "uni-app:///pages/consultation/index.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/consultation/index.vue?56a5", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/consultation/index.vue?91d0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "CustomTabBar", "data", "consultationType", "selectedBank", "showBankPicker", "bankList", "formData", "contact_name", "contact_phone", "content", "errors", "computed", "canSubmit", "onLoad", "onShow", "methods", "selectType", "selectBank", "loadBankList", "api", "res", "console", "id", "name", "contact_person", "phone", "submitConsultation", "uni", "title", "icon", "submitData", "type", "response", "duration", "errorMessage", "validateName", "validatePhone", "validateContent", "validateBank", "validateAll"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2Ip4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QAAAL;QAAAC;QAAAC;;MAEA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QAAA;MAAA;MAEA;IACA;EACA;EACAI;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;gBACA,kBACA;kBAAAC;kBAAAC;kBAAAC;kBAAAC;gBAAA,GACA;kBAAAH;kBAAAC;kBAAAC;kBAAAC;gBAAA,GACA;kBAAAH;kBAAAC;kBAAAC;kBAAAC;gBAAA,GACA;kBAAAH;kBAAAC;kBAAAC;kBAAAC;gBAAA,GACA;kBAAAH;kBAAAC;kBAAAC;kBAAAC;gBAAA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKAF;kBAAAC;gBAAA;;gBAEA;gBACAE;kBACAvB;kBACAC;kBACAC;kBACAsB;gBACA,GAEA;gBACA;kBACAD;gBACA;gBAEAT;;gBAEA;gBAAA;gBAAA,OACAF;cAAA;gBAAAa;gBACAX;gBAEAM;gBACAA;kBACAC;kBACAC;kBACAI;gBACA;;gBAEA;gBACA;kBACA1B;kBACAC;kBACAC;gBACA;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAkB;gBACAN;;gBAEA;gBACAa;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEAP;kBACAC;kBACAC;kBACAI;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAE;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACAX;UACAC;UACAC;QACA;QACA;MACA;MACA;IACA;IAEAU;MACA;MACA;MACA;MAEA;QAAA;MAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9VA;AAAA;AAAA;AAAA;AAA2oD,CAAgB,g/CAAG,EAAC,C;;;;;;;;;;;ACA/pD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/consultation/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/consultation/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=17138ff6&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=17138ff6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"17138ff6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/consultation/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=17138ff6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.content.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showBankPicker = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showBankPicker = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showBankPicker = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"consultation-page\">\n    <!-- 咨询类型选择 -->\n    <view class=\"type-section section\">\n      <view class=\"section-title\">咨询类型</view>\n      <view class=\"type-options\">\n        <view \n          class=\"type-option\" \n          :class=\"{ active: consultationType === 'business_consult' }\"\n          @click=\"selectType('business_consult')\"\n        >\n          <view class=\"option-icon\">💼</view>\n          <text class=\"option-title\">业务咨询</text>\n          <text class=\"option-desc\">一般性外汇政策咨询</text>\n        </view>\n        <view \n          class=\"type-option\" \n          :class=\"{ active: consultationType === 'policy_demand' }\"\n          @click=\"selectType('policy_demand')\"\n        >\n          <view class=\"option-icon\">🏦</view>\n          <text class=\"option-title\">政策需求</text>\n          <text class=\"option-desc\">向银行机构提出政策支持与服务需求</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 银行选择（政策需求时显示） -->\n    <view class=\"bank-section section\" v-if=\"consultationType === 'policy_demand'\">\n      <view class=\"section-title\">选择银行机构</view>\n      <view class=\"bank-selector\" @click=\"showBankPicker = true\">\n        <text class=\"bank-selected\" v-if=\"selectedBank\">{{ selectedBank.name }}</text>\n        <text class=\"bank-placeholder\" v-else>请选择银行机构</text>\n        <text class=\"bank-arrow\">></text>\n      </view>\n    </view>\n\n    <!-- 咨询表单 -->\n    <view class=\"form-section section\">\n      <view class=\"section-title\">咨询信息</view>\n      <view class=\"form-content\">\n        <view class=\"form-item\">\n          <text class=\"form-label\">联系人姓名 <text class=\"required\">*</text></text>\n          <input \n            class=\"form-input\" \n            :class=\"{ error: errors.contact_name }\"\n            v-model=\"formData.contact_name\" \n            placeholder=\"请输入您的姓名\"\n            maxlength=\"20\"\n            @blur=\"validateName\"\n            @input=\"validateName\"\n          />\n          <text class=\"error-text\" v-if=\"errors.contact_name\">{{ errors.contact_name }}</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">联系电话 <text class=\"required\">*</text></text>\n          <input \n            class=\"form-input\" \n            :class=\"{ error: errors.contact_phone }\"\n            v-model=\"formData.contact_phone\" \n            placeholder=\"请输入联系电话\"\n            type=\"number\"\n            maxlength=\"11\"\n            @blur=\"validatePhone\"\n            @input=\"validatePhone\"\n          />\n          <text class=\"error-text\" v-if=\"errors.contact_phone\">{{ errors.contact_phone }}</text>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"form-label\">咨询内容 <text class=\"required\">*</text></text>\n          <textarea \n            class=\"form-textarea\" \n            :class=\"{ error: errors.content }\"\n            v-model=\"formData.content\" \n            placeholder=\"请详细描述您的咨询内容，我们将尽快为您回复\"\n            maxlength=\"600\"\n            @blur=\"validateContent\"\n            @input=\"validateContent\"\n          ></textarea>\n          <view class=\"char-count\">{{ formData.content.length }}/600</view>\n          <text class=\"error-text\" v-if=\"errors.content\">{{ errors.content }}</text>\n        </view>\n        \n        <!-- 提交按钮移到表单内 -->\n        <view class=\"form-submit\">\n          <view \n            class=\"submit-btn\" \n            :class=\"{ disabled: !canSubmit }\"\n            @click=\"submitConsultation\"\n          >\n            提交咨询\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 重庆特色提示 -->\n    <view class=\"tip-section section cq-decoration\">\n      <view class=\"tip-header\">\n        <text class=\"tip-icon\">💡</text>\n        <text class=\"tip-title\">温馨提示</text>\n      </view>\n      <view class=\"tip-content\">\n        <text class=\"tip-text\">• 我们将及时回复您的咨询</text>\n        <text class=\"tip-text\">• 如需紧急咨询，请直接联系相关银行机构</text>\n        <text class=\"tip-text\">• 国家外汇管理局重庆市分局咨询热线：023-********</text>\n        <text class=\"tip-text\">• 服务时间：周一至周五（节假日除外）8:30-12:00、14:00-17:30</text>\n      </view>\n    </view>\n\n    <!-- 银行选择弹窗 -->\n    <view class=\"modal-overlay\" v-if=\"showBankPicker\" @click=\"showBankPicker = false\">\n                <view class=\"bank-picker\" catchtap=\"true\">\n        <view class=\"picker-header\">\n          <text class=\"picker-title\">选择银行机构</text>\n          <text class=\"picker-close\" @click=\"showBankPicker = false\">×</text>\n        </view>\n        <scroll-view class=\"picker-list\" scroll-y>\n          <view \n            class=\"picker-item\" \n            v-for=\"bank in bankList\" \n            :key=\"bank.id\"\n            @click=\"selectBank(bank)\"\n          >\n            <text class=\"bank-name\">{{ bank.name }}</text>\n            <text class=\"bank-contact\">{{ bank.contact_person }} - {{ bank.phone }}</text>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n    \n    <!-- 自定义TabBar -->\n    <custom-tab-bar />\n  </view>\n</template>\n\n<script>\nimport { api } from '@/utils/api'\nimport CustomTabBar from '@/custom-tab-bar/index.vue'\n\nexport default {\n  components: {\n    CustomTabBar\n  },\n  data() {\n    return {\n      consultationType: 'business_consult',\n      selectedBank: null,\n      showBankPicker: false,\n      bankList: [],\n      formData: {\n        contact_name: '',\n        contact_phone: '',\n        content: ''\n      },\n      errors: {}\n    }\n  },\n  computed: {\n    canSubmit() {\n      const { contact_name, contact_phone, content } = this.formData\n      \n      // 详细的字段验证\n      const nameValid = contact_name.trim().length >= 2 && contact_name.trim().length <= 20\n      const phoneValid = /^1[3-9]\\d{9}$/.test(contact_phone.trim())\n      const contentValid = content.trim().length >= 10 && content.trim().length <= 600\n      \n      // 银行选择验证（政策需求时必须选择银行）\n      const bankValid = this.consultationType === 'business_consult' || this.selectedBank\n      \n      // 没有错误信息\n      const noErrors = Object.values(this.errors).every(error => !error)\n      \n      return nameValid && phoneValid && contentValid && bankValid && noErrors\n    }\n  },\n  onLoad() {\n    this.loadBankList()\n  },\n  onShow() {\n    if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {\n      this.$root.$mp.page.getTabBar().$vm.updateSelected(2)\n    }\n  },\n  methods: {\n    selectType(type) {\n      this.consultationType = type\n      if (type === 'business_consult') {\n        this.selectedBank = null\n      }\n      // 清除错误信息\n      this.errors = {}\n    },\n\n    selectBank(bank) {\n      this.selectedBank = bank\n      this.showBankPicker = false\n    },\n\n    async loadBankList() {\n      try {\n        const res = await api.getBanks()\n        this.bankList = res.data?.items || []\n      } catch (error) {\n        console.error('加载银行列表失败:', error)\n        // 模拟数据\n        this.bankList = [\n          { id: 1, name: '中国银行重庆分行', contact_person: '张经理', phone: '023-********' },\n          { id: 2, name: '建设银行重庆分行', contact_person: '李经理', phone: '023-********' },\n          { id: 3, name: '工商银行重庆分行', contact_person: '王经理', phone: '023-********' },\n          { id: 4, name: '农业银行重庆分行', contact_person: '刘经理', phone: '023-********' },\n          { id: 5, name: '交通银行重庆分行', contact_person: '陈经理', phone: '023-********' }\n        ]\n      }\n    },\n\n    async submitConsultation() {\n      // 如果按钮被禁用，直接返回\n      if (!this.canSubmit) {\n        return\n      }\n      \n      // 进行全面校验\n      if (!this.validateAll()) {\n        uni.showToast({\n          title: '请检查并完善表单信息',\n          icon: 'none'\n        })\n        return\n      }\n\n      try {\n        uni.showLoading({ title: '提交中...' })\n\n        // 准备提交数据（暂时不包含用户ID）\n        const submitData = {\n          contact_name: this.formData.contact_name.trim(),\n          contact_phone: this.formData.contact_phone.trim(),\n          content: this.formData.content.trim(),\n          type: this.consultationType\n        }\n\n        // 如果是政策需求，添加银行ID\n        if (this.consultationType === 'policy_demand' && this.selectedBank) {\n          submitData.bank_id = this.selectedBank.id\n        }\n\n        console.log('提交的数据:', submitData)\n        \n        // 调用API提交数据\n        const response = await api.submitInquiry(submitData)\n        console.log('提交响应:', response)\n\n        uni.hideLoading()\n        uni.showToast({\n          title: '提交成功',\n          icon: 'success',\n          duration: 2000\n        })\n\n        // 重置表单和错误信息\n        this.formData = {\n          contact_name: '',\n          contact_phone: '',\n          content: ''\n        }\n        this.errors = {}\n        this.selectedBank = null\n        this.consultationType = 'business_consult'\n\n      } catch (error) {\n        uni.hideLoading()\n        console.error('提交咨询失败:', error)\n        \n        // 根据错误类型显示不同的提示\n        let errorMessage = '提交失败，请重试'\n        if (error.message) {\n          errorMessage = error.message\n        } else if (error.data && error.data.message) {\n          errorMessage = error.data.message\n        }\n        \n        uni.showToast({\n          title: errorMessage,\n          icon: 'none',\n          duration: 3000\n        })\n      }\n    },\n\n    validateName() {\n      const name = this.formData.contact_name.trim()\n      if (!name) {\n        this.errors.contact_name = '请输入联系人姓名'\n      } else if (name.length < 2) {\n        this.errors.contact_name = '姓名至少需要2个字符'\n      } else if (name.length > 20) {\n        this.errors.contact_name = '姓名不能超过20个字符'\n      } else {\n        this.errors.contact_name = ''\n      }\n    },\n\n    validatePhone() {\n      const phone = this.formData.contact_phone.trim()\n      const phoneRegex = /^1[3-9]\\d{9}$/\n      if (!phone) {\n        this.errors.contact_phone = '请输入联系电话'\n      } else if (!phoneRegex.test(phone)) {\n        this.errors.contact_phone = '请输入正确的手机号码'\n      } else {\n        this.errors.contact_phone = ''\n      }\n    },\n\n    validateContent() {\n      const content = this.formData.content.trim()\n      if (!content) {\n        this.errors.content = '请输入咨询内容'\n      } else if (content.length < 10) {\n        this.errors.content = '咨询内容至少需要10个字符'\n      } else if (content.length > 600) {\n        this.errors.content = '咨询内容不能超过600个字符'\n      } else {\n        this.errors.content = ''\n      }\n    },\n\n    validateBank() {\n      if (this.consultationType === 'policy_demand' && !this.selectedBank) {\n        uni.showToast({\n          title: '请选择银行机构',\n          icon: 'none'\n        })\n        return false\n      }\n      return true\n    },\n\n    validateAll() {\n      this.validateName()\n      this.validatePhone()\n      this.validateContent()\n      \n      const hasErrors = Object.values(this.errors).some(error => error !== '')\n      return !hasErrors && this.validateBank()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.consultation-page {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);\n  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */\n  position: relative;\n}\n\n/* 顶部重庆山城风格背景 */\n.consultation-page::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 300rpx;\n  background: linear-gradient(180deg, \n    #1E90FF 0%, \n    #4A90E2 25%, \n    #6F80FF 50%, \n    rgba(111, 128, 255, 0.7) 70%, \n    rgba(175, 184, 255, 0.4) 85%, \n    rgba(223, 228, 255, 0.2) 95%, \n    transparent 100%\n  );\n  z-index: 1;\n}\n\n/* 重庆山城剪影装饰 */\n.consultation-page::after {\n  content: '';\n  position: absolute;\n  top: 180rpx;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 100'%3E%3Cpath d='M0,80 L50,75 L100,70 L150,65 L200,60 L250,65 L300,55 L350,60 L400,50 L450,55 L500,45 L550,50 L600,40 L650,45 L700,35 L750,40 L800,30 L850,35 L900,25 L950,30 L1000,20 L1050,25 L1100,15 L1150,20 L1200,10 L1200,100 L0,100 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E\") repeat-x;\n  background-size: 1200rpx 100rpx;\n  z-index: 2;\n  opacity: 0.8;\n}\n\n.section {\n  background: white;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n  position: relative;\n  z-index: 3;\n}\n\n.section-title {\n  font-size: 34rpx;\n  font-weight: bold;\n  color: #333;\n  padding: 30rpx 30rpx 20rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.type-section {\n  margin: 0 30rpx 30rpx;\n  margin-top: 0;\n}\n\n.type-options {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 30rpx;\n  padding: 30rpx;\n}\n\n.type-option {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 40rpx 20rpx;\n  background: white;\n  border: 2rpx solid #e5e5e5;\n  border-radius: 16rpx;\n  transition: all 0.3s;\n}\n\n.type-option.active {\n  border-color: #1E90FF;\n  background: rgba(30, 144, 255, 0.05);\n}\n\n.option-icon {\n  font-size: 60rpx;\n  margin-bottom: 20rpx;\n}\n\n.option-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: var(--text-color);\n  margin-bottom: 12rpx;\n}\n\n.option-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.bank-section {\n  margin: 0 30rpx 30rpx;\n}\n\n.bank-selector {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 30rpx;\n  background: white;\n  border-radius: 12rpx;\n  border: 1rpx solid #e5e5e5;\n}\n\n.bank-selected {\n  font-size: 30rpx;\n  color: #333;\n}\n\n.bank-placeholder {\n  font-size: 30rpx;\n  color: #999;\n}\n\n.bank-arrow {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.form-section {\n  margin: 0 30rpx 30rpx;\n}\n\n.form-content {\n  padding: 30rpx;\n}\n\n.form-item {\n  margin-bottom: 40rpx;\n}\n\n.form-label {\n  display: block;\n  font-size: 30rpx;\n  color: #333;\n  margin-bottom: 16rpx;\n}\n\n.required {\n  color: #1E90FF;\n}\n\n.form-input {\n  width: 100%;\n  height: 88rpx;\n  padding: 0 24rpx;\n  background: #f8f8f8;\n  border-radius: 12rpx;\n  border: 1rpx solid #e5e5e5;\n  font-size: 30rpx;\n  box-sizing: border-box;\n  line-height: 88rpx;\n}\n\n.form-input:focus {\n  border-color: #1E90FF;\n  background: #fff;\n}\n\n.form-textarea {\n  width: 100%;\n  min-height: 200rpx;\n  padding: 24rpx;\n  background: #f8f8f8;\n  border-radius: 12rpx;\n  border: 1rpx solid #e5e5e5;\n  font-size: 30rpx;\n  box-sizing: border-box;\n  line-height: 1.6;\n}\n\n.form-textarea:focus {\n  border-color: #1E90FF;\n  background: #fff;\n}\n\n.char-count {\n  text-align: right;\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 12rpx;\n}\n\n.form-submit {\n  margin-top: 40rpx;\n  padding-top: 20rpx;\n}\n\n.submit-btn {\n  width: 100%;\n  padding: 32rpx 24rpx;\n  background: linear-gradient(45deg, #1E90FF, #4A90E2);\n  color: white;\n  text-align: center;\n  border-radius: 16rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  box-shadow: 0 4rpx 20rpx rgba(30, 144, 255, 0.3);\n  box-sizing: border-box;\n  border: none;\n  cursor: pointer;\n}\n\n.submit-btn:active:not(.disabled) {\n  transform: scale(0.98);\n  box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);\n}\n\n.submit-btn.disabled {\n  opacity: 0.5;\n  background: #ccc;\n  box-shadow: none;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.tip-section {\n  margin: 0 30rpx 30rpx;\n}\n\n.tip-header {\n  display: flex;\n  align-items: center;\n  padding: 30rpx 30rpx 20rpx;\n}\n\n.tip-icon {\n  font-size: 32rpx;\n  margin-right: 16rpx;\n}\n\n.tip-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #1E90FF;\n}\n\n.tip-content {\n  padding: 0 30rpx 30rpx;\n}\n\n.tip-text {\n  display: block;\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 12rpx;\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.bank-picker {\n  width: 600rpx;\n  max-height: 800rpx;\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.picker-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx;\n  border-bottom: 1rpx solid #e5e5e5;\n}\n\n.picker-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.picker-close {\n  font-size: 40rpx;\n  color: #999;\n}\n\n.picker-list {\n  max-height: 600rpx;\n}\n\n.picker-item {\n  padding: 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.picker-item:last-child {\n  border-bottom: none;\n}\n\n.bank-name {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.bank-contact {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.form-input.error,\n.form-textarea.error {\n  border-color: #ff4757;\n  background: #fff5f5;\n}\n\n.error-text {\n  font-size: 24rpx;\n  color: #ff4757;\n  margin-top: 8rpx;\n  display: block;\n}\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=17138ff6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=17138ff6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051464\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}