# 公共样式使用指南

## 项目配置

### 1. 文件结构
```
├── static/
│   └── css/
│       ├── common.css          # 公共样式文件
│       └── README.md           # 详细使用说明
├── App.vue                     # 全局引入公共样式
└── pages/                      # 各页面文件
```

### 2. 全局引入
在 `App.vue` 中已经全局引入了公共样式：
```scss
<style lang="scss">
/* 引入公共样式 */
@import url('./static/css/common.css');
</style>
```

这意味着所有页面都可以直接使用这些样式类，无需重复引入。

## 主要功能

### 1. 基础样式重置
- 统一的盒模型设置
- 标准化的字体设置
- 按钮样式重置

### 2. Flex布局工具类
- **方向控制**: `.flex-col`, `.flex-row`
- **水平对齐**: `.justify-start`, `.justify-center`, `.justify-end`, `.justify-between`, `.justify-around`, `.justify-evenly`
- **垂直对齐**: `.align-start`, `.align-center`, `.align-end`
- **组合布局**: `.flex-center`, `.flex-between`, `.flex-start`, `.flex-end`

### 3. 间距工具类
- **外边距**: `.m-0` 到 `.m-4`, `.mt-0` 到 `.mt-4`, `.mb-0` 到 `.mb-4`, 等
- **内边距**: `.p-0` 到 `.p-4`, `.pt-0` 到 `.pt-4`, `.pb-0` 到 `.pb-4`, 等
- 数值对应: 0, 10rpx, 20rpx, 30rpx, 40rpx

### 4. 文本工具类
- **对齐**: `.text-left`, `.text-center`, `.text-right`
- **大小**: `.text-xs`(20rpx), `.text-sm`(24rpx), `.text-base`(28rpx), `.text-lg`(32rpx), `.text-xl`(36rpx), `.text-2xl`(40rpx)
- **粗细**: `.font-normal`, `.font-bold`

### 5. 颜色工具类
- **文字颜色**: `.text-primary`, `.text-secondary`, `.text-gray`, `.text-dark`, `.text-white`
- **背景颜色**: `.bg-primary`, `.bg-secondary`, `.bg-white`, `.bg-gray`

### 6. 边框和圆角
- **边框**: `.border`, `.border-t`, `.border-b`, `.border-l`, `.border-r`
- **圆角**: `.rounded`(8rpx), `.rounded-lg`(16rpx), `.rounded-full`

### 7. 文本省略
- `.ellipsis` - 单行省略
- `.ellipsis-2` - 两行省略
- `.ellipsis-3` - 三行省略

## 实际应用示例

### 页面优化前后对比

**优化前** (需要大量自定义CSS):
```html
<view class="policy-item">
  <view class="policy-header">
    <text class="policy-title">标题</text>
    <view class="policy-category">分类</view>
  </view>
</view>

<style>
.policy-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.policy-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.policy-category {
  background-color: #1E90FF;
  color: white;
  font-size: 20rpx;
  padding: 10rpx;
  border-radius: 8rpx;
}
</style>
```

**优化后** (使用公共样式类):
```html
<view class="policy-item bg-white rounded-lg p-3 mb-2">
  <view class="policy-header flex-between mb-2">
    <text class="policy-title text-lg font-bold text-dark">标题</text>
    <view class="policy-category bg-primary text-white text-xs p-1 rounded">分类</view>
  </view>
</view>
```

### 常用布局模式

#### 1. 卡片布局
```html
<view class="bg-white rounded-lg p-3 mb-2">
  <view class="flex-between mb-2">
    <text class="text-lg font-bold text-dark">标题</text>
    <text class="text-sm text-gray">时间</text>
  </view>
  <text class="text-base text-gray ellipsis-2">内容描述...</text>
</view>
```

#### 2. 列表项布局
```html
<view class="flex-between p-3 border-b">
  <view class="flex-1">
    <text class="text-base font-bold text-dark ellipsis">项目名称</text>
    <text class="text-sm text-gray">项目描述</text>
  </view>
  <view class="bg-primary text-white text-xs p-1 rounded">状态</view>
</view>
```

#### 3. 按钮组布局
```html
<view class="flex-between p-3">
  <view class="bg-primary text-white p-2 rounded flex-center">
    <text class="text-sm">确认</text>
  </view>
  <view class="border border-primary p-2 rounded flex-center">
    <text class="text-sm text-primary">取消</text>
  </view>
</view>
```

## 使用建议

### 1. 优先使用工具类
- 减少自定义CSS的编写
- 保持样式的一致性
- 提高开发效率

### 2. 组合使用
- 多个工具类可以组合使用
- 例如: `flex-center bg-primary text-white p-2 rounded`

### 3. 响应式考虑
- 所有尺寸使用 `rpx` 单位
- 自动适配不同屏幕尺寸

### 4. 扩展原则
- 如需新增工具类，在 `common.css` 中添加
- 保持命名规范的一致性
- 更新相应的文档说明

## 测试页面

创建了 `pages/test-common-styles.vue` 测试页面，展示了所有工具类的使用效果，可以用来：
- 验证样式是否正常工作
- 查看各种样式的视觉效果
- 作为开发参考

## 注意事项

1. **全局生效**: 所有样式类在整个项目中都可以直接使用
2. **优先级**: 工具类样式优先级较高，可以覆盖大部分默认样式
3. **兼容性**: 样式已考虑uni-app的跨平台兼容性
4. **维护性**: 统一的样式管理，便于后期维护和修改

## 下一步

1. 在现有页面中逐步应用公共样式类
2. 减少页面中的自定义CSS代码
3. 根据实际使用情况扩展更多工具类
4. 建立团队的样式使用规范
