<template>
  <view class="faq-detail-page">
    <view v-if="faqDetail" class="content">
      
      <view class="qa-card" @tap="handleContentTap">
        <view class="question-section">
          <view v-html="processedQuestion"></view>
        </view>
        <view class="answer-section">
          <view v-html="processedAnswer"></view>
        </view>
        <view class="meta-info">
          <text class="meta-item">回答时间：{{ formatDate(faqDetail.answer_date) }}</text>
          <text class="meta-item">{{ faqDetail.view_count || 0 }}次浏览</text>
        </view>
      </view>

      <view class="disclaimer">
        说明：回答基于当时有效的外汇管理政策，如外汇管理政策发生变化，以最新政策为准，如有疑问请咨询外汇局或商业银行。
      </view>
      
    </view>

    <!-- 悬浮操作栏 -->
    <view v-if="faqDetail" class="action-bar-sticky">
      <button @click="toggleLike" class="action-btn" :class="{ active: isLiked }">
        <text class="icon">👍</text>
        <text class="action-text">{{ faqDetail.like_count > 0 ? faqDetail.like_count : '点赞' }}</text>
      </button>
      <button @click="toggleCollect" class="action-btn" :class="{ active: isCollected }">
        <text class="icon">{{ isCollected ? '⭐' : '☆' }}</text>
        <text class="action-text">{{ faqDetail.collect_count > 0 ? faqDetail.collect_count : '收藏' }}</text>
      </button>
      <button open-type="share" class="action-btn">
        <text class="icon">📤</text>
        <text class="action-text">{{ faqDetail.forward_count > 0 ? faqDetail.forward_count : '分享' }}</text>
      </button>
    </view>

    <view v-else class="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      faqDetail: null,
      faqId: null,
      isLiked: false,
      isCollected: false,
    }
  },

  computed: {
    processedQuestion() {
      if (!this.faqDetail || !this.faqDetail.question) {
        return ''
      }
      return this.processContent(this.faqDetail.question)
    },

    processedAnswer() {
      if (!this.faqDetail || !this.faqDetail.answer) {
        return ''
      }
      return this.processContent(this.faqDetail.answer)
    }
  },

  onLoad(options) {
    this.faqId = options.id
    if (this.faqId) {
      this.loadFAQDetail()
    }
  },

  methods: {
    async loadFAQDetail() {
      try {
        const res = await api.getFaqDetail(this.faqId)
        this.faqDetail = res.data

        // 详情加载成功后
        this.recordView()
        this.checkStatus()
      } catch (error) {
        console.error('加载FAQ详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    async checkStatus() {
      const token = uni.getStorageSync('token');
      if (!token) return;

      try {
        const params = {
          item_type: 'faq',
          item_id: parseInt(this.faqId)
        };
        const [likeRes, collectRes] = await Promise.all([
          api.checkInteractionStatus({ ...params, actions: ['like'] }),
          api.checkCollectionStatus(params)
        ]);

        if (likeRes.data.like) {
          this.isLiked = true;
        }
        if (collectRes.data.is_collected) {
          this.isCollected = true;
        }
      } catch (error) {
        console.error('检查状态失败:', error);
      }
    },

    async recordView() {
      const token = uni.getStorageSync('token');
      if (!token) {
        return; // 未登录，不记录
      }
      
      try {
        await api.recordView({
          item_type: 'faq',
          item_id: parseInt(this.faqId)
          // 后端会从token中解析user_id
        });
        if (this.faqDetail) {
          this.faqDetail.view_count++;
        }
      } catch (error) {
        console.error('记录浏览失败:', error);
      }
    },

    async toggleLike() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('点赞');
        return;
      }
      try {
        const res = await api.toggleInteraction({
          action: 'like',
          item_type: 'faq',
          item_id: parseInt(this.faqId)
        });
        
        this.isLiked = res.data.action === 'added';
        this.faqDetail.like_count = res.data.current_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('点赞操作失败:', error);
      }
    },

    async toggleCollect() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('收藏');
        return;
      }
      try {
        const res = await api.toggleCollection({
          item_type: 'faq',
          item_id: parseInt(this.faqId)
        });
        
        this.isCollected = res.data.action === 'added';
        this.faqDetail.collect_count = res.data.current_collect_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('收藏操作失败:', error);
      }
    },

    onShareAppMessage() {
      return {
        title: this.faqDetail.question,
        path: `/pages/faq/detail?id=${this.faqId}`,
        imageUrl: '' 
      };
    },

    promptLogin(action) {
      uni.showModal({
        title: '请先登录',
        content: `登录后才能${action}哦`,
        success: (res) => {
          if (res.confirm) {
            uni.switchTab({
              url: '/pages/profile/index'
            });
          }
        }
      });
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = ('0' + (date.getMonth() + 1)).slice(-2)
      const day = ('0' + date.getDate()).slice(-2)
      return `${year}年${month}月${day}日`
    },

    // 处理富文本内容
    processContent(content) {
      if (!content) return ''

      // 处理相对路径的链接，转换为绝对路径
      let processedContent = content

      // 将相对路径的文件链接转换为完整的下载链接
      processedContent = processedContent.replace(
        /href="\/files\//g,
        'href="http://kjrzymt.com/files/'
      )

      // 为所有链接添加样式和数据属性
      processedContent = processedContent.replace(
        /<a\s+([^>]*?)href="([^"]*?)"([^>]*?)>/g,
        '<a $1href="$2"$3 data-link="$2" style="color: #1E90FF; text-decoration: underline; cursor: pointer;">'
      )

      return processedContent
    },

    // 处理内容区域的点击事件
    handleContentTap(event) {
      // 在 H5 环境下，尝试获取点击的元素
      if (typeof window !== 'undefined' && event.target) {
        const target = event.target

        // 检查是否点击了链接
        if (target.tagName === 'A' || target.closest('a')) {
          const link = target.tagName === 'A' ? target : target.closest('a')
          const url = link.getAttribute('href') || link.getAttribute('data-link')

          if (url) {
            event.preventDefault()
            event.stopPropagation()
            this.handleLinkClick(url)
          }
        }
      }
    },

    // 处理富文本中的链接点击
    handleLinkClick(url) {
      console.log('点击链接:', url)

      if (url.startsWith('http://') || url.startsWith('https://')) {
        // 外部链接，复制到剪贴板
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制到剪贴板，请在浏览器中打开',
              showCancel: false
            })
          }
        })
      } else if (url.startsWith('/pages/')) {
        // 内部页面跳转
        uni.navigateTo({
          url: url,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      } else {
        // 其他链接，也复制到剪贴板
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制到剪贴板，请在浏览器中打开',
              showCancel: false
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.faq-detail-page {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding: 30rpx;
  padding-bottom: 180rpx; // 为悬浮按钮留出空间
}

.content {
  width: 100%;
}

.qa-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.question-section, .answer-section {
  position: relative;
  padding-left: 60rpx;
  line-height: 1.7;
}

.question-section {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}

.answer-section {
  font-size: 30rpx;
  color: #555;
  padding-bottom: 40rpx;
  border-bottom: 1rpx solid #eee;
}

.question-section::before, .answer-section::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
}

.question-section::before {
  content: '问';
  background-color: #007bff;
}

.answer-section::before {
  content: '答';
  background-color: #28a745;
}

.meta-info {
  display: flex;
  justify-content: space-between;
  padding-top: 30rpx;
  font-size: 26rpx;
  color: #999;
}

.disclaimer {
  margin-top: 40rpx;
  padding: 20rpx;
  font-size: 24rpx;
  color: #aaa;
  line-height: 1.6;
  text-align: center;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
}

.action-bar-sticky {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  transition: color 0.2s ease-in-out, transform 0.1s ease;

  &::after {
    border: none;
  }
  
  &:active {
    transform: scale(0.9);
  }
  
  &.active {
    color: #DC143C;
  }
  
  .icon {
    font-size: 44rpx;
    margin-bottom: 6rpx;
    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    opacity: 0.7;
    filter: grayscale(80%);
  }
  
  &.active .icon {
    transform: scale(1.1);
    opacity: 1;
    filter: grayscale(0%);
  }
  
  .action-text {
    font-size: 24rpx;
  }
}
</style> 