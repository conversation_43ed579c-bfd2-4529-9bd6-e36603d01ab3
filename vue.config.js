module.exports = {
  devServer: {
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://kjrzymt.com',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug',
        pathRewrite: {
          '^/api': '/api'
        },
        onProxyReq: (proxyReq, req, res) => {
          console.log('代理请求:', req.method, req.url, '->', proxyReq.getHeader('host') + proxyReq.path);

          // 设置基本的浏览器请求头
          proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
          proxyReq.setHeader('Accept', 'application/json, text/plain, */*');
          proxyReq.setHeader('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8');
          proxyReq.setHeader('Referer', 'http://kjrzymt.com/');

          // 如果是POST请求，确保Content-Type正确
          if (req.method === 'POST' && req.headers['content-type']) {
            proxyReq.setHeader('Content-Type', req.headers['content-type']);
          }
        },
        onProxyRes: (proxyRes, req, res) => {
          console.log('代理响应:', proxyRes.statusCode, req.url);

          // 设置CORS头
          proxyRes.headers['Access-Control-Allow-Origin'] = '*';
          proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
          proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization';
        },
        onError: (err, req, res) => {
          console.error('代理错误:', err.message);
        }
      }
    }
  },
  transpileDependencies: ['@dcloudio/uni-ui']
}