<template>
  <view class="faq-list-page">
    <!-- 顶部导航 -->
    <view class="page-header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="header-title">
        <text class="title-text">🤔 热门问答</text>
        <text class="title-subtitle">山城企业常见问题解答</text>
      </view>
      <view class="header-right">
        <text class="refresh-btn" @click="refreshList">🔄</text>
      </view>
    </view>

    <!-- 问题列表 -->
    <view class="faq-content">
      <view v-if="loading" class="loading-section">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载问题...</text>
      </view>
      <view v-else-if="faqList.length === 0" class="empty-section">
        <text class="empty-icon">🤔</text>
        <text class="empty-title">暂无相关问题</text>
        <text class="empty-subtitle">稍后再来看看</text>
      </view>
      <view v-else class="faq-list">
        <view class="faq-item" v-for="faq in faqList" :key="faq.id" @click="toFAQDetail(faq.id)">
          <view class="faq-item-header">
            <view class="faq-icon">❓</view>
            <view class="faq-main">
              <text class="faq-question">{{ faq.question }}</text>
              <view class="faq-meta">
                <text class="faq-category" v-if="faq.category">{{ faq.category }}</text>
                <text class="faq-date">{{ formatDate(faq.answer_date || faq.created_at) }}</text>
              </view>
            </view>
            <view class="faq-arrow">›</view>
          </view>
          <view class="faq-preview" v-if="faq.answer">
            <text class="faq-answer-preview">{{ getAnswerPreview(faq.answer) }}</text>
          </view>
          <view class="faq-stats">
            <view class="stat-item"><text class="stat-icon">👁</text><text class="stat-value">{{ formatViewCount(faq.view_count) }}</text></view>
            <view class="stat-item"><text class="stat-icon">👍</text><text class="stat-value">{{ faq.like_count || 0 }}</text></view>
            <view class="stat-item"><text class="stat-icon">⭐</text><text class="stat-value">{{ faq.collect_count || 0 }}</text></view>
          </view>
        </view>
      </view>
      <view v-if="hasMore && !loading" class="load-more">
        <text class="load-more-btn" @click="loadMore">加载更多</text>
      </view>
    </view>
  </view>
</template>

<script>
import { api } from '@/utils/api.js'

export default {
  data() {
    return {
      faqList: [],
      loading: true,
      page: 1,
      perPage: 20,
      hasMore: true
    }
  },
  onLoad() {
    this.loadFAQList()
  },
  onPullDownRefresh() {
    this.refreshList()
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    async loadFAQList() {
      try {
        this.loading = true
        const params = {
          page: this.page,
          per_page: this.perPage,
          sort: 'view_count'
        }
        const res = await api.getFaqs(params)
        if (res.success && res.data) {
          const faqs = res.data.items || []
          if (this.page === 1) {
            this.faqList = faqs
          } else {
            this.faqList = [...this.faqList, ...faqs]
          }
          this.hasMore = res.data.has_next || false
        } else {
          this.faqList = []
          this.hasMore = false
        }
      } catch (error) {
        uni.showToast({ title: '加载失败', icon: 'error' })
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },
    refreshList() {
      this.page = 1
      this.hasMore = true
      this.loadFAQList()
    },
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++
        this.loadFAQList()
      }
    },
    toFAQDetail(id) {
      uni.navigateTo({ url: `/pages/faq/detail?id=${id}` })
    },
    getAnswerPreview(answer) {
      if (!answer) return ''
      const plainText = answer.replace(/<[^>]*>/g, '')
      return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      if (diffDays === 1) return '昨天'
      if (diffDays <= 7) return `${diffDays}天前`
      return date.toLocaleDateString('zh-CN')
    },
    formatViewCount(count) {
      if (!count) return '0'
      if (count >= 10000) return (count / 10000).toFixed(1) + 'w'
      if (count >= 1000) return (count / 1000).toFixed(1) + 'k'
      return count.toString()
    }
  }
}
</script>

<style scoped>
.faq-list-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.title-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 4rpx;
}

.header-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.refresh-btn {
  font-size: 36rpx;
  color: #1E90FF;
}

.faq-content {
  padding: 20rpx 0;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
}

.faq-list {
  padding: 0 30rpx;
}

.faq-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.faq-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.faq-item-header {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.faq-icon {
  font-size: 24rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 193, 7, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.faq-main {
  flex: 1;
  min-width: 0;
}

.faq-question {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.faq-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.faq-category {
  font-size: 22rpx;
  color: #1E90FF;
  background: rgba(30, 144, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.faq-date {
  font-size: 22rpx;
  color: #999;
}

.faq-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
  flex-shrink: 0;
}

.faq-preview {
  margin-bottom: 16rpx;
}

.faq-answer-preview {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

.faq-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  font-size: 24rpx;
}

.stat-value {
  font-size: 24rpx;
  color: #999;
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #e8e8e8;
}
</style> 