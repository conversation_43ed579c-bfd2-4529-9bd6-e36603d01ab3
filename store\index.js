import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

const store = new Vuex.Store({
  state: {
    userInfo: null,
    searchHistory: [],
    systemInfo: null
  },
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    },
    ADD_SEARCH_HISTORY(state, keyword) {
      if (keyword && !state.searchHistory.includes(keyword)) {
        state.searchHistory.unshift(keyword)
        if (state.searchHistory.length > 10) {
          state.searchHistory = state.searchHistory.slice(0, 10)
        }
      }
    },
    CLEAR_SEARCH_HISTORY(state) {
      state.searchHistory = []
    },
    SET_SYSTEM_INFO(state, systemInfo) {
      state.systemInfo = systemInfo
    }
  },
  actions: {
    setUserInfo({ commit }, userInfo) {
      commit('SET_USER_INFO', userInfo)
    },
    addSearchHistory({ commit }, keyword) {
      commit('ADD_SEARCH_HISTORY', keyword)
    },
    clearSearchHistory({ commit }) {
      commit('CLEAR_SEARCH_HISTORY')
    },
    setSystemInfo({ commit }, systemInfo) {
      commit('SET_SYSTEM_INFO', systemInfo)
    }
  }
})

export default store 