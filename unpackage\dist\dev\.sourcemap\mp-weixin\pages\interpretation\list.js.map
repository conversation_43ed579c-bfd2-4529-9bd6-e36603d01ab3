{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/list.vue?e970", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/list.vue?bd46", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/list.vue?eeaf", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/list.vue?6b2c", "uni-app:///pages/interpretation/list.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/list.vue?2c19", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/interpretation/list.vue?b437"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "interpretationList", "currentPage", "hasMore", "loading", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "loadInterpretations", "reset", "api", "page", "per_page", "res", "newList", "console", "uni", "toDetail", "url", "toSearch", "formatDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAA+2B,CAAgB,g0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6Cn4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKAC;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;cAAA;gBAAA;gBAEA;gBACAC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACAD;QACAE;MACA;IACA;IACAC;MACAH;QACAE;MACA;IACA;IACAE;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/GA;AAAA;AAAA;AAAA;AAA0oD,CAAgB,++CAAG,EAAC,C;;;;;;;;;;;ACA9pD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/interpretation/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/interpretation/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=02adcadc&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=02adcadc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"02adcadc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/interpretation/list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=02adcadc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.interpretationList, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.formatDate(item.publish_date)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.hasMore && _vm.interpretationList.length > 0\n  var g1 = _vm.interpretationList.length === 0 && !_vm.loading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"interpretation-page\">\r\n    <!-- 搜索栏 -->\r\n    <view class=\"search-section\" @click=\"toSearch\">\r\n      <view class=\"search-box\">\r\n        <text class=\"search-icon\">🔍</text>\r\n        <text class=\"search-placeholder\">搜索政策解读...</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 列表 -->\r\n    <view class=\"interpretation-list\">\r\n      <view v-for=\"item in interpretationList\" :key=\"item.id\" class=\"interpretation-item\" @click=\"toDetail(item.id)\">\r\n        <image class=\"cover-image\" :src=\"item.cover_img || '/static/images/default-cover.jpg'\" mode=\"aspectFill\"></image>\r\n        <view class=\"info-content\">\r\n          <text class=\"title ellipsis-2\">{{ item.title }}</text>\r\n          <view class=\"meta-row\">\r\n            <text class=\"date\">{{ formatDate(item.publish_date) }}</text>\r\n            <view class=\"stats\">\r\n              <text class=\"stat-item\">\r\n                <text class=\"icon\">👁️</text> {{ item.view_count || 0 }}\r\n              </text>\r\n              <text class=\"stat-item\">\r\n                <text class=\"icon\">👍</text> {{ item.like_count || 0 }}\r\n              </text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载提示 -->\r\n    <view class=\"load-more-tip\" v-if=\"loading\">加载中...</view>\r\n    <view class=\"load-more-tip\" v-if=\"!hasMore && interpretationList.length > 0\">没有更多了</view>\r\n    \r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-if=\"interpretationList.length === 0 && !loading\">\r\n      <text class=\"empty-icon\">🎥</text>\r\n      <text class=\"empty-text\">暂无政策解读</text>\r\n    </view>\r\n\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { api } from '@/utils/api';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      interpretationList: [],\r\n      currentPage: 1,\r\n      hasMore: true,\r\n      loading: false,\r\n    };\r\n  },\r\n  onLoad() {\r\n    this.loadInterpretations(true);\r\n  },\r\n  onPullDownRefresh() {\r\n    this.loadInterpretations(true);\r\n  },\r\n  onReachBottom() {\r\n    if (this.hasMore && !this.loading) {\r\n      this.loadInterpretations();\r\n    }\r\n  },\r\n  methods: {\r\n    async loadInterpretations(reset = false) {\r\n      if (this.loading) return;\r\n      this.loading = true;\r\n\r\n      if (reset) {\r\n        this.currentPage = 1;\r\n        this.interpretationList = [];\r\n      }\r\n\r\n      try {\r\n        const res = await api.getInterpretations({\r\n          page: this.currentPage,\r\n          per_page: 10,\r\n        });\r\n        \r\n        const newList = res.data?.items || [];\r\n        this.interpretationList = this.interpretationList.concat(newList);\r\n        this.hasMore = newList.length === 10;\r\n        this.currentPage++;\r\n        \r\n      } catch (error) {\r\n        console.error(\"加载政策解读列表失败\", error);\r\n      } finally {\r\n        this.loading = false;\r\n        uni.stopPullDownRefresh();\r\n      }\r\n    },\r\n    toDetail(id) {\r\n      uni.navigateTo({\r\n        url: `/pages/interpretation/detail?id=${id}`,\r\n      });\r\n    },\r\n    toSearch() {\r\n      uni.navigateTo({\r\n        url: '/pages/search/index?type=interpretation',\r\n      });\r\n    },\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '';\r\n      const date = new Date(dateStr);\r\n      return `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)}`;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.interpretation-page {\r\n  background-color: #f4f5f7;\r\n  min-height: 100vh;\r\n}\r\n\r\n.search-section {\r\n  padding: 20rpx 30rpx;\r\n  background-color: #fff;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.search-box {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #f5f5f5;\r\n  border-radius: 50rpx;\r\n  padding: 16rpx 30rpx;\r\n  color: #999;\r\n}\r\n\r\n.search-icon {\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.interpretation-list {\r\n  padding: 30rpx;\r\n}\r\n\r\n.interpretation-item {\r\n  display: flex;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);\r\n  overflow: hidden;\r\n}\r\n\r\n.cover-image {\r\n  width: 240rpx;\r\n  height: 180rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n  padding: 20rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.title {\r\n  font-size: 30rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  line-height: 1.4;\r\n  height: 84rpx; // 2 lines\r\n}\r\n\r\n.meta-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 10rpx;\r\n  color: #999;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.stats {\r\n  display: flex;\r\n  gap: 20rpx;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6rpx;\r\n}\r\n\r\n.icon {\r\n  font-size: 28rpx;\r\n}\r\n\r\n.load-more-tip {\r\n  padding: 30rpx;\r\n  text-align: center;\r\n  color: #999;\r\n}\r\n\r\n.empty-state {\r\n  padding: 100rpx 30rpx;\r\n  text-align: center;\r\n  color: #999;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 80rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=02adcadc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=02adcadc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051453\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}