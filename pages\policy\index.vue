<template>
  <view class="policy-page">
    <!-- 搜索栏 -->
    <view class="search-section p-3 bg-white">
      <view class="search-box flex-center bg-gray rounded-lg p-2" @click="toSearch">
        <text class="search-icon text-gray mr-2">🔍</text>
        <text class="search-placeholder text-gray">搜索政策文件...</text>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view class="filter-section bg-white p-2 mb-2">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-list flex-row">
          <view
            class="filter-item p-2 mr-2 rounded text-center"
            :class="{
              'bg-primary text-white': currentCategory === item.value,
              'bg-gray text-dark': currentCategory !== item.value
            }"
            v-for="item in categoryList"
            :key="item.value"
            @click="selectCategory(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 政策列表 -->
    <view class="policy-list-section">
      <view class="policy-item bg-white rounded-lg p-3 mb-2" v-for="policy in policyList" :key="policy.id" @click="toPolicyDetail(policy.id)">
        <view class="policy-header flex-between mb-2">
          <text class="policy-title ellipsis-2 text-lg font-bold text-dark">{{ policy.title }}</text>
          <view class="policy-category bg-primary text-white text-xs p-1 rounded">{{ policy.category1 }}</view>
        </view>
        <view class="policy-content ellipsis-2 text-base text-gray mb-2" v-html="policy.content"></view>
        <view class="policy-footer flex-between align-center">
          <view class="policy-date text-sm text-gray">{{ formatDate(policy.publish_date) }}</view>
          <view class="policy-stats flex-row">
            <text class="stat-item flex-center mr-2 text-xs text-gray">
              <text class="icon mr-1">👁️</text> {{ policy.view_count || 0 }}
            </text>
            <text class="stat-item flex-center mr-2 text-xs text-gray">
              <text class="icon mr-1">👍</text> {{ policy.like_count || 0 }}
            </text>
            <text class="stat-item flex-center mr-2 text-xs text-gray">
              <text class="icon mr-1">⭐</text> {{ policy.collect_count || 0 }}
            </text>
             <text class="stat-item flex-center text-xs text-gray">
              <text class="icon mr-1">📤</text> {{ policy.forward_count || 0 }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more flex-center p-3" v-if="hasMore">
      <view class="load-more-btn bg-primary text-white p-2 rounded-lg" @click="loadMore">
        <text>加载更多</text>
      </view>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" v-else-if="policyList.length > 0">
      <text>没有更多数据了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="policyList.length === 0 && !loading">
      <text class="empty-icon">📄</text>
      <text class="empty-text">暂无政策文件</text>
    </view>
    
    <!-- 自定义TabBar -->
    <custom-tab-bar />
  </view>
</template>

<script>
import { api } from '@/utils/api'
import CustomTabBar from '@/custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      currentCategory: '',
      categoryList: [
        { label: '全部', value: '' },
        { label: '外汇管理', value: '外汇管理' },
        { label: '跨境融资', value: '跨境融资' },
        { label: '地方政策', value: '地方政策' },
        { label: '金融监管', value: '金融监管' },
        { label: '对外投资', value: '对外投资' },
        { label: '人民币国际化', value: '人民币国际化' }
      ],
      policyList: [],
      currentPage: 1,
      hasMore: true,
      loading: false
    }
  },
  onLoad() {
    this.loadPolicyList()
  },
  onPullDownRefresh() {
    this.refreshData()
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  onShow() {
    if (typeof this?.$root?.$mp?.page?.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {
      this.$root.$mp.page.getTabBar().$vm.updateSelected(1);
    }
  },
  methods: {
    async loadPolicyList(reset = false) {
      if (this.loading) return
      
      this.loading = true
      
      try {
        const params = {
          page: reset ? 1 : this.currentPage,
          per_page: 20
        }
        
        if (this.currentCategory) {
          params.category1 = this.currentCategory
        }
        
        const res = await api.getPolicies(params)
        const newList = res.data?.items || []
        
        if (reset) {
          this.policyList = newList
          this.currentPage = 1
        } else {
          this.policyList = this.policyList.concat(newList)
        }
        
        this.hasMore = newList.length === 20
        this.currentPage += 1
        
      } catch (error) {
        console.error('加载政策列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
        
        // 使用模拟数据
        if (reset || this.policyList.length === 0) {
          this.loadMockData()
        }
      } finally {
        this.loading = false
        if (reset) {
          uni.stopPullDownRefresh()
        }
      }
    },

    loadMockData() {
      this.policyList = [
        {
          id: 1,
          title: '国家外汇管理局关于进一步促进跨境贸易投资便利化的通知',
          category1: '外汇管理',
          category2: '跨境贸易',
          category3: '便民措施',
          content: '为深入贯彻党中央、国务院关于稳外贸稳外资的决策部署...',
          view_count: 312,
          publish_date: '2024-01-10'
        },
        {
          id: 2,
          title: '重庆市促进跨境融资发展实施细则',
          category1: '地方政策',
          category2: '跨境融资',
          category3: '执行细则',
          content: '根据国家相关政策，结合重庆实际，制定本实施细则...',
          view_count: 256,
          publish_date: '2024-01-12'
        },
        {
          id: 3,
          title: '银行业金融机构外汇业务管理办法',
          category1: '金融监管',
          category2: '银行业务',
          category3: '管理办法',
          content: '为规范银行业金融机构外汇业务经营行为...',
          view_count: 198,
          publish_date: '2024-01-18'
        },
        {
          id: 4,
          title: '企业对外投资备案管理办法',
          category1: '对外投资',
          category2: '备案管理',
          category3: '管理流程',
          content: '为加强和规范企业对外投资备案管理...',
          view_count: 234,
          publish_date: '2024-01-22'
        },
        {
          id: 5,
          title: '跨境人民币业务管理暂行办法',
          category1: '人民币国际化',
          category2: '跨境结算',
          category3: '暂行办法',
          content: '为促进跨境人民币业务健康发展...',
          view_count: 287,
          publish_date: '2024-01-28'
        }
      ]
    },

    selectCategory(category) {
      this.currentCategory = category
      this.refreshData()
    },

    refreshData() {
      this.loadPolicyList(true)
    },

    loadMore() {
      this.loadPolicyList()
    },

    toPolicyDetail(id) {
      uni.navigateTo({
        url: `/pages/policy/detail?id=${id}`
      })
    },

    toSearch() {
      uni.navigateTo({
        url: '/pages/search/index?type=policy'
      })
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      return date.toLocaleDateString()
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */
  position: relative;
}

/* 顶部重庆山城风格背景 */
.policy-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, 
    #1E90FF 0%, 
    #4A90E2 30%, 
    #6BA3E8 60%, 
    rgba(107, 163, 232, 0.6) 80%, 
    rgba(138, 180, 240, 0.3) 90%, 
    transparent 100%
  );
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.policy-page::after {
  content: '';
  position: absolute;
  top: 140rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}

.search-section {
  padding: 30rpx;
  background: white;
  border-bottom: 1rpx solid #e5e5e5;
  position: relative;
  z-index: 3;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
}

.search-icon {
  font-size: 28rpx;
  margin-right: 20rpx;
  color: #999;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

.filter-section {
  background: white;
  border-bottom: 1rpx solid #e5e5e5;
  position: relative;
  z-index: 3;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.filter-item {
  padding: 16rpx 32rpx;
  background: #f5f5f5;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s;
}

.filter-item.active {
  background: #1E90FF; /* 主题蓝色 */
  color: white;
}

.policy-list-section {
  padding: 0 30rpx;
}

.policy-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.policy-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  line-height: 1.4;
  margin-right: 20rpx;
}

.policy-category {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}

.policy-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.policy-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.policy-date {
  font-size: 24rpx;
  color: #999;
}

.policy-stats {
  display: flex;
  gap: 24rpx;
  font-size: 24rpx;
  color: #999;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.icon {
  font-size: 28rpx;
}

.load-more {
  padding: 40rpx;
  text-align: center;
}

.load-more-btn {
  display: inline-block;
  padding: 20rpx 60rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
}

.no-more {
  padding: 40rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
}
</style> 