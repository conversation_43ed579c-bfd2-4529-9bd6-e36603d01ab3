.interpretation-page.data-v-0c55e0e2 {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding-bottom: 160rpx;
}
.video-player-section.data-v-0c55e0e2 {
  width: 100%;
  height: 420rpx;
  background-color: #000;
}
.video-player.data-v-0c55e0e2 {
  width: 100%;
  height: 100%;
}
.info-card.data-v-0c55e0e2 {
  padding: 30rpx;
  background-color: #fff;
}
.title.data-v-0c55e0e2 {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}
.meta-info.data-v-0c55e0e2 {
  font-size: 26rpx;
  color: #999;
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.content-body.data-v-0c55e0e2 {
  font-size: 30rpx;
  color: #555;
  line-height: 1.8;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.attachment-section.data-v-0c55e0e2 {
  margin-top: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.attachment-title.data-v-0c55e0e2 {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.attachment-item.data-v-0c55e0e2 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #e9ecef;
  border-radius: 8rpx;
}
.attachment-icon.data-v-0c55e0e2 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.attachment-name.data-v-0c55e0e2 {
  color: #007bff;
  font-size: 28rpx;
  text-decoration: underline;
}
.loading.data-v-0c55e0e2 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
}
.action-bar-footer.data-v-0c55e0e2 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.98);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.action-btn.data-v-0c55e0e2 {
  flex: 1;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  color: #666;
  transition: color 0.2s ease-in-out, -webkit-transform 0.1s ease;
  transition: color 0.2s ease-in-out, transform 0.1s ease;
  transition: color 0.2s ease-in-out, transform 0.1s ease, -webkit-transform 0.1s ease;
}
.action-btn.data-v-0c55e0e2::after {
  border: none;
}
.action-btn.active.data-v-0c55e0e2 {
  color: #DC143C;
}
.action-btn .icon.data-v-0c55e0e2 {
  font-size: 44rpx;
  transition: all 0.2s ease;
}
.action-btn.active .icon.data-v-0c55e0e2 {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.action-btn .action-text.data-v-0c55e0e2 {
  font-size: 24rpx;
}

