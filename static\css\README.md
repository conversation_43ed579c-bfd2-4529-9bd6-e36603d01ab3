# 公共样式使用说明

## 概述

项目使用SCSS作为CSS预处理器，提供了强大的变量、混合器和工具类系统。

### 文件结构
- `common.scss` - 主要的公共样式文件，包含所有工具类
- `variables.scss` - SCSS变量定义文件
- `mixins.scss` - SCSS混合器定义文件

所有样式已经在 `App.vue` 中全局引入，所有页面都可以直接使用这些样式类。

## 在页面中使用SCSS变量和混合器

### 1. 引入变量文件
```scss
<style lang="scss" scoped>
@import '@/static/css/variables.scss';

.my-component {
  color: $primary-color;
  padding: $spacing-3;
}
</style>
```

### 2. 引入混合器文件
```scss
<style lang="scss" scoped>
@import '@/static/css/mixins.scss';

.my-button {
  @include button-style($primary-color, white, 'large');
}

.my-card {
  @include card-style($spacing-4, 'lg');
}
</style>
```

## 样式类别

### 1. Flex布局类

#### 基础Flex方向
- `.flex-col` - 垂直方向的flex布局
- `.flex-row` - 水平方向的flex布局

#### 水平对齐
- `.justify-start` - 左对齐
- `.justify-center` - 居中对齐
- `.justify-end` - 右对齐
- `.justify-between` - 两端对齐
- `.justify-around` - 环绕对齐
- `.justify-evenly` - 平均分布

#### 垂直对齐
- `.align-start` - 顶部对齐
- `.align-center` - 垂直居中
- `.align-end` - 底部对齐

#### 组合布局类
- `.flex-center` - 水平垂直居中
- `.flex-between` - 垂直居中，水平两端对齐
- `.flex-start` - 垂直居中，水平左对齐
- `.flex-end` - 垂直居中，水平右对齐

### 2. 间距工具类

#### 外边距 (margin)
- `.m-0` 到 `.m-4` - 全方向外边距 (0, 10rpx, 20rpx, 30rpx, 40rpx)
- `.mt-0` 到 `.mt-4` - 上外边距
- `.mb-0` 到 `.mb-4` - 下外边距
- `.ml-0` 到 `.ml-4` - 左外边距
- `.mr-0` 到 `.mr-4` - 右外边距

#### 内边距 (padding)
- `.p-0` 到 `.p-4` - 全方向内边距
- `.pt-0` 到 `.pt-4` - 上内边距
- `.pb-0` 到 `.pb-4` - 下内边距
- `.pl-0` 到 `.pl-4` - 左内边距
- `.pr-0` 到 `.pr-4` - 右内边距

### 3. 文本工具类

#### 文本对齐
- `.text-left` - 左对齐
- `.text-center` - 居中对齐
- `.text-right` - 右对齐

#### 字体大小
- `.text-xs` - 20rpx
- `.text-sm` - 24rpx
- `.text-base` - 28rpx
- `.text-lg` - 32rpx
- `.text-xl` - 36rpx
- `.text-2xl` - 40rpx

#### 字体粗细
- `.font-normal` - 正常粗细
- `.font-bold` - 加粗

### 4. 颜色工具类

#### 文字颜色
- `.text-primary` - 主色调文字 (#1E90FF)
- `.text-secondary` - 次要色文字 (#4A90E2)
- `.text-gray` - 灰色文字 (#999999)
- `.text-dark` - 深色文字 (#333333)
- `.text-white` - 白色文字

#### 背景颜色
- `.bg-primary` - 主色调背景
- `.bg-secondary` - 次要色背景
- `.bg-white` - 白色背景
- `.bg-gray` - 灰色背景

### 5. 边框工具类

#### 边框
- `.border` - 全边框
- `.border-t` - 上边框
- `.border-b` - 下边框
- `.border-l` - 左边框
- `.border-r` - 右边框

#### 圆角
- `.rounded` - 小圆角 (8rpx)
- `.rounded-lg` - 大圆角 (16rpx)
- `.rounded-full` - 圆形

### 6. 文本省略

- `.ellipsis` - 单行文本省略
- `.ellipsis-2` - 两行文本省略
- `.ellipsis-3` - 三行文本省略

## 使用示例

### 基础布局示例
```html
<!-- 水平居中的卡片 -->
<view class="flex-center p-3 bg-white rounded-lg">
  <text class="text-lg font-bold text-primary">标题</text>
</view>

<!-- 两端对齐的列表项 -->
<view class="flex-between p-2 border-b">
  <text class="text-base text-dark">项目名称</text>
  <text class="text-sm text-gray">2024-01-01</text>
</view>

<!-- 垂直布局的内容区域 -->
<view class="flex-col p-3">
  <text class="text-xl font-bold mb-2">标题</text>
  <text class="text-base text-gray ellipsis-2">这是一段很长的描述文字...</text>
</view>
```

### 网格布局示例
```html
<!-- 功能菜单网格 -->
<view class="flex-row justify-around p-3">
  <view class="flex-col align-center">
    <view class="w-full h-full bg-primary rounded-lg mb-1"></view>
    <text class="text-sm">功能1</text>
  </view>
  <view class="flex-col align-center">
    <view class="w-full h-full bg-secondary rounded-lg mb-1"></view>
    <text class="text-sm">功能2</text>
  </view>
</view>
```

### 列表项示例
```html
<!-- 新闻列表项 -->
<view class="flex-row p-3 bg-white rounded border-b">
  <image class="rounded mr-2" style="width: 120rpx; height: 80rpx;"></image>
  <view class="flex-col flex-1">
    <text class="text-base font-bold ellipsis-2 mb-1">新闻标题</text>
    <text class="text-sm text-gray">2024-01-01</text>
  </view>
</view>
```

## 注意事项

1. 所有样式类都已经全局引入，无需在单个页面中重复引入
2. 尺寸单位使用 `rpx`，适配不同屏幕尺寸
3. 颜色值与项目主题色保持一致
4. 建议优先使用工具类，减少自定义样式的编写
5. 如需扩展样式，可以在 `common.css` 中添加新的工具类

## SCSS特有功能

### 1. 变量系统
项目定义了完整的设计系统变量：
```scss
// 颜色变量
$primary-color: #1E90FF;
$secondary-color: #4A90E2;

// 间距变量
$spacing-1: 10rpx;
$spacing-2: 20rpx;

// 字体大小变量
$text-base: 28rpx;
$text-lg: 32rpx;
```

### 2. 混合器(Mixins)
提供了丰富的混合器，可以快速应用复杂样式：

#### 布局混合器
```scss
@include flex-center;        // 水平垂直居中
@include flex-between;       // 两端对齐
@include flex-column-center; // 垂直布局居中
```

#### 样式混合器
```scss
@include button-style($primary-color, white, 'large');
@include card-style($spacing-4, 'lg');
@include text-ellipsis(2);
@include gradient-bg($primary-color, $secondary-color);
```

#### 动画混合器
```scss
@include fade-in(0.3s);
@include slide-up(0.5s);
```

### 3. 响应式设计
```scss
.my-component {
  font-size: $text-base;

  @include respond-to('md') {
    font-size: $text-lg;
  }

  @include respond-to('lg') {
    font-size: $text-xl;
  }
}
```

### 4. 嵌套语法
```scss
.card {
  background: white;
  padding: $spacing-3;

  &:hover {
    transform: translateY(-2rpx);
  }

  .title {
    font-size: $text-lg;
    color: $primary-color;

    &.active {
      font-weight: bold;
    }
  }
}
```

## 扩展建议

### 添加新变量
在 `variables.scss` 中添加：
```scss
$new-color: #FF6B6B;
$new-spacing: 25rpx;
```

### 添加新混合器
在 `mixins.scss` 中添加：
```scss
@mixin my-custom-style($param1, $param2: default) {
  // 样式定义
}
```

### 添加新工具类
在 `common.scss` 中添加：
```scss
.my-utility {
  @include my-custom-style($primary-color);
}
```

建议遵循以下规范：
- 保持命名的一致性和语义化
- 使用项目统一的变量系统
- 优先使用混合器而不是重复的CSS代码
- 添加相应的文档说明
- 考虑响应式设计的需求
