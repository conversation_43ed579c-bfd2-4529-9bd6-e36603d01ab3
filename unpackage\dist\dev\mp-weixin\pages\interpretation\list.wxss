.interpretation-page.data-v-02adcadc {
  background-color: #f4f5f7;
  min-height: 100vh;
}
.search-section.data-v-02adcadc {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}
.search-box.data-v-02adcadc {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 16rpx 30rpx;
  color: #999;
}
.search-icon.data-v-02adcadc {
  margin-right: 16rpx;
}
.interpretation-list.data-v-02adcadc {
  padding: 30rpx;
}
.interpretation-item.data-v-02adcadc {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.cover-image.data-v-02adcadc {
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}
.info-content.data-v-02adcadc {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.title.data-v-02adcadc {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  height: 84rpx;
}
.meta-row.data-v-02adcadc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
  color: #999;
  font-size: 24rpx;
}
.stats.data-v-02adcadc {
  display: flex;
  gap: 20rpx;
}
.stat-item.data-v-02adcadc {
  display: flex;
  align-items: center;
  gap: 6rpx;
}
.icon.data-v-02adcadc {
  font-size: 28rpx;
}
.load-more-tip.data-v-02adcadc {
  padding: 30rpx;
  text-align: center;
  color: #999;
}
.empty-state.data-v-02adcadc {
  padding: 100rpx 30rpx;
  text-align: center;
  color: #999;
}
.empty-icon.data-v-02adcadc {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

