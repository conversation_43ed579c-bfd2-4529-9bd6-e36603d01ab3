{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/news/detail.vue?ce05", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/news/detail.vue?a956", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/news/detail.vue?2825", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/news/detail.vue?2099", "uni-app:///pages/news/detail.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/news/detail.vue?8b26", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/news/detail.vue?c484"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "newsDetail", "newsId", "isLiked", "isCollected", "onLoad", "methods", "loadNewsDetail", "api", "res", "console", "uni", "title", "icon", "checkStatus", "token", "params", "item_type", "item_id", "Promise", "actions", "likeRes", "collectRes", "recordView", "toggleLike", "action", "toggleCollect", "onShareAppMessage", "path", "imageUrl", "promptLogin", "content", "success", "url", "formatDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACwDr4B;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACAC,aACAX;kBAAAY;gBAAA,KACAZ,2CACA;cAAA;gBAAA;gBAAA;gBAHAa;gBAAAC;gBAKA;kBACA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAP;kBACAS;kBACAC;gBACA;cAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAR;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAT;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAP;kBACAiB;kBACAR;kBACAC;gBACA;cAAA;gBAJAT;gBAMA;gBACA;gBAEAE;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAX;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAP;kBACAS;kBACAC;gBACA;cAAA;gBAHAT;gBAKA;gBACA;gBAEAE;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiB;MACA;QACAf;QACAgB;QACAC;MACA;IACA;IAEAC;MACAnB;QACAC;QACAmB;QACAC;UACA;YACArB;cACAsB;YACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvNA;AAAA;AAAA;AAAA;AAA4oD,CAAgB,i/CAAG,EAAC,C;;;;;;;;;;;ACAhqD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/news/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/news/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=6803dca4&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=6803dca4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6803dca4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/news/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=6803dca4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.newsDetail ? _vm.formatDate(_vm.newsDetail.publish_date) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"news-detail-page\">\n    <view v-if=\"newsDetail\" class=\"content\">\n      <view class=\"article-card\">\n        <!-- 文章头部 -->\n        <view class=\"article-header\">\n          <view class=\"article-title\">{{ newsDetail.title }}</view>\n          <view class=\"article-meta\">\n            <text class=\"category\">{{ newsDetail.category }}</text>\n            <text class=\"publish-date\">{{ formatDate(newsDetail.publish_date) }}</text>\n          </view>\n          <view class=\"article-stats\">\n            <text class=\"stat-item\">浏览 {{ newsDetail.view_count || 0 }}</text>\n            <text class=\"stat-item\">点赞 {{ newsDetail.like_count || 0 }}</text>\n            <text class=\"stat-item\">收藏 {{ newsDetail.collect_count || 0 }}</text>\n          </view>\n        </view>\n\n        <!-- 封面图片 -->\n        <image \n          v-if=\"newsDetail.cover_img\" \n          :src=\"newsDetail.cover_img\" \n          class=\"cover-image\"\n          mode=\"aspectFill\"\n        />\n\n        <!-- 文章内容 -->\n        <view class=\"article-body\">\n          <rich-text :nodes=\"newsDetail.content\"></rich-text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 悬浮操作栏 -->\n    <view v-if=\"newsDetail\" class=\"action-bar-sticky\">\n      <button @click=\"toggleLike\" class=\"action-btn\" :class=\"{ active: isLiked }\">\n        <text class=\"icon\">👍</text>\n        <text class=\"action-text\">{{ newsDetail.like_count > 0 ? newsDetail.like_count : '点赞' }}</text>\n      </button>\n      <button @click=\"toggleCollect\" class=\"action-btn\" :class=\"{ active: isCollected }\">\n        <text class=\"icon\">{{ isCollected ? '⭐' : '☆' }}</text>\n        <text class=\"action-text\">{{ newsDetail.collect_count > 0 ? newsDetail.collect_count : '收藏' }}</text>\n      </button>\n      <button open-type=\"share\" class=\"action-btn\">\n        <text class=\"icon\">📤</text>\n        <text class=\"action-text\">{{ newsDetail.forward_count > 0 ? newsDetail.forward_count : '分享' }}</text>\n      </button>\n    </view>\n\n    <view v-else class=\"loading\">\n      <text>加载中...</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport api from '@/utils/api.js'\n\nexport default {\n  data() {\n    return {\n      newsDetail: null,\n      newsId: null,\n      isLiked: false,\n      isCollected: false\n    }\n  },\n\n  onLoad(options) {\n    this.newsId = options.id\n    if (this.newsId) {\n      this.loadNewsDetail()\n    }\n  },\n\n  methods: {\n    async loadNewsDetail() {\n      try {\n        const res = await api.getNewsDetail(this.newsId)\n        this.newsDetail = res.data\n        \n        // 详情加载成功后\n        this.recordView()\n        this.checkStatus()\n      } catch (error) {\n        console.error('加载新闻详情失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    async checkStatus() {\n      const token = uni.getStorageSync('token');\n      if (!token) return;\n\n      try {\n        const params = {\n          item_type: 'news',\n          item_id: parseInt(this.newsId)\n        };\n        const [likeRes, collectRes] = await Promise.all([\n          api.checkInteractionStatus({ ...params, actions: ['like'] }),\n          api.checkCollectionStatus(params)\n        ]);\n\n        if (likeRes.data.like) {\n          this.isLiked = true;\n        }\n        if (collectRes.data.is_collected) {\n          this.isCollected = true;\n        }\n      } catch (error) {\n        console.error('检查状态失败:', error);\n      }\n    },\n\n    async recordView() {\n      const token = uni.getStorageSync('token');\n      if (!token) {\n        return; // 未登录，不记录\n      }\n      try {\n        await api.recordView({\n          item_type: 'news',\n          item_id: parseInt(this.newsId)\n        });\n        if (this.newsDetail) {\n          this.newsDetail.view_count++;\n        }\n      } catch (error) {\n        console.error('记录浏览失败:', error);\n      }\n    },\n\n    async toggleLike() {\n      const token = uni.getStorageSync('token');\n      if (!token) {\n        this.promptLogin('点赞');\n        return;\n      }\n      try {\n        const res = await api.toggleInteraction({\n          action: 'like',\n          item_type: 'news',\n          item_id: parseInt(this.newsId)\n        });\n        \n        this.isLiked = res.data.action === 'added';\n        this.newsDetail.like_count = res.data.current_count;\n        \n        uni.showToast({\n          title: res.data.message,\n          icon: 'none'\n        });\n      } catch (error) {\n        console.error('点赞操作失败:', error);\n      }\n    },\n\n    async toggleCollect() {\n      const token = uni.getStorageSync('token');\n      if (!token) {\n        this.promptLogin('收藏');\n        return;\n      }\n      try {\n        const res = await api.toggleCollection({\n          item_type: 'news',\n          item_id: parseInt(this.newsId)\n        });\n        \n        this.isCollected = res.data.action === 'added';\n        this.newsDetail.collect_count = res.data.current_collect_count;\n        \n        uni.showToast({\n          title: res.data.message,\n          icon: 'none'\n        });\n      } catch (error) {\n        console.error('收藏操作失败:', error);\n      }\n    },\n\n    onShareAppMessage() {\n      return {\n        title: this.newsDetail.title,\n        path: `/pages/news/detail?id=${this.newsId}`,\n        imageUrl: this.newsDetail.cover_img || ''\n      };\n    },\n\n    promptLogin(action) {\n      uni.showModal({\n        title: '请先登录',\n        content: `登录后才能${action}哦`,\n        success: (res) => {\n          if (res.confirm) {\n            uni.switchTab({\n              url: '/pages/profile/index'\n            });\n          }\n        }\n      });\n    },\n\n    formatDate(dateStr) {\n      const date = new Date(dateStr)\n      const year = date.getFullYear()\n      const month = ('0' + (date.getMonth() + 1)).slice(-2)\n      const day = ('0' + date.getDate()).slice(-2)\n      return `${year}/${month}/${day}`\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.news-detail-page {\n  background-color: #f4f5f7;\n  min-height: 100vh;\n  padding-bottom: 180rpx; // 为悬浮按钮留出空间\n}\n\n.content {\n  padding: 30rpx;\n}\n\n.article-card {\n  background: #ffffff;\n  border-radius: 16rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.article-header {\n  margin-bottom: 30rpx;\n}\n\n.article-title {\n  font-size: 40rpx;\n  font-weight: bold;\n  line-height: 1.4;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.article-meta {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n  gap: 20rpx;\n}\n\n.category {\n  background: #3b82f6;\n  color: white;\n  padding: 8rpx 16rpx;\n  border-radius: 8rpx;\n  font-size: 24rpx;\n}\n\n.publish-date {\n  color: #999;\n  font-size: 26rpx;\n}\n\n.article-stats {\n  display: flex;\n  gap: 30rpx;\n  font-size: 26rpx;\n  color: #888;\n}\n\n.cover-image {\n  width: 100%;\n  height: 400rpx;\n  border-radius: 16rpx;\n  margin-bottom: 40rpx;\n}\n\n.article-body {\n  font-size: 32rpx;\n  line-height: 1.8;\n  color: #333;\n}\n\n.loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200rpx;\n  color: #999;\n}\n\n.action-bar-sticky {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: space-around;\n  padding: 20rpx 0;\n  padding-bottom: constant(safe-area-inset-bottom);\n  padding-bottom: env(safe-area-inset-bottom);\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 100;\n}\n\n.action-btn {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: none;\n  border: none;\n  padding: 10rpx 0;\n  font-size: 24rpx;\n  color: #666;\n  line-height: 1.5;\n  transition: color 0.2s ease-in-out, transform 0.1s ease;\n\n  &::after {\n    border: none;\n  }\n  \n  &:active {\n    transform: scale(0.9);\n  }\n  \n  &.active {\n    color: #3b82f6;\n  }\n  \n  .icon {\n    font-size: 44rpx;\n    margin-bottom: 6rpx;\n    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);\n    opacity: 0.7;\n    filter: grayscale(80%);\n  }\n  \n  &.active .icon {\n    transform: scale(1.1);\n    opacity: 1;\n    filter: grayscale(0%);\n  }\n  \n  .action-text {\n    font-size: 24rpx;\n  }\n}\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=6803dca4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=6803dca4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051452\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}