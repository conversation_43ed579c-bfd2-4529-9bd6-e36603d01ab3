<template>
  <view class="collection-page">
    <view v-if="collectionList.length > 0" class="collection-list">
      <view v-for="item in collectionList" :key="item.id" class="collection-item" @click="goToDetail(item.item_type, item.item_id)">
        <view class="item-header">
          <text class="type-tag">{{ formatType(item.item_type) }}</text>
          <text class="date">{{ formatDate(item.created_at) }}</text>
        </view>
        <view class="item-title">
          <text>{{ item.title || '加载中...' }}</text>
        </view>
        <view v-if="item.content" class="item-content">
          <text>{{ item.content }}</text>
        </view>
      </view>
    </view>

    <view v-if="!loading && collectionList.length === 0" class="empty-state">
      <text class="empty-icon">⭐</text>
      <text class="empty-text">暂无收藏记录</text>
    </view>

    <view class="loading-tip" v-if="loading">加载中...</view>
    <view class="loading-tip" v-if="!hasMore && collectionList.length > 0">没有更多了</view>
  </view>
</template>

<script>
import { api } from '@/utils/api';

export default {
  data() {
    return {
      collectionList: [],
      currentPage: 1,
      hasMore: true,
      loading: false,
    };
  },
  onLoad() {
    this.loadCollections(true);
  },
  onPullDownRefresh() {
    this.loadCollections(true);
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadCollections();
    }
  },
  methods: {
    async loadCollections(reset = false) {
      if (this.loading) return;
      this.loading = true;

      if (reset) {
        this.currentPage = 1;
        this.collectionList = [];
      }

      try {
        const res = await api.getMyCollections({
          page: this.currentPage,
          per_page: 20,
        });
        
        const newList = res.data?.items || [];
        
        // 为每个收藏项获取具体内容信息
        const enrichedList = await this.enrichCollectionData(newList);
        
        this.collectionList = this.collectionList.concat(enrichedList);
        this.hasMore = newList.length === 20;
        this.currentPage++;
        
      } catch (error) {
        console.error("加载收藏列表失败", error);
        if (error.data && error.data.code === 401) {
            uni.showToast({ title: '请先登录', icon: 'none' });
            uni.switchTab({ url: '/pages/profile/index' });
        }
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },
    
    async enrichCollectionData(collectionList) {
      const enrichedList = [];
      
      for (const item of collectionList) {
        try {
          // 根据item_type获取具体内容
          let contentData = null;
          
          switch (item.item_type) {
            case 'policy':
              const policyRes = await api.getPolicyDetail(item.item_id);
              contentData = policyRes.data;
              break;
            case 'news':
              const newsRes = await api.getNewsDetail(item.item_id);
              contentData = newsRes.data;
              break;
            case 'faq':
              const faqRes = await api.getFaqDetail(item.item_id);
              contentData = faqRes.data;
              break;
            case 'interpretation':
              const interpretationRes = await api.getInterpretationDetail(item.item_id);
              contentData = interpretationRes.data;
              break;
          }
          
          // 合并数据
          enrichedList.push({
            ...item,
            title: contentData?.title || contentData?.question || '未知标题',
            content: contentData?.content || contentData?.answer || ''
          });
          
        } catch (error) {
          console.error(`获取${item.item_type}详情失败:`, error);
          // 如果获取详情失败，至少显示基本信息
          enrichedList.push({
            ...item,
            title: '获取失败',
            content: ''
          });
        }
      }
      
      return enrichedList;
    },
    
    formatDate(dateStr) {
      return new Date(dateStr).toLocaleDateString();
    },
    formatType(type) {
        const typeMap = {
            'news': '新闻',
            'policy': '政策',
            'faq': '问答',
            'interpretation': '解读'
        };
        return typeMap[type] || '未知';
    },
    goToDetail(type, id) {
        const urlMap = {
            'news': '/pages/news/detail',
            'policy': '/pages/policy/detail',
            'faq': '/pages/faq/detail',
            'interpretation': '/pages/interpretation/detail'
        };
        const url = urlMap[type];
        if (url) {
            uni.navigateTo({ url: `${url}?id=${id}` });
        }
    }
  }
}
</script>

<style lang="scss" scoped>
.collection-page {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding: 20rpx 0;
}

.collection-list {
  padding: 0 30rpx;
}

.collection-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.type-tag {
  background-color: #1E90FF;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.date {
  font-size: 24rpx;
  color: #999;
}

.item-title {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.item-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.empty-state, .loading-tip {
  text-align: center;
  padding-top: 100rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}
</style> 