@charset "UTF-8";
.consultation-page.data-v-17138ff6 {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  padding-bottom: 140rpx;
  /* 为自定义tabBar留出空间 */
  position: relative;
}
/* 顶部重庆山城风格背景 */
.consultation-page.data-v-17138ff6::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(180deg, #1E90FF 0%, #4A90E2 25%, #6F80FF 50%, rgba(111, 128, 255, 0.7) 70%, rgba(175, 184, 255, 0.4) 85%, rgba(223, 228, 255, 0.2) 95%, transparent 100%);
  z-index: 1;
}
/* 重庆山城剪影装饰 */
.consultation-page.data-v-17138ff6::after {
  content: '';
  position: absolute;
  top: 180rpx;
  left: 0;
  right: 0;
  height: 100rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 100'%3E%3Cpath d='M0,80 L50,75 L100,70 L150,65 L200,60 L250,65 L300,55 L350,60 L400,50 L450,55 L500,45 L550,50 L600,40 L650,45 L700,35 L750,40 L800,30 L850,35 L900,25 L950,30 L1000,20 L1050,25 L1100,15 L1150,20 L1200,10 L1200,100 L0,100 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 100rpx;
  z-index: 2;
  opacity: 0.8;
}
.section.data-v-17138ff6 {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  z-index: 3;
}
.section-title.data-v-17138ff6 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.type-section.data-v-17138ff6 {
  margin: 0 30rpx 30rpx;
  margin-top: 0;
}
.type-options.data-v-17138ff6 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  padding: 30rpx;
}
.type-option.data-v-17138ff6 {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s;
}
.type-option.active.data-v-17138ff6 {
  border-color: #1E90FF;
  background: rgba(30, 144, 255, 0.05);
}
.option-icon.data-v-17138ff6 {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}
.option-title.data-v-17138ff6 {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 12rpx;
}
.option-desc.data-v-17138ff6 {
  font-size: 24rpx;
  color: #999;
}
.bank-section.data-v-17138ff6 {
  margin: 0 30rpx 30rpx;
}
.bank-selector.data-v-17138ff6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: white;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
}
.bank-selected.data-v-17138ff6 {
  font-size: 30rpx;
  color: #333;
}
.bank-placeholder.data-v-17138ff6 {
  font-size: 30rpx;
  color: #999;
}
.bank-arrow.data-v-17138ff6 {
  font-size: 28rpx;
  color: #999;
}
.form-section.data-v-17138ff6 {
  margin: 0 30rpx 30rpx;
}
.form-content.data-v-17138ff6 {
  padding: 30rpx;
}
.form-item.data-v-17138ff6 {
  margin-bottom: 40rpx;
}
.form-label.data-v-17138ff6 {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.required.data-v-17138ff6 {
  color: #1E90FF;
}
.form-input.data-v-17138ff6 {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
  font-size: 30rpx;
  box-sizing: border-box;
  line-height: 88rpx;
}
.form-input.data-v-17138ff6:focus {
  border-color: #1E90FF;
  background: #fff;
}
.form-textarea.data-v-17138ff6 {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
  font-size: 30rpx;
  box-sizing: border-box;
  line-height: 1.6;
}
.form-textarea.data-v-17138ff6:focus {
  border-color: #1E90FF;
  background: #fff;
}
.char-count.data-v-17138ff6 {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}
.form-submit.data-v-17138ff6 {
  margin-top: 40rpx;
  padding-top: 20rpx;
}
.submit-btn.data-v-17138ff6 {
  width: 100%;
  padding: 32rpx 24rpx;
  background: linear-gradient(45deg, #1E90FF, #4A90E2);
  color: white;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(30, 144, 255, 0.3);
  box-sizing: border-box;
  border: none;
  cursor: pointer;
}
.submit-btn.data-v-17138ff6:active:not(.disabled) {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);
}
.submit-btn.disabled.data-v-17138ff6 {
  opacity: 0.5;
  background: #ccc;
  box-shadow: none;
  cursor: not-allowed;
  -webkit-transform: none;
          transform: none;
}
.tip-section.data-v-17138ff6 {
  margin: 0 30rpx 30rpx;
}
.tip-header.data-v-17138ff6 {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
}
.tip-icon.data-v-17138ff6 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.tip-title.data-v-17138ff6 {
  font-size: 32rpx;
  font-weight: bold;
  color: #1E90FF;
}
.tip-content.data-v-17138ff6 {
  padding: 0 30rpx 30rpx;
}
.tip-text.data-v-17138ff6 {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.modal-overlay.data-v-17138ff6 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.bank-picker.data-v-17138ff6 {
  width: 600rpx;
  max-height: 800rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}
.picker-header.data-v-17138ff6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}
.picker-title.data-v-17138ff6 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.picker-close.data-v-17138ff6 {
  font-size: 40rpx;
  color: #999;
}
.picker-list.data-v-17138ff6 {
  max-height: 600rpx;
}
.picker-item.data-v-17138ff6 {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.picker-item.data-v-17138ff6:last-child {
  border-bottom: none;
}
.bank-name.data-v-17138ff6 {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}
.bank-contact.data-v-17138ff6 {
  font-size: 26rpx;
  color: #666;
}
.form-input.error.data-v-17138ff6,
.form-textarea.error.data-v-17138ff6 {
  border-color: #ff4757;
  background: #fff5f5;
}
.error-text.data-v-17138ff6 {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
  display: block;
}

