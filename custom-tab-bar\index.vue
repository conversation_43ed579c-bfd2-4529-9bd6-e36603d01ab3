<template>
  <view class="group_7 flex-col">
    <!-- 顶部分割线 -->
    <image
      class="image_3"
      referrerpolicy="no-referrer"
      src="/static/images/tabbar-top-line.png"
    />

    <!-- 标签栏内容 -->
    <view class="list_1 flex-row">
      <view
        class="image-text_3 flex-col justify-between"
        v-for="(item, index) in loopData0"
        :key="index"
        @tap="switchTab(index)"
      >
        <image
          class="label_3"
          referrerpolicy="no-referrer"
          :src="item.lanhuimage0"
        />
        <text
          class="text-group_3"
          :style="{ color: item.lanhufontColor0 }"
          v-html="item.lanhutext0"
        ></text>
      </view>
    </view>

    <!-- 底部背景 -->
    <image
      class="image_4"
      referrerpolicy="no-referrer"
      src="/static/images/tabbar-bottom-bg.png"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 2, // 默认选中咨询页面（索引2）
      loopData0: [
        {
          lanhuimage0: '/static/images/tabbar/home-icon.png',
          lanhutext0: '首页',
          lanhufontColor0: 'rgba(147,152,160,1.000000)',
          pagePath: '/pages/index/index'
        },
        {
          lanhuimage0: '/static/images/tabbar/policy-icon.png',
          lanhutext0: '政策',
          lanhufontColor0: 'rgba(147,152,160,1.000000)',
          pagePath: '/pages/policy/index'
        },
        {
          lanhuimage0: '/static/images/tabbar/consult-icon-active.png',
          lanhutext0: '咨询',
          lanhufontColor0: 'rgba(31,115,255,1.000000)',
          pagePath: '/pages/consultation/index'
        },
        {
          lanhuimage0: '/static/images/tabbar/profile-icon.png',
          lanhutext0: '我的',
          lanhufontColor0: 'rgba(147,152,160,1.000000)',
          pagePath: '/pages/profile/index'
        }
      ]
    }
  },

  watch: {
    $route: {
      handler(to) {
        if (!to || !to.path) return;
        const targetIndex = this.loopData0.findIndex(item => item.pagePath === to.path);
        if (targetIndex !== -1) {
          this.updateTabState(targetIndex);
        }
      },
      immediate: true, // 立即执行一次，初始化状态
    }
  },
  
  mounted() {
    // 初始化当前tab状态
    this.initCurrentTab();
  },

  methods: {
    // 初始化当前tab状态
    initCurrentTab() {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const currentRoute = '/' + currentPage.route;
        const targetIndex = this.loopData0.findIndex(item => item.pagePath === currentRoute);
        if (targetIndex !== -1) {
          this.updateTabState(targetIndex);
        }
      }
    },

    // 更新tab状态（图标和颜色）
    updateTabState(activeIndex) {
      this.currentTab = activeIndex;

      // 重置所有tab为非激活状态
      this.loopData0.forEach((item, index) => {
        if (index === 0) { // 首页
          item.lanhuimage0 = '/static/images/tabbar/home-icon.png';
          item.lanhufontColor0 = 'rgba(147,152,160,1.000000)';
        } else if (index === 1) { // 政策
          item.lanhuimage0 = '/static/images/tabbar/policy-icon.png';
          item.lanhufontColor0 = 'rgba(147,152,160,1.000000)';
        } else if (index === 2) { // 咨询
          item.lanhuimage0 = '/static/images/tabbar/consult-icon.png';
          item.lanhufontColor0 = 'rgba(147,152,160,1.000000)';
        } else if (index === 3) { // 我的
          item.lanhuimage0 = '/static/images/tabbar/profile-icon.png';
          item.lanhufontColor0 = 'rgba(147,152,160,1.000000)';
        }
      });

      // 设置激活状态
      if (activeIndex >= 0 && activeIndex < this.loopData0.length) {
        const activeItem = this.loopData0[activeIndex];
        if (activeIndex === 0) { // 首页激活
          activeItem.lanhuimage0 = '/static/images/tabbar/home-icon-active.png';
          activeItem.lanhufontColor0 = 'rgba(31,115,255,1.000000)';
        } else if (activeIndex === 1) { // 政策激活
          activeItem.lanhuimage0 = '/static/images/tabbar/policy-icon-active.png';
          activeItem.lanhufontColor0 = 'rgba(31,115,255,1.000000)';
        } else if (activeIndex === 2) { // 咨询激活
          activeItem.lanhuimage0 = '/static/images/tabbar/consult-icon-active.png';
          activeItem.lanhufontColor0 = 'rgba(31,115,255,1.000000)';
        } else if (activeIndex === 3) { // 我的激活
          activeItem.lanhuimage0 = '/static/images/tabbar/profile-icon-active.png';
          activeItem.lanhufontColor0 = 'rgba(31,115,255,1.000000)';
        }
      }
    },

    // 更新选中状态（供外部调用）
    updateSelected(index) {
      if (typeof index === 'number' && index >= 0 && index < this.loopData0.length) {
        this.updateTabState(index);
      }
    },

    // 切换tab
    switchTab(index) {
      if (this.currentTab === index) return;

      const tabItem = this.loopData0[index];

      // 添加点击反馈
      uni.vibrateShort({
        type: 'light'
      });

      uni.switchTab({
        url: tabItem.pagePath,
        success: () => {
          this.updateTabState(index);
        },
        fail: (err) => {
          console.error('切换Tab失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    // 获取当前tab索引
    getCurrentTabIndex() {
      return this.currentTab;
    },

    // 获取tab配置
    getTabConfig(index) {
      return this.loopData0[index] || null;
    }
  }
}
</script>

<style lang="scss" scoped>
// 引入SCSS变量（保持与项目一致）
@import '@/static/css/variables.scss';

.group_7 {
  background-color: rgba(255, 255, 255, 1);
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: 166rpx;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);

  .image_3 {
    width: 750rpx;
    height: 2rpx;
  }

  .list_1 {
    width: 740rpx;
    height: 80rpx;
    margin: 14rpx 0 0 4rpx;

    .image-text_3 {
      width: 188rpx;
      height: 80rpx;
      margin-right: -4rpx;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        opacity: 0.8;
      }

      .label_3 {
        width: 48rpx;
        height: 48rpx;
        margin-left: 70rpx;
        transition: all 0.3s ease;
      }

      .text-group_3 {
        width: 188rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        font-size: 20rpx;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: center;
        white-space: nowrap;
        line-height: 28rpx;
        margin-top: 4rpx;
        transition: all 0.3s ease;
      }
    }
  }

  .image_4 {
    width: 750rpx;
    height: 68rpx;
    margin-top: 2rpx;
  }
}

// 适配安全区域
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .group_7 {
    padding-bottom: calc(env(safe-area-inset-bottom) + 0rpx);
  }
}

// 响应式适配（可选）
@media (max-width: 375px) {
  .group_7 {
    .list_1 {
      .image-text_3 {
        width: 25%;

        .label_3 {
          margin-left: calc(50% - 24rpx);
        }
      }
    }
  }
}
</style>