{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?e8c4", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?6fbd", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?96ce", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?6036", "uni-app:///pages/index/index.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?7669", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?2cb4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "CustomTabBar", "data", "bannerList", "statistics", "policyCount", "visitCount", "consultCount", "bankList", "newsList", "faqList", "interpretationList", "onLoad", "console", "onPullDownRefresh", "then", "uni", "catch", "onShow", "methods", "loadPageData", "title", "Promise", "loadBanners", "api", "res", "banners", "filter", "sort", "map", "imageUrl", "id", "image", "link_url", "sort_order", "is_active", "created_at", "loadStatistics", "bankCount", "loadBanks", "per_page", "banks", "name", "contact_person", "phone", "address", "latitude", "longitude", "icon", "loadNews", "news", "content", "category", "cover_img", "view_count", "like_count", "collect_count", "forward_count", "publish_date", "loadFAQ", "loadInterpretations", "interpretations", "video_url", "toSearch", "url", "toPage", "toNewsDetail", "toFAQDetail", "toInterpretationDetail", "toNewsList", "toFAQList", "toInterpretationList", "toBankListPage", "refreshBanks", "random", "mockBanks", "shuffled", "viewBankDetail", "showCancel", "callPhone", "confirmText", "cancelText", "success", "phoneNumber", "fail", "openMap", "fallbackToAddressSearch", "recordPageView", "action", "item_type", "item_id", "formatDate", "onBannerClick", "recordBannerClick", "content_type", "content_id", "onBannerImageError", "onBannerImageLoad", "getCurrentTime", "getVideoThumbnail", "onVideoThumbnailError", "getNewsCover", "onNewsCoverError", "formatViewCount", "getNewsCategoryIcon", "getNewsCategoryType", "getBankIcon", "onBankIconError"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoQp4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;QACAC;MACA;IACA;MACAA;IACA;EACA;EACAC;IACA,oBACAC;MACAC;IACA,GACAC;MACAJ;MACAG;IACA;EACA;EACAE;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAJ;kBAAAK;gBAAA;;gBAEA;gBAAA;gBAAA,OACAC,aACA,qBACA,wBACA,mBACA,kBACA,iBACA,4BACA;cAAA;gBAEAN;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACAZ;;gBAEA;gBACAa;gBACAb;;gBAEA;gBACA,4BACAc;kBAAA;gBAAA;gBAAA,CACAC;kBAAA;gBAAA;gBAAA,CACAC;kBACA;kBACA;;kBAEA;kBACA;oBACA;oBACAhB;kBACA;oBACA;oBACAiB;oBACAjB;kBACA;kBAEA;oBACAkB;oBACAV;oBACAW;oBACAC;oBAAA;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAEAvB;;gBAEA;gBACA;kBACAA;kBACA,qBACA;oBACAkB;oBACAV;oBACAW;oBACAC;oBACAC;oBACAC;kBACA,GACA;oBACAJ;oBACAV;oBACAW;oBACAC;oBACAC;oBACAC;kBACA,GACA;oBACAJ;oBACAV;oBACAW;oBACAC;oBACAC;oBACAC;kBACA,EACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAtB;;gBAEA;gBACA,qBACA;kBACAkB;kBACAV;kBACAW;kBACAC;kBACAC;kBACAC;gBACA,GACA;kBACAJ;kBACAV;kBACAW;kBACAC;kBACAC;kBACAC;gBACA,GACA;kBACAJ;kBACAV;kBACAW;kBACAC;kBACAC;kBACAC;gBACA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAb;cAAA;gBAAAC;gBACAZ;;gBAEA;gBACA;kBACA;oBACAR;oBACAiC;oBACA/B;oBACAD;kBACA;gBACA;gBACAO;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;kBACAR;kBACAiC;kBACA/B;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAf;kBAAAgB;gBAAA;cAAA;gBAAAf;gBACAZ;;gBAEA;gBACA4B;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA;gBACA5B;;gBAEA;gBACA;kBACAA;kBACA,mBACA;oBACAkB;oBACAW;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAZ;kBACA,GACA;oBACAL;oBACAW;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAZ;kBACA,GACA;oBACAL;oBACAW;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAZ;kBACA,EACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvB;gBACA;gBACA,mBACA;kBACAkB;kBACAW;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAZ;gBACA,GACA;kBACAL;kBACAW;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAZ;gBACA,GACA;kBACAL;kBACAW;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAZ;gBACA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAzB;kBAAAgB;gBAAA;cAAA;gBAAAf;gBACAZ;;gBAEA;gBACAqC;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA;gBACArC;;gBAEA;gBACA;kBACAA;kBACA,mBACA;oBACAkB;oBACAV;oBACA8B;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAtB;kBACA,GACA;oBACAL;oBACAV;oBACA8B;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAtB;kBACA,GACA;oBACAL;oBACAV;oBACA8B;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAtB;kBACA,EACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvB;gBACA;gBACA,mBACA;kBACAkB;kBACAV;kBACA8B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAtB;gBACA,GACA;kBACAL;kBACAV;kBACA8B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAtB;gBACA,GACA;kBACAL;kBACAV;kBACA8B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAtB;gBACA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAnC;kBAAAgB;kBAAAZ;gBAAA;cAAA;gBAAAH;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA+C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEApC;kBAAAgB;gBAAA;cAAA;gBAAAf;gBACAZ;;gBAEA;gBACAgD;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA;gBACAhD;;gBAEA;gBACA;kBACAA;kBACA,6BACA;oBACAkB;oBACAV;oBACA8B;oBACAW;oBACAR;oBACAC;oBACAC;oBACAE;kBACA,GACA;oBACA3B;oBACAV;oBACA8B;oBACAW;oBACAR;oBACAC;oBACAC;oBACAE;kBACA,GACA;oBACA3B;oBACAV;oBACA8B;oBACAW;oBACAR;oBACAC;oBACAC;oBACAE;kBACA,GACA;oBACA3B;oBACAV;oBACA8B;oBACAW;oBACAR;oBACAC;oBACAC;oBACAE;kBACA,GACA;oBACA3B;oBACAV;oBACA8B;oBACAW;oBACAR;oBACAC;oBACAC;oBACAE;kBACA,GACA;oBACA3B;oBACAV;oBACA8B;oBACAW;oBACAR;oBACAC;oBACAC;oBACAE;kBACA,EACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA7C;gBACA;gBACA,6BACA;kBACAkB;kBACAV;kBACA8B;kBACAW;kBACAR;kBACAC;kBACAC;kBACAE;gBACA,GACA;kBACA3B;kBACAV;kBACA8B;kBACAW;kBACAR;kBACAC;kBACAC;kBACAE;gBACA,GACA;kBACA3B;kBACAV;kBACA8B;kBACAW;kBACAR;kBACAC;kBACAC;kBACAE;gBACA,GACA;kBACA3B;kBACAV;kBACA8B;kBACAW;kBACAR;kBACAC;kBACAC;kBACAE;gBACA,GACA;kBACA3B;kBACAV;kBACA8B;kBACAW;kBACAR;kBACAC;kBACAC;kBACAE;gBACA,GACA;kBACA3B;kBACAV;kBACA8B;kBACAW;kBACAR;kBACAC;kBACAC;kBACAE;gBACA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAK;MACA/C;QACAgD;MACA;IACA;IAEAC;MACAjD;QACAgD;MACA;IACA;IAEAE;MACAlD;QACAgD;MACA;IACA;IAEAG;MACAnD;QACAgD;MACA;IACA;IAEAI;MACApD;QACAgD;MACA;IACA;IAEAK;MACArD;QACAgD;MACA;IACA;IAEAM;MACAtD;QACAgD;MACA;IACA;IAEAO;MACAvD;QACAgD;MACA;IACA;IAEAQ;MACAxD;QACAgD;MACA;IACA;IAEAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAjD;kBAAAgB;kBAAAkC;gBAAA;cAAA;gBAAAjD;gBACAZ;;gBAEA;gBACA4B;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA;;gBAEA;gBACA;kBACAkC,aACA;oBACA5C;oBACAW;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAZ;kBACA,GACA;oBACAL;oBACAW;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAZ;kBACA,GACA;oBACAL;oBACAW;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAZ;kBACA,GACA;oBACAL;oBACAW;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAZ;kBACA,GACA;oBACAL;oBACAW;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAZ;kBACA,EACA,EACA;kBACAwC;oBAAA;kBAAA;kBACA;gBACA;gBAEA5D;kBACAK;kBACA2B;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnC;gBACA;gBACA8D,cACA;kBACA5C;kBACAW;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAZ;gBACA,GACA;kBACAL;kBACAW;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAZ;gBACA,GACA;kBACAL;kBACAW;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAZ;gBACA,GACA;kBACAL;kBACAW;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAZ;gBACA,GACA;kBACAL;kBACAW;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAZ;gBACA,EACA,EACA;gBACAwC;kBAAA;gBAAA;gBACA;gBAEA5D;kBACAK;kBACA2B;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA6B;MACA7D;QACAK;QACA8B;QACA2B;MACA;IACA;IAEAC;MACA;QACA/D;UACAK;UACA2B;QACA;QACA;MACA;MAEAhC;QACAK;QACA8B;QACA6B;QACAC;QACAC;UACA;YACAlE;cACAmE;cACAC;gBACAvE;gBACAG;kBACAK;kBACA2B;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAqC;MAAA;MACA;QACArE;UACAK;UACA2B;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QAEAhC;UACA8B;UACAC;UACAL;UACAG;UACAuC;YACAvE;YACA;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEAyE;MACA;MACAtE;QACAK;QACA8B;QACA6B;QACAC;QACAC;UACA;YACA;YACAlE;cACA8B;cACAC;cACAL;cACAG;cACAuC;gBACAvE;gBACAG;kBACAK;kBACA2B;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAuC;MACA;MACA/D;QACAgE;QACAC;QACAC;MACA;QACA7E;MACA;IACA;IAEA8E;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA5E;UACAd;UACAgF;YACAlE;cACAK;cACA2B;YACA;UACA;QACA;MACA;QACA;QACAhC;UACAgD;UACAoB;YACAvE;YACAG;cACAK;cACA2B;YACA;UACA;QACA;MACA;QACA;QACAhC;UACAgD;UACAoB;YACAvE;YACAG;cACAK;cACA2B;YACA;UACA;QACA;MACA;IACA;IAEA;IACA6C;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEArE;kBACAsE;kBACAC;kBACAP;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA3E;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAmF;MACAnF;;MAEA;MACA,qBACA,uCACA,2CACA,0CACA;;MAEA;MACA;QACA;QACAA;MACA;IACA;IAEAoF;MACA;QACApF;MACA;QACAA;MACA;IACA;IAEAqF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;IACA;IAEAC;MACAvF;MACA;MACA;IACA;IAEAwF;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IAEAC;MACAzF;MACA;MACA;IACA;IAEA0F;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEAC;MACA9F;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC72CA;AAAA;AAAA;AAAA;AAA2oD,CAAgB,g/CAAG,EAAC,C;;;;;;;;;;;ACA/pD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=********&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=********&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"********\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=********&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getCurrentTime()\n  var g0 = _vm.bankList && _vm.bankList.length > 0\n  var l0 = _vm.__map(_vm.bankList.slice(0, 3), function (bank, __i0__) {\n    var $orig = _vm.__get_orig(bank)\n    var m1 = g0 ? _vm.getBankIcon(bank) : null\n    var m2 = g0 && m1 ? _vm.getBankIcon(bank) : null\n    return {\n      $orig: $orig,\n      m1: m1,\n      m2: m2,\n    }\n  })\n  var l1 = _vm.__map(_vm.newsList.slice(0, 3), function (news, __i1__) {\n    var $orig = _vm.__get_orig(news)\n    var m3 = _vm.getNewsCover(news)\n    var m4 = m3 ? _vm.getNewsCover(news) : null\n    var m5 = !m3 ? _vm.getNewsCategoryType(news.category) : null\n    var m6 = !m3 ? _vm.getNewsCategoryIcon(news.category) : null\n    var m7 = _vm.formatDate(news.publish_date)\n    var m8 = _vm.formatViewCount(news.view_count)\n    return {\n      $orig: $orig,\n      m3: m3,\n      m4: m4,\n      m5: m5,\n      m6: m6,\n      m7: m7,\n      m8: m8,\n    }\n  })\n  var l2 = _vm.faqList.slice(0, 4)\n  var l3 = _vm.__map(\n    _vm.interpretationList.slice(0, 6),\n    function (item, index) {\n      var $orig = _vm.__get_orig(item)\n      var m9 = _vm.getVideoThumbnail(item, index)\n      var m10 = m9 ? _vm.getVideoThumbnail(item, index) : null\n      return {\n        $orig: $orig,\n        m9: m9,\n        m10: m10,\n      }\n    }\n  )\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, bank) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        bank = _temp2.bank\n      var _temp, _temp2\n      return _vm.viewBankDetail(bank)\n    }\n    _vm.e1 = function ($event, bank) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        bank = _temp4.bank\n      var _temp3, _temp4\n      return _vm.onBankIconError(bank)\n    }\n    _vm.e2 = function ($event, bank) {\n      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        bank = _temp6.bank\n      var _temp5, _temp6\n      $event.stopPropagation()\n      return _vm.callPhone(bank.phone)\n    }\n    _vm.e3 = function ($event, bank) {\n      var _temp7 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp8 = _temp7.eventParams || _temp7[\"event-params\"],\n        bank = _temp8.bank\n      var _temp7, _temp8\n      $event.stopPropagation()\n      return _vm.openMap(bank)\n    }\n    _vm.e4 = function ($event, news) {\n      var _temp9 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp10 = _temp9.eventParams || _temp9[\"event-params\"],\n        news = _temp10.news\n      var _temp9, _temp10\n      return _vm.toNewsDetail(news.id)\n    }\n    _vm.e5 = function ($event, news) {\n      var _temp11 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp12 = _temp11.eventParams || _temp11[\"event-params\"],\n        news = _temp12.news\n      var _temp11, _temp12\n      return _vm.onNewsCoverError(news)\n    }\n    _vm.e6 = function ($event, faq) {\n      var _temp13 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp14 = _temp13.eventParams || _temp13[\"event-params\"],\n        faq = _temp14.faq\n      var _temp13, _temp14\n      return _vm.toFAQDetail(faq.id)\n    }\n    _vm.e7 = function ($event, item) {\n      var _temp15 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp16 = _temp15.eventParams || _temp15[\"event-params\"],\n        item = _temp16.item\n      var _temp15, _temp16\n      return _vm.toInterpretationDetail(item.id)\n    }\n    _vm.e8 = function ($event, item, index) {\n      var _temp17 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp18 = _temp17.eventParams || _temp17[\"event-params\"],\n        item = _temp18.item,\n        index = _temp18.index\n      var _temp17, _temp18\n      return _vm.onVideoThumbnailError(item, index)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"index-page\">\n    <!-- 顶部搜索栏 -->\n    <view class=\"search-header\">\n      <view class=\"search-box\" @click=\"toSearch\">\n        <uni-icons type=\"search\" color=\"#999\" size=\"18\"></uni-icons>\n        <text class=\"search-placeholder\">🏔️ 搜索山城跨境政策、咨询...</text>\n      </view>\n    </view>\n\n    <!-- 轮播图 -->\n    <view class=\"banner-section\">\n      <swiper class=\"banner-swiper\" autoplay interval=\"3000\" duration=\"500\" circular indicator-dots indicator-color=\"rgba(255,255,255,0.5)\" indicator-active-color=\"#1E90FF\">\n        <swiper-item v-for=\"(banner, index) in bannerList\" :key=\"banner.id || index\" @click=\"onBannerClick(banner)\">\n          <image \n            :src=\"banner.image\" \n            class=\"banner-image\" \n            mode=\"aspectFill\"\n            @error=\"onBannerImageError(banner, index)\"\n            @load=\"onBannerImageLoad(banner)\"\n          ></image>\n          <view class=\"banner-overlay\">\n            <text class=\"banner-title\">{{ banner.title }}</text>\n          </view>\n        </swiper-item>\n      </swiper>\n    </view>\n\n    <!-- 功能菜单 -->\n    <view class=\"menu-section section\">\n      <view class=\"menu-grid\">\n        <view class=\"menu-item\" @click=\"toPage('/pages/policy/index')\">\n          <view class=\"menu-icon\">\n            <text class=\"icon-font\" style=\"color: #1E90FF; font-size: 48rpx;\">📋</text>\n          </view>\n          <text class=\"menu-text\">政策文件</text>\n        </view>\n        <view class=\"menu-item\" @click=\"toFAQList\">\n          <view class=\"menu-icon\">\n            <text class=\"icon-font\" style=\"color: #1E90FF; font-size: 48rpx;\">🤔</text>\n          </view>\n          <text class=\"menu-text\">热门问答</text>\n        </view>\n        <view class=\"menu-item\" @click=\"toPage('/pages/consultation/index')\">\n          <view class=\"menu-icon\">\n            <text class=\"icon-font\" style=\"color: #1E90FF; font-size: 48rpx;\">💬</text>\n          </view>\n          <text class=\"menu-text\">业务咨询</text>\n        </view>\n        <view class=\"menu-item\" @click=\"toInterpretationList\">\n          <view class=\"menu-icon\">\n            <text class=\"icon-font\" style=\"color: #1E90FF; font-size: 48rpx;\">📺</text>\n          </view>\n          <text class=\"menu-text\">政策解读</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 数据统计 -->\n    <view class=\"stats-section\">\n      <view class=\"stats-header\">\n        <text class=\"stats-title\">🏙️ 服务数据统计</text>\n        <text class=\"stats-time\">截至：{{ getCurrentTime() }}</text>\n      </view>\n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <view class=\"stats-icon\">📋</view>\n          <view class=\"stats-info\">\n            <text class=\"stats-number\">{{ statistics.policyCount || 0 }}</text>\n            <text class=\"stats-unit\">份</text>\n          </view>\n          <text class=\"stats-label\">已收录政策文件</text>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"stats-icon\">💬</view>\n          <view class=\"stats-info\">\n            <text class=\"stats-number\">{{ statistics.consultCount || 0 }}</text>\n            <text class=\"stats-unit\">次</text>\n          </view>\n          <text class=\"stats-label\">已解答业务咨询</text>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"stats-icon\">👀</view>\n          <view class=\"stats-info\">\n            <text class=\"stats-number\">{{ statistics.visitCount || 0 }}</text>\n            <text class=\"stats-unit\">次</text>\n          </view>\n          <text class=\"stats-label\">累计访问量</text>\n        </view>\n\n        <view class=\"stats-card\">\n          <view class=\"stats-icon\">🏦</view>\n          <view class=\"stats-info\">\n            <text class=\"stats-number\">{{ statistics.bankCount || 0 }}</text>\n            <text class=\"stats-unit\">个</text>\n          </view>\n          <text class=\"stats-label\">在线机构数量</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 银行机构 -->\n    <view class=\"bank-section section\">\n      <view class=\"section-header\">\n        <view class=\"section-title-wrap\">\n          <text class=\"section-icon\">🏦金融机构</text>\n          <text class=\"section-subtitle\">服务重庆跨境融资发展</text>\n        </view>\n        <view class=\"more-actions\">\n          <text class=\"action-btn refresh-btn\" @click=\"refreshBanks\">🔄</text>\n          <text class=\"action-btn more-btn\" @click=\"toBankListPage\">更多</text>\n        </view>\n      </view>\n      <view class=\"bank-grid\">\n        <view v-if=\"bankList && bankList.length > 0\" class=\"bank-card\" v-for=\"bank in bankList.slice(0, 3)\" :key=\"bank.id\" @click=\"viewBankDetail(bank)\">\n          <view class=\"bank-card-header\">\n            <view class=\"bank-logo\">\n              <image \n                v-if=\"getBankIcon(bank)\" \n                :src=\"getBankIcon(bank)\" \n                class=\"bank-logo-image\"\n                mode=\"aspectFill\"\n                @error=\"onBankIconError(bank)\"\n              />\n              <view \n                v-else \n                class=\"bank-logo-placeholder\"\n              >\n                🏦\n              </view>\n            </view>\n            <view class=\"bank-basic-info\">\n              <text class=\"bank-name\">{{ bank.name || '银行名称' }}</text>\n              <view class=\"bank-contact-row\">\n                <text class=\"bank-contact\">{{ bank.contact_person || '联系人' }}</text>\n                <text class=\"bank-phone\" @click.stop=\"callPhone(bank.phone)\">{{ bank.phone || '电话号码' }}</text>\n              </view>\n            </view>\n            <view class=\"bank-actions\">\n              <view class=\"bank-location-btn\" @click.stop=\"openMap(bank)\">\n                <text class=\"location-icon\">📍</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view v-else class=\"bank-empty\">\n          <text class=\"empty-icon\">🏦</text>\n          <text class=\"empty-text\">正在加载银行信息...</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 最新新闻 -->\n    <view class=\"news-section section\">\n      <view class=\"section-header\">\n        <view class=\"section-title-wrap\">\n          <text class=\"section-icon\">🌉最新资讯</text>\n          <text class=\"section-subtitle\">山城跨境金融最新动态</text>\n        </view>\n        <text class=\"action-btn more-btn\" @click=\"toNewsList\">更多</text>\n      </view>\n      <view class=\"news-list\">\n        <view class=\"news-item\" v-for=\"news in newsList.slice(0, 3)\" :key=\"news.id\" @click=\"toNewsDetail(news.id)\">\n          <view class=\"news-cover\">\n            <image \n              v-if=\"getNewsCover(news)\" \n              :src=\"getNewsCover(news)\" \n              class=\"news-cover-image\"\n              mode=\"aspectFill\"\n              @error=\"onNewsCoverError(news)\"\n            />\n            <view \n              v-else \n              class=\"news-cover-placeholder\"\n              :class=\"'news-cover-' + getNewsCategoryType(news.category)\"\n            >\n              {{ getNewsCategoryIcon(news.category) }}\n            </view>\n          </view>\n          <view class=\"news-content\">\n            <text class=\"news-title\">{{ news.title }}</text>\n            <view class=\"news-meta\">\n              <text class=\"news-category\">{{ news.category }}</text>\n              <text class=\"news-date\">{{ formatDate(news.publish_date) }}</text>\n              <text class=\"news-views\">👁 {{ formatViewCount(news.view_count) }}</text>\n            </view>\n          </view>\n          <text class=\"news-arrow\">›</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 热门提问 -->\n    <view class=\"faq-section section\">\n      <view class=\"section-header\">\n        <view class=\"section-title-wrap\">\n          <text class=\"section-icon\">🤔热门问答</text>\n          <text class=\"section-subtitle\">山城企业常见问题解答</text>\n        </view>\n        <text class=\"action-btn more-btn\" @click=\"toFAQList\">更多</text>\n      </view>\n      <view class=\"faq-list\">\n        <view class=\"faq-item\" v-for=\"faq in faqList.slice(0, 4)\" :key=\"faq.id\" @click=\"toFAQDetail(faq.id)\">\n          <view class=\"faq-icon\">❓</view>\n          <view class=\"faq-content\">\n            <text class=\"faq-question\">{{ faq.question }}</text>\n            <view class=\"faq-stats\">\n              <text class=\"faq-stat\">👁 {{ faq.view_count || 0 }}</text>\n              <text class=\"faq-stat\">👍 {{ faq.like_count || 0 }}</text>\n            </view>\n          </view>\n          <text class=\"faq-arrow\">›</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 政策解读 -->\n    <view class=\"interpretation-section section\">\n      <view class=\"section-header\">\n        <view class=\"section-title-wrap\">\n          <text class=\"section-icon\">📺政策解读</text>\n          <text class=\"section-subtitle\">重庆跨境政策专业解读</text>\n        </view>\n        <text class=\"action-btn more-btn\" @click=\"toInterpretationList\">更多</text>\n      </view>\n      <view class=\"interpretation-grid\">\n        <view class=\"interpretation-item\" v-for=\"(item, index) in interpretationList.slice(0, 6)\" :key=\"item.id\" @click=\"toInterpretationDetail(item.id)\">\n          <view class=\"interpretation-cover\">\n            <image \n              v-if=\"getVideoThumbnail(item, index)\" \n              :src=\"getVideoThumbnail(item, index)\" \n              class=\"interpretation-image\"\n              mode=\"aspectFill\"\n              @error=\"onVideoThumbnailError(item, index)\"\n            />\n            <view \n              v-else \n              class=\"interpretation-image\"\n              :class=\"'interpretation-bg-' + (index % 6 + 1)\"\n            ></view>\n            <view v-if=\"item.video_url\" class=\"play-icon\">\n              <text>▶</text>\n            </view>\n            <view v-else class=\"text-icon\">\n              <text>📄</text>\n            </view>\n          </view>\n          <text class=\"interpretation-item-title\">{{ item.title || `课程${index + 1}：利用...` }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 自定义TabBar -->\n    <custom-tab-bar />\n  </view>\n</template>\n\n<script>\nimport { api } from '@/utils/api'\nimport CustomTabBar from '@/custom-tab-bar/index.vue'\n\nexport default {\n  components: {\n    CustomTabBar\n  },\n  data() {\n    return {\n      bannerList: [],\n      statistics: {\n        policyCount: 0,\n        visitCount: 0,\n        consultCount: 0\n      },\n      bankList: [],\n      newsList: [],\n      faqList: [],\n      interpretationList: []\n    }\n  },\n  onLoad() {\n    try {\n      this.loadPageData().catch(err => {\n        console.error('页面数据加载失败:', err)\n      })\n    } catch (err) {\n      console.error('onLoad错误:', err)\n    }\n  },\n  onPullDownRefresh() {\n    this.loadPageData()\n      .then(() => {\n        uni.stopPullDownRefresh()\n      })\n      .catch(err => {\n        console.error('下拉刷新失败:', err)\n        uni.stopPullDownRefresh()\n      })\n  },\n  onShow() {\n    if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {\n      this.$root.$mp.page.getTabBar().$vm.updateSelected(0)\n    }\n    // 记录页面访问\n    this.recordPageView()\n  },\n  methods: {\n    async loadPageData() {\n      try {\n        uni.showLoading({ title: '加载中...' })\n        \n        // 并行加载数据\n        await Promise.all([\n          this.loadBanners(),\n          this.loadStatistics(),\n          this.loadBanks(),\n          this.loadNews(),\n          this.loadFAQ(),\n          this.loadInterpretations()\n        ])\n        \n        uni.hideLoading()\n      } catch (error) {\n        uni.hideLoading()\n        console.error('数据加载失败:', error)\n      }\n    },\n\n    async loadBanners() {\n      try {\n        const res = await api.getBanners()\n        console.log('轮播图API返回:', res)\n        \n        // 根据实际API返回，轮播图数据直接在res.data中（数组格式）\n        const banners = res.data || []\n        console.log('原始轮播图数据:', banners)\n        \n        // 处理轮播图数据，映射字段名\n        this.bannerList = banners\n          .filter(banner => banner.is_active) // 只显示启用的轮播图\n          .sort((a, b) => a.sort - b.sort) // 按sort字段排序\n          .map((banner, index) => {\n            // 处理图片URL\n            let imageUrl = banner.image\n            \n            // 如果是本地API文件URL，确保可以正常访问\n            if (imageUrl && imageUrl.includes('localhost:5000')) {\n              // 如果图片URL看起来正常，保持原样\n              console.log(`轮播图${index + 1}图片URL:`, imageUrl)\n            } else if (imageUrl && imageUrl.startsWith('https://example.com')) {\n              // 替换示例URL为默认图片\n              imageUrl = `/static/images/banner/cq-${index % 3 === 0 ? 'sanxia' : index % 3 === 1 ? 'hongyadong' : 'jiefangbei'}.jpg`\n              console.log(`轮播图${index + 1}使用默认图片:`, imageUrl)\n            }\n            \n            return {\n              id: banner.id,\n              title: banner.title,\n              image: imageUrl,\n              link_url: banner.link === '#' ? '' : banner.link, // 处理无效链接\n              sort_order: banner.sort,\n              is_active: banner.is_active,\n              created_at: banner.created_at\n            }\n          })\n        \n        console.log('处理后的轮播图数据:', this.bannerList)\n        \n        // 如果后端没有轮播图数据或数据为空，使用默认图片\n        if (this.bannerList.length === 0) {\n          console.log('使用默认轮播图')\n          this.bannerList = [\n            {\n              id: 1,\n              title: '重庆长江三峡',\n              image: '/static/images/banner/cq-sanxia.jpg',\n              link_url: '',\n              sort_order: 1,\n              is_active: true\n            },\n            {\n              id: 2,\n              title: '重庆洪崖洞夜景',\n              image: '/static/images/banner/cq-hongyadong.jpg',\n              link_url: '',\n              sort_order: 2,\n              is_active: true\n            },\n            {\n              id: 3,\n              title: '重庆解放碑',\n              image: '/static/images/banner/cq-jiefangbei.jpg',\n              link_url: '',\n              sort_order: 3,\n              is_active: true\n            }\n          ]\n        }\n      } catch (error) {\n        console.error('轮播图数据加载失败:', error)\n        \n        // API失败时使用默认轮播图\n        this.bannerList = [\n          {\n            id: 1,\n            title: '重庆长江三峡',\n            image: '/static/images/banner/cq-sanxia.jpg',\n            link_url: '',\n            sort_order: 1,\n            is_active: true\n          },\n          {\n            id: 2,\n            title: '重庆洪崖洞夜景',\n            image: '/static/images/banner/cq-hongyadong.jpg',\n            link_url: '',\n            sort_order: 2,\n            is_active: true\n          },\n          {\n            id: 3,\n            title: '重庆解放碑',\n            image: '/static/images/banner/cq-jiefangbei.jpg',\n            link_url: '',\n            sort_order: 3,\n            is_active: true\n          }\n        ]\n      }\n    },\n\n    async loadStatistics() {\n      try {\n        const res = await api.getStatistics()\n        console.log('统计数据API返回:', res)\n        \n        // 处理统计数据，确保字段映射正确\n        if (res.data) {\n          this.statistics = {\n            policyCount: res.data.policyCount || res.data.policy_count || 0,\n            bankCount: res.data.bankCount || res.data.bank_count || 0,\n            consultCount: res.data.consultCount || res.data.consult_count || 0,\n            visitCount: res.data.visitCount || res.data.visit_count || 0\n          }\n        }\n        console.log('处理后的统计数据:', this.statistics)\n      } catch (error) {\n        console.error('统计数据加载失败:', error)\n        // API失败时使用默认数据\n        this.statistics = {\n          policyCount: 7,\n          bankCount: 6,\n          consultCount: 9,\n          visitCount: 22\n        }\n      }\n    },\n\n    async loadBanks() {\n      try {\n        const res = await api.getBanks({ per_page: 3 })\n        console.log('银行API返回:', res)\n        \n        // 尝试不同的数据结构\n        let banks = []\n        if (res.data?.items) {\n          banks = res.data.items\n        } else if (res.data?.data) {\n          banks = res.data.data\n        } else if (Array.isArray(res.data)) {\n          banks = res.data\n        }\n        \n        this.bankList = banks || []\n        console.log('处理后的银行列表:', this.bankList)\n        \n        // 如果没有获取到数据，使用模拟数据\n        if (this.bankList.length === 0) {\n          console.log('使用银行模拟数据')\n          this.bankList = [\n            {\n              id: 1,\n              name: '中国银行重庆分行',\n              contact_person: '张经理',\n              phone: '023-********',\n              address: '重庆市渝中区解放碑步行街123号',\n              latitude: 29.559434,\n              longitude: 106.577011,\n              icon: 'https://example.com/bank1.png',\n              created_at: '2025-06-22T09:29:49'\n            },\n            {\n              id: 2,\n              name: '建设银行重庆分行',\n              contact_person: '李经理',\n              phone: '023-********',\n              address: '重庆市江北区观音桥步行街456号',\n              latitude: 29.574639,\n              longitude: 106.539285,\n              icon: 'https://example.com/bank2.png',\n              created_at: '2025-06-22T09:29:49'\n            },\n            {\n              id: 3,\n              name: '工商银行重庆分行',\n              contact_person: '王经理',\n              phone: '023-********',\n              address: '重庆市沙坪坝区三峡广场789号',\n              latitude: 29.544606,\n              longitude: 106.456878,\n              icon: null,\n              created_at: '2025-06-22T09:29:49'\n            }\n          ]\n        }\n      } catch (error) {\n        console.error('银行数据加载失败:', error)\n        // API失败时使用模拟数据\n        this.bankList = [\n          {\n            id: 1,\n            name: '中国银行重庆分行',\n            contact_person: '张经理',\n            phone: '023-********',\n            address: '重庆市渝中区解放碑步行街123号',\n            latitude: 29.559434,\n            longitude: 106.577011,\n            icon: 'https://example.com/bank1.png',\n            created_at: '2025-06-22T09:29:49'\n          },\n          {\n            id: 2,\n            name: '建设银行重庆分行',\n            contact_person: '李经理',\n            phone: '023-********',\n            address: '重庆市江北区观音桥步行街456号',\n            latitude: 29.574639,\n            longitude: 106.539285,\n            icon: 'https://example.com/bank2.png',\n            created_at: '2025-06-22T09:29:49'\n          },\n          {\n            id: 3,\n            name: '工商银行重庆分行',\n            contact_person: '王经理',\n            phone: '023-********',\n            address: '重庆市沙坪坝区三峡广场789号',\n            latitude: 29.544606,\n            longitude: 106.456878,\n            icon: null,\n            created_at: '2025-06-22T09:29:49'\n          }\n        ]\n      }\n    },\n\n    async loadNews() {\n      try {\n        const res = await api.getNews({ per_page: 3 })\n        console.log('新闻API返回:', res)\n        \n        // 尝试不同的数据结构\n        let news = []\n        if (res.data?.items) {\n          news = res.data.items\n        } else if (res.data?.data) {\n          news = res.data.data\n        } else if (Array.isArray(res.data)) {\n          news = res.data\n        }\n        \n        this.newsList = news || []\n        console.log('处理后的新闻列表:', this.newsList)\n        \n        // 如果没有数据，使用模拟数据\n        if (this.newsList.length === 0) {\n          console.log('使用新闻模拟数据')\n          this.newsList = [\n            {\n              id: 1,\n              title: '重庆自贸区跨境融资便利化措施正式发布',\n              content: '<p>重庆自贸区管委会今日正式发布跨境融资便利化措施，新措施将有效降低企业融资成本，提升跨境融资效率...</p>',\n              category: '政策发布',\n              cover_img: 'https://example.com/news1.jpg',\n              view_count: 1256,\n              like_count: 89,\n              collect_count: 23,\n              forward_count: 12,\n              publish_date: '2024-03-15T09:30:00',\n              created_at: '2024-03-15T09:25:00'\n            },\n            {\n              id: 2,\n              title: '外汇局重庆分局召开跨境融资政策解读会',\n              content: '<p>为帮助企业更好理解和运用跨境融资政策，外汇局重庆分局召开专题解读会，详细解读了最新的跨境融资监管政策和操作流程...</p>',\n              category: '会议活动',\n              cover_img: 'https://example.com/news2.jpg',\n              view_count: 892,\n              like_count: 67,\n              collect_count: 15,\n              forward_count: 8,\n              publish_date: '2024-03-14T14:20:00',\n              created_at: '2024-03-14T14:15:00'\n            },\n            {\n              id: 3,\n              title: '重庆企业跨境融资规模创历史新高',\n              content: '<p>据重庆市金融办统计数据显示，2024年第一季度，重庆企业跨境融资总规模达到新高度，同比增长35%...</p>',\n              category: '市场动态',\n              cover_img: 'https://example.com/news3.jpg',\n              view_count: 2134,\n              like_count: 156,\n              collect_count: 43,\n              forward_count: 25,\n              publish_date: '2024-03-13T16:45:00',\n              created_at: '2024-03-13T16:40:00'\n            }\n          ]\n        }\n      } catch (error) {\n        console.error('新闻数据加载失败:', error)\n        // API失败时使用模拟数据\n        this.newsList = [\n          {\n            id: 1,\n            title: '重庆自贸区跨境融资便利化措施正式发布',\n            content: '<p>重庆自贸区管委会今日正式发布跨境融资便利化措施，新措施将有效降低企业融资成本，提升跨境融资效率...</p>',\n            category: '政策发布',\n            cover_img: 'https://example.com/news1.jpg',\n            view_count: 1256,\n            like_count: 89,\n            collect_count: 23,\n            forward_count: 12,\n            publish_date: '2024-03-15T09:30:00',\n            created_at: '2024-03-15T09:25:00'\n          },\n          {\n            id: 2,\n            title: '外汇局重庆分局召开跨境融资政策解读会',\n            content: '<p>为帮助企业更好理解和运用跨境融资政策，外汇局重庆分局召开专题解读会，详细解读了最新的跨境融资监管政策和操作流程...</p>',\n            category: '会议活动',\n            cover_img: 'https://example.com/news2.jpg',\n            view_count: 892,\n            like_count: 67,\n            collect_count: 15,\n            forward_count: 8,\n            publish_date: '2024-03-14T14:20:00',\n            created_at: '2024-03-14T14:15:00'\n          },\n          {\n            id: 3,\n            title: '重庆企业跨境融资规模创历史新高',\n            content: '<p>据重庆市金融办统计数据显示，2024年第一季度，重庆企业跨境融资总规模达到新高度，同比增长35%...</p>',\n            category: '市场动态',\n            cover_img: 'https://example.com/news3.jpg',\n            view_count: 2134,\n            like_count: 156,\n            collect_count: 43,\n            forward_count: 25,\n            publish_date: '2024-03-13T16:45:00',\n            created_at: '2024-03-13T16:40:00'\n          }\n        ]\n      }\n    },\n\n    async loadFAQ() {\n      try {\n        const res = await api.getFaqs({ per_page: 5, sort: 'view_count' })\n        this.faqList = res.data?.items || []\n      } catch (error) {\n        console.error('FAQ数据加载失败:', error)\n      }\n    },\n\n    async loadInterpretations() {\n      try {\n        const res = await api.getInterpretations({ per_page: 6 })\n        console.log('政策解读API返回:', res)\n        \n        // 尝试不同的数据结构\n        let interpretations = []\n        if (res.data?.items) {\n          interpretations = res.data.items\n        } else if (res.data?.data) {\n          interpretations = res.data.data\n        } else if (Array.isArray(res.data)) {\n          interpretations = res.data\n        }\n        \n        this.interpretationList = interpretations || []\n        console.log('处理后的政策解读列表:', this.interpretationList)\n        \n        // 如果没有数据，使用模拟数据\n        if (this.interpretationList.length === 0) {\n          console.log('使用政策解读模拟数据')\n          this.interpretationList = [\n            { \n              id: 1, \n              title: '跨境融资实务操作指南',\n              content: '<p>详细介绍跨境融资的实务操作流程和注意事项...</p>',\n              video_url: 'https://example.com/video1.mp4',\n              view_count: 356,\n              like_count: 53,\n              collect_count: 28,\n              publish_date: '2024-01-23T14:30:00'\n            },\n            { \n              id: 2, \n              title: '外汇收支便民措施详解',\n              content: '<p>全面解读最新的外汇收支便民措施...</p>',\n              video_url: 'https://example.com/video2.mp4',\n              view_count: 298,\n              like_count: 44,\n              collect_count: 25,\n              publish_date: '2024-02-01T09:15:00'\n            },\n            { \n              id: 3, \n              title: '重庆自贸区金融创新政策解读',\n              content: '<p>专门针对重庆自贸区的金融创新政策进行深入解读...</p>',\n              video_url: 'https://example.com/video3.mp4',\n              view_count: 387,\n              like_count: 58,\n              collect_count: 31,\n              publish_date: '2024-02-08T11:45:00'\n            },\n            { \n              id: 4, \n              title: '企业境外投资合规要点',\n              content: '<p>从合规角度详细讲解企业境外投资的各项要求...</p>',\n              video_url: 'https://example.com/video4.mp4',\n              view_count: 267,\n              like_count: 41,\n              collect_count: 22,\n              publish_date: '2024-02-15T15:20:00'\n            },\n            { \n              id: 5, \n              title: '测试政策解答',\n              content: '这是测试政策解答内容',\n              video_url: null,\n              view_count: 2,\n              like_count: 0,\n              collect_count: 0,\n              publish_date: '2025-06-22T00:00:00'\n            },\n            { \n              id: 6, \n              title: '跨境电商外汇支付新规解读',\n              content: '<p>针对跨境电商的最新外汇支付规定进行详细解读...</p>',\n              video_url: 'https://example.com/video6.mp4',\n              view_count: 234,\n              like_count: 36,\n              collect_count: 19,\n              publish_date: '2024-02-20T10:00:00'\n            }\n          ]\n        }\n      } catch (error) {\n        console.error('政策解读数据加载失败:', error)\n        // API失败时使用模拟数据\n        this.interpretationList = [\n          { \n            id: 1, \n            title: '跨境融资实务操作指南',\n            content: '<p>详细介绍跨境融资的实务操作流程和注意事项...</p>',\n            video_url: 'https://example.com/video1.mp4',\n            view_count: 356,\n            like_count: 53,\n            collect_count: 28,\n            publish_date: '2024-01-23T14:30:00'\n          },\n          { \n            id: 2, \n            title: '外汇收支便民措施详解',\n            content: '<p>全面解读最新的外汇收支便民措施...</p>',\n            video_url: 'https://example.com/video2.mp4',\n            view_count: 298,\n            like_count: 44,\n            collect_count: 25,\n            publish_date: '2024-02-01T09:15:00'\n          },\n          { \n            id: 3, \n            title: '重庆自贸区金融创新政策解读',\n            content: '<p>专门针对重庆自贸区的金融创新政策进行深入解读...</p>',\n            video_url: 'https://example.com/video3.mp4',\n            view_count: 387,\n            like_count: 58,\n            collect_count: 31,\n            publish_date: '2024-02-08T11:45:00'\n          },\n          { \n            id: 4, \n            title: '企业境外投资合规要点',\n            content: '<p>从合规角度详细讲解企业境外投资的各项要求...</p>',\n            video_url: 'https://example.com/video4.mp4',\n            view_count: 267,\n            like_count: 41,\n            collect_count: 22,\n            publish_date: '2024-02-15T15:20:00'\n          },\n          { \n            id: 5, \n            title: '测试政策解答',\n            content: '这是测试政策解答内容',\n            video_url: null,\n            view_count: 2,\n            like_count: 0,\n            collect_count: 0,\n            publish_date: '2025-06-22T00:00:00'\n          },\n          { \n            id: 6, \n            title: '跨境电商外汇支付新规解读',\n            content: '<p>针对跨境电商的最新外汇支付规定进行详细解读...</p>',\n            video_url: 'https://example.com/video6.mp4',\n            view_count: 234,\n            like_count: 36,\n            collect_count: 19,\n            publish_date: '2024-02-20T10:00:00'\n          }\n        ]\n      }\n    },\n\n    toSearch() {\n      uni.navigateTo({\n        url: '/pages/search/index'\n      })\n    },\n\n    toPage(url) {\n      uni.switchTab({\n        url: url\n      })\n    },\n\n    toNewsDetail(id) {\n      uni.navigateTo({\n        url: `/pages/news/detail?id=${id}`\n      })\n    },\n\n    toFAQDetail(id) {\n      uni.navigateTo({\n        url: `/pages/faq/detail?id=${id}`\n      })\n    },\n\n    toInterpretationDetail(id) {\n      uni.navigateTo({\n        url: `/pages/interpretation/detail?id=${id}`\n      })\n    },\n\n    toNewsList() {\n      uni.navigateTo({\n        url: '/pages/search/index?type=news'\n      })\n    },\n\n    toFAQList() {\n      uni.navigateTo({\n        url: '/pages/search/index?type=faq'\n      })\n    },\n\n    toInterpretationList() {\n      uni.navigateTo({\n        url: '/pages/search/index?type=interpretation'\n      })\n    },\n\n    toBankListPage() {\n      uni.navigateTo({\n        url: '/pages/bank/list'\n      })\n    },\n\n    async refreshBanks() {\n      try {\n        const res = await api.getBanks({ per_page: 3, random: true })\n        console.log('刷新银行API返回:', res)\n        \n        // 尝试不同的数据结构\n        let banks = []\n        if (res.data?.items) {\n          banks = res.data.items\n        } else if (res.data?.data) {\n          banks = res.data.data\n        } else if (Array.isArray(res.data)) {\n          banks = res.data\n        }\n        \n        this.bankList = banks || []\n        \n        // 如果没有获取到数据，随机显示模拟数据\n        if (this.bankList.length === 0) {\n          const mockBanks = [\n            {\n              id: 1,\n              name: '中国银行重庆分行',\n              contact_person: '张经理',\n              phone: '023-********',\n              address: '重庆市渝中区解放碑步行街123号',\n              latitude: 29.559434,\n              longitude: 106.577011,\n              icon: 'https://example.com/bank1.png',\n              created_at: '2025-06-22T09:29:49'\n            },\n            {\n              id: 2,\n              name: '建设银行重庆分行',\n              contact_person: '李经理',\n              phone: '023-********',\n              address: '重庆市江北区观音桥步行街456号',\n              latitude: 29.574639,\n              longitude: 106.539285,\n              icon: 'https://example.com/bank2.png',\n              created_at: '2025-06-22T09:29:49'\n            },\n            {\n              id: 3,\n              name: '工商银行重庆分行',\n              contact_person: '王经理',\n              phone: '023-********',\n              address: '重庆市沙坪坝区三峡广场789号',\n              latitude: 29.544606,\n              longitude: 106.456878,\n              icon: null,\n              created_at: '2025-06-22T09:29:49'\n            },\n            {\n              id: 4,\n              name: '农业银行重庆分行',\n              contact_person: '刘经理',\n              phone: '023-********',\n              address: '重庆市九龙坡区杨家坪步行街456号',\n              latitude: 29.503143,\n              longitude: 106.511470,\n              icon: 'https://example.com/bank4.png',\n              created_at: '2025-06-22T09:29:49'\n            },\n            {\n              id: 5,\n              name: '交通银行重庆分行',\n              contact_person: '陈经理',\n              phone: '023-********',\n              address: '重庆市南岸区南坪步行街789号',\n              latitude: 29.523456,\n              longitude: 106.560789,\n              icon: null,\n              created_at: '2025-06-22T09:29:49'\n            }\n          ]\n          // 随机选择3个\n          const shuffled = mockBanks.sort(() => 0.5 - Math.random())\n          this.bankList = shuffled.slice(0, 3)\n        }\n        \n        uni.showToast({\n          title: '已刷新',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('刷新银行列表失败:', error)\n        // 使用模拟数据并随机排序\n        const mockBanks = [\n          {\n            id: 1,\n            name: '中国银行重庆分行',\n            contact_person: '张经理',\n            phone: '023-********',\n            address: '重庆市渝中区解放碑步行街123号',\n            latitude: 29.559434,\n            longitude: 106.577011,\n            icon: 'https://example.com/bank1.png',\n            created_at: '2025-06-22T09:29:49'\n          },\n          {\n            id: 2,\n            name: '建设银行重庆分行',\n            contact_person: '李经理',\n            phone: '023-********',\n            address: '重庆市江北区观音桥步行街456号',\n            latitude: 29.574639,\n            longitude: 106.539285,\n            icon: 'https://example.com/bank2.png',\n            created_at: '2025-06-22T09:29:49'\n          },\n          {\n            id: 3,\n            name: '工商银行重庆分行',\n            contact_person: '王经理',\n            phone: '023-********',\n            address: '重庆市沙坪坝区三峡广场789号',\n            latitude: 29.544606,\n            longitude: 106.456878,\n            icon: null,\n            created_at: '2025-06-22T09:29:49'\n          },\n          {\n            id: 4,\n            name: '农业银行重庆分行',\n            contact_person: '刘经理',\n            phone: '023-********',\n            address: '重庆市九龙坡区杨家坪步行街456号',\n            latitude: 29.503143,\n            longitude: 106.511470,\n            icon: 'https://example.com/bank4.png',\n            created_at: '2025-06-22T09:29:49'\n          },\n          {\n            id: 5,\n            name: '交通银行重庆分行',\n            contact_person: '陈经理',\n            phone: '023-********',\n            address: '重庆市南岸区南坪步行街789号',\n            latitude: 29.523456,\n            longitude: 106.560789,\n            icon: null,\n            created_at: '2025-06-22T09:29:49'\n          }\n        ]\n        // 随机选择3个\n        const shuffled = mockBanks.sort(() => 0.5 - Math.random())\n        this.bankList = shuffled.slice(0, 3)\n        \n        uni.showToast({\n          title: '已刷新',\n          icon: 'success'\n        })\n      }\n    },\n\n    viewBankDetail(bank) {\n      uni.showModal({\n        title: bank.name,\n        content: `联系人：${bank.contact_person}\\n电话：${bank.phone}\\n地址：${bank.address}`,\n        showCancel: false\n      })\n    },\n\n    callPhone(phone) {\n      if (!phone || phone === '电话号码') {\n        uni.showToast({\n          title: '电话号码无效',\n          icon: 'none'\n        })\n        return\n      }\n      \n      uni.showModal({\n        title: '拨打电话',\n        content: `是否拨打 ${phone}？`,\n        confirmText: '拨打',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            uni.makePhoneCall({\n              phoneNumber: phone,\n              fail: (err) => {\n                console.error('拨打电话失败:', err)\n                uni.showToast({\n                  title: '拨打失败',\n                  icon: 'none'\n                })\n              }\n            })\n          }\n        }\n      })\n    },\n\n    openMap(bank) {\n      if (!bank.address || bank.address === '地址') {\n        uni.showToast({\n          title: '地址信息无效',\n          icon: 'none'\n        })\n        return\n      }\n      \n      // 如果有经纬度信息，直接使用\n      if (bank.latitude && bank.longitude) {\n        const latitude = typeof bank.latitude === 'number' ? bank.latitude : parseFloat(bank.latitude)\n        const longitude = typeof bank.longitude === 'number' ? bank.longitude : parseFloat(bank.longitude)\n        \n        uni.openLocation({\n          latitude: latitude,\n          longitude: longitude,\n          name: bank.name,\n          address: bank.address,\n          fail: (err) => {\n            console.error('打开地图失败:', err)\n            this.fallbackToAddressSearch(bank)\n          }\n        })\n      } else {\n        // 没有经纬度信息，使用地址搜索\n        this.fallbackToAddressSearch(bank)\n      }\n    },\n\n    fallbackToAddressSearch(bank) {\n      // 备用方案：使用地址搜索\n      uni.showModal({\n        title: '查看位置',\n        content: `地址：${bank.address}\\n\\n是否在地图中搜索该地址？`,\n        confirmText: '打开地图',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            // 尝试使用默认的重庆坐标\n            uni.openLocation({\n              latitude: 29.563761,\n              longitude: 106.550464,\n              name: bank.name,\n              address: bank.address,\n              fail: (err) => {\n                console.error('打开地图失败:', err)\n                uni.showToast({\n                  title: '无法打开地图',\n                  icon: 'none'\n                })\n              }\n            })\n          }\n        }\n      })\n    },\n\n    recordPageView() {\n      // 记录页面访问量\n      api.recordInteraction({\n        action: 'view',\n        item_type: 'page',\n        item_id: 'index'\n      }).catch(err => {\n        console.error('记录访问失败:', err)\n      })\n    },\n\n    formatDate(dateStr) {\n      const date = new Date(dateStr)\n      const now = new Date()\n      const diff = now - date\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n      \n      if (days === 0) {\n        return '今天'\n      } else if (days === 1) {\n        return '昨天'\n      } else if (days < 7) {\n        return `${days}天前`\n      } else {\n        return date.toLocaleDateString()\n      }\n    },\n\n    onBannerClick(banner) {\n      if (!banner.link_url) {\n        return\n      }\n      \n      // 记录轮播图点击事件\n      this.recordBannerClick(banner)\n      \n      // 判断链接类型并进行相应跳转\n      if (banner.link_url.startsWith('http://') || banner.link_url.startsWith('https://')) {\n        // 外部链接，复制到剪贴板并提示\n        uni.setClipboardData({\n          data: banner.link_url,\n          success: () => {\n            uni.showToast({\n              title: '链接已复制到剪贴板',\n              icon: 'none'\n            })\n          }\n        })\n      } else if (banner.link_url.startsWith('/pages/')) {\n        // 内部页面跳转\n        uni.navigateTo({\n          url: banner.link_url,\n          fail: (err) => {\n            console.error('页面跳转失败:', err)\n            uni.showToast({\n              title: '页面跳转失败',\n              icon: 'none'\n            })\n          }\n        })\n      } else {\n        // 其他格式链接，尝试作为内部页面跳转\n        uni.navigateTo({\n          url: banner.link_url,\n          fail: (err) => {\n            console.error('页面跳转失败:', err)\n            uni.showToast({\n              title: '页面跳转失败',\n              icon: 'none'\n            })\n          }\n        })\n      }\n    },\n\n    // 记录轮播图点击事件\n    async recordBannerClick(banner) {\n      try {\n        await api.recordView({\n          content_type: 'banner',\n          content_id: banner.id,\n          action: 'click'\n        })\n      } catch (error) {\n        console.error('记录轮播图点击失败:', error)\n      }\n    },\n\n    onBannerImageError(banner, index) {\n      console.error(`轮播图${index + 1}加载失败:`, banner)\n      \n      // 替换为默认图片\n      const defaultImages = [\n        '/static/images/banner/cq-sanxia.jpg',\n        '/static/images/banner/cq-hongyadong.jpg',\n        '/static/images/banner/cq-jiefangbei.jpg'\n      ]\n      \n      // 更新该轮播图的图片URL\n      if (this.bannerList[index]) {\n        this.$set(this.bannerList[index], 'image', defaultImages[index % 3])\n        console.log(`轮播图${index + 1}已替换为默认图片:`, defaultImages[index % 3])\n      }\n    },\n\n    onBannerImageLoad(banner) {\n      if (banner && banner.id) {\n        console.log(`轮播图${banner.id}加载成功:`, banner.title)\n      } else {\n        console.log('轮播图加载成功，但数据异常:', banner)\n      }\n    },\n\n    getCurrentTime() {\n      const now = new Date()\n      const year = now.getFullYear()\n      const month = String(now.getMonth() + 1).padStart(2, '0')\n      const day = String(now.getDate()).padStart(2, '0')\n      const hour = String(now.getHours()).padStart(2, '0')\n      const minute = String(now.getMinutes()).padStart(2, '0')\n      return `${year}年${month}月${day}日 ${hour}:${minute}`\n    },\n\n    getVideoThumbnail(item, index) {\n      // 如果有真实的缩略图字段，使用真实的\n      if (item && item.thumbnail) {\n        return item.thumbnail\n      }\n      // 如果有封面图字段\n      if (item && item.cover_image) {\n        return item.cover_image\n      }\n      // 如果有视频地址且不为null，尝试生成缩略图\n      if (item && item.video_url && item.video_url !== null) {\n        // 对于示例URL，尝试生成缩略图URL\n        if (item.video_url.includes('example.com')) {\n          return item.video_url.replace('.mp4', '_thumb.jpg')\n        }\n        // 对于其他真实视频URL，可以根据视频平台的规则生成缩略图\n        // 这里可以根据实际需求扩展\n        return item.video_url.replace('.mp4', '_thumb.jpg')\n      }\n      // 返回空字符串，使用CSS渐变背景\n      return ''\n    },\n\n    onVideoThumbnailError(item, index) {\n      console.log(`视频${index + 1}缩略图加载失败:`, item)\n      // 缩略图加载失败时，可以设置一个标记使用CSS背景\n      this.$set(this.interpretationList[index], 'thumbnail_error', true)\n    },\n\n    getNewsCover(news) {\n      // 如果已经标记为加载失败，返回空\n      if (news && news.cover_error) {\n        return ''\n      }\n      \n      // 优先使用后端的cover_img字段\n      if (news && news.cover_img && !news.cover_img.includes('example.com')) {\n        return news.cover_img\n      }\n      // 其次使用thumbnail字段\n      if (news && news.thumbnail && !news.thumbnail.includes('example.com')) {\n        return news.thumbnail\n      }\n      // 再次使用cover_image字段\n      if (news && news.cover_image && !news.cover_image.includes('example.com')) {\n        return news.cover_image\n      }\n      // 如果有image字段也可以使用\n      if (news && news.image && !news.image.includes('example.com')) {\n        return news.image\n      }\n      // 返回空字符串使用占位符\n      return ''\n    },\n\n    onNewsCoverError(news) {\n      console.log('新闻封面加载失败:', news)\n      // 封面加载失败时，设置标记使用占位符\n      this.$set(news, 'cover_error', true)\n    },\n\n    formatViewCount(count) {\n      if (!count || count === 0) return '0'\n      if (count < 1000) return count.toString()\n      if (count < 10000) return (count / 1000).toFixed(1) + 'k'\n      return (count / 10000).toFixed(1) + 'w'\n    },\n\n    getNewsCategoryIcon(category) {\n      const iconMap = {\n        '政策发布': '📋',\n        '会议活动': '🏛️',\n        '数据统计': '📊',\n        '通知公告': '📢',\n        '新闻动态': '📰',\n        '行业资讯': '💼',\n        '法规解读': '⚖️',\n        '业务指导': '📖',\n        '市场分析': '📈',\n        '市场动态': '📈',\n        '测试分类': '🧪',\n        '政策解读': '📋',\n        '金融创新': '💰',\n        '跨境融资': '🌐',\n        '监管政策': '⚖️'\n      }\n      return iconMap[category] || '📰'\n    },\n\n    getNewsCategoryType(category) {\n      const typeMap = {\n        '政策发布': 'policy',\n        '会议活动': 'meeting',\n        '数据统计': 'data',\n        '通知公告': 'notice',\n        '新闻动态': 'news',\n        '行业资讯': 'industry',\n        '法规解读': 'law',\n        '业务指导': 'guide',\n        '市场分析': 'market',\n        '市场动态': 'market',\n        '测试分类': 'test',\n        '政策解读': 'policy',\n        '金融创新': 'finance',\n        '跨境融资': 'finance',\n        '监管政策': 'law'\n      }\n      return typeMap[category] || 'default'\n    },\n\n    getBankIcon(bank) {\n      // 如果已经标记为加载失败，返回空\n      if (bank && bank.icon_error) {\n        return ''\n      }\n      \n      // 优先使用后端的icon字段\n      if (bank && bank.icon && !bank.icon.includes('example.com')) {\n        return bank.icon\n      }\n      \n      // 返回空字符串使用占位符\n      return ''\n    },\n\n    onBankIconError(bank) {\n      console.log('银行图标加载失败:', bank)\n      // 图标加载失败时，设置标记使用占位符\n      this.$set(bank, 'icon_error', true)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.index-page {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);\n  position: relative;\n  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */\n}\n\n/* 顶部重庆山城风格背景 */\n.index-page::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 280rpx;\n  background: linear-gradient(180deg, \n    #1E90FF 0%, \n    #4A90E2 20%, \n    #6BA3E8 40%, \n    rgba(107, 163, 232, 0.7) 65%, \n    rgba(138, 180, 240, 0.4) 80%, \n    rgba(169, 197, 248, 0.2) 90%, \n    rgba(200, 214, 255, 0.1) 95%, \n    transparent 100%\n  );\n  z-index: 1;\n}\n\n/* 重庆山城剪影装饰 */\n.index-page::after {\n  content: '';\n  position: absolute;\n  top: 200rpx;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  background: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M0,100 L50,95 L100,85 L150,90 L200,80 L250,85 L300,75 L350,80 L400,70 L450,75 L500,65 L550,70 L600,60 L650,65 L700,55 L750,60 L800,50 L850,55 L900,45 L950,50 L1000,40 L1050,45 L1100,35 L1150,40 L1200,30 L1200,120 L0,120 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E\") repeat-x;\n  background-size: 1200rpx 120rpx;\n  z-index: 2;\n  opacity: 0.6;\n}\n\n/* 搜索头部 */\n.search-header {\n  padding: 20rpx 20rpx 20rpx;\n  background: transparent;\n  position: relative;\n  z-index: 2;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 50rpx;\n  padding: 30rpx 30rpx;\n  backdrop-filter: blur(15rpx);\n  box-shadow: 0 4rpx 20rpx rgba(255, 255, 255, 0.3);\n  border: 1rpx solid rgba(255, 255, 255, 0.4);\n}\n\n.search-placeholder {\n  margin-left: 20rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n/* 轮播图 */\n.banner-section {\n  margin: 15rpx 20rpx 30rpx 20rpx; /* 稍微调整上边距 */\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: \n    0 12rpx 40rpx rgba(0, 0, 0, 0.1), \n    0 6rpx 20rpx rgba(30, 144, 255, 0.15),\n    0 2rpx 8rpx rgba(248, 113, 162, 0.2); /* 多层柔和阴影 */\n  background: #ffffff;\n  position: relative;\n  z-index: 3;\n  border: 1rpx solid rgba(255, 255, 255, 0.3); /* 添加微妙的边框 */\n}\n\n.banner-swiper {\n  height: 300rpx;\n  position: relative;\n}\n\n.banner-image {\n  width: 100%;\n  height: 100%;\n}\n\n.banner-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));\n  padding: 40rpx 30rpx 20rpx;\n}\n\n.banner-title {\n  color: white;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n/* 功能菜单 */\n.menu-section {\n  margin: 0 20rpx 20rpx;\n}\n\n.menu-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 40rpx;\n  padding: 50rpx 30rpx;\n}\n\n.menu-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n\n.menu-icon {\n  width: 100rpx;\n  height: 100rpx;\n  background: linear-gradient(135deg, #E5F0FF, #F0F8FF);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20rpx;\n  box-shadow: 0 6rpx 20rpx rgba(30, 144, 255, 0.25);\n  position: relative;\n  overflow: hidden;\n}\n\n/* 菜单图标山城装饰 */\n.menu-icon::before {\n  content: '';\n  position: absolute;\n  top: -10rpx;\n  right: -10rpx;\n  width: 40rpx;\n  height: 40rpx;\n  background: radial-gradient(circle, rgba(30, 144, 255, 0.1) 0%, transparent 70%);\n  border-radius: 50%;\n}\n\n.menu-text {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.icon-font {\n  line-height: 1;\n}\n\n/* 数据统计 - 重庆山城风格 */\n.stats-section {\n  margin: 20rpx 30rpx 30rpx;\n  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.3);\n  position: relative;\n}\n\n/* 山城装饰纹理 */\n.stats-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 30l15-15v30zM15 0l15 15H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n  border-radius: 16rpx;\n  pointer-events: none;\n}\n\n.stats-header {\n  background: rgba(255, 255, 255, 0.1);\n  padding: 20rpx 30rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.stats-title {\n  color: white;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.stats-time {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 24rpx;\n}\n\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20rpx;\n  padding: 20rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.stats-card {\n  background: rgb(255, 255, 255);\n  border-radius: 12rpx;\n  padding: 20rpx 10rpx;\n  text-align: center;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.stats-card:active {\n  transform: translateY(-2rpx);\n  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15);\n}\n\n.stats-icon {\n  font-size: 40rpx;\n  margin-bottom: 15rpx;\n  display: block;\n}\n\n.stats-info {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin-bottom: 10rpx;\n}\n\n.stats-number {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #705e67;\n  line-height: 1;\n}\n\n.stats-unit {\n  font-size: 24rpx;\n  color: #705e67;\n  margin-left: 4rpx;\n}\n\n.stats-label {\n  font-size: 16rpx;\n  color: #666;\n  line-height: 1.2;\n}\n\n/* 通用区块样式 - 重庆风格 */\n.section {\n  background: white;\n  margin: 0 30rpx 30rpx;\n  border-radius: 24rpx;\n  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);\n  overflow: hidden;\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(10rpx);\n  position: relative;\n}\n\n/* 为区块添加重庆山城微妙装饰 */\n.section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 80rpx;\n  height: 80rpx;\n  background: linear-gradient(45deg, rgba(220, 20, 60, 0.05) 0%, rgba(220, 20, 60, 0.02) 50%, transparent 100%);\n  border-radius: 0 24rpx 0 80rpx;\n  pointer-events: none;\n}\n\n/* 银行机构 */\n.bank-section {\n  background: white;\n  margin: 0 30rpx 30rpx;\n  border-radius: 24rpx;\n  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);\n  overflow: hidden;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 30rpx 30rpx 20rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.section-title-wrap {\n  display: flex;\n  flex-direction: column;\n}\n\n.section-icon {\n  font-size: 32rpx;\n  margin-bottom: 8rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 4rpx;\n}\n\n.section-subtitle {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.more-actions {\n  display: flex;\n  gap: 15rpx;\n  align-items: center;\n}\n\n.action-btn {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  transition: all 0.3s ease;\n}\n\n.refresh-btn {\n  background: rgba(30, 144, 255, 0.1);\n  color: #1E90FF;\n}\n\n.more-btn {\n  background: #1E90FF;\n  color: white;\n}\n\n.action-btn:active {\n  transform: scale(0.95);\n}\n\n.bank-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n  padding: 0 30rpx 30rpx;\n}\n\n.bank-section .bank-card {\n  background: white;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  border-left: 4rpx solid transparent;\n  color: #333;\n}\n\n.bank-section .bank-card:active {\n  transform: translateY(-2rpx);\n  box-shadow: 0 6rpx 30rpx rgba(30, 144, 255, 0.15);\n  border-left-color: #1E90FF;\n}\n\n.bank-section .bank-card-header {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n}\n\n.bank-logo {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16rpx;\n  flex-shrink: 0;\n  overflow: hidden;\n  background: #f5f5f5;\n}\n\n.bank-logo-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n}\n\n.bank-logo-placeholder {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #1E90FF, #4A90E2);\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24rpx;\n  color: white;\n}\n\n.bank-basic-info {\n  flex: 1;\n  min-width: 0;\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.bank-section .bank-name {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333 !important;\n  margin-bottom: 4rpx;\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.bank-contact-row {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n  flex-wrap: wrap;\n}\n\n.bank-section .bank-contact {\n  font-size: 24rpx;\n  color: #666 !important;\n  flex-shrink: 0;\n}\n\n.bank-section .bank-phone {\n  font-size: 22rpx;\n  color: #1E90FF !important;\n  text-decoration: underline;\n  transition: all 0.3s ease;\n}\n\n.bank-section .bank-phone:active {\n  opacity: 0.7;\n  transform: scale(0.98);\n}\n\n.bank-actions {\n  display: flex;\n  align-items: flex-start;\n  flex-shrink: 0;\n}\n\n.bank-location-btn {\n  width: 48rpx;\n  height: 48rpx;\n  background: linear-gradient(135deg, #1E90FF, #4A90E2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);\n}\n\n.bank-location-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);\n}\n\n.location-icon {\n  font-size: 24rpx;\n  color: white;\n}\n\n.bank-empty {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60rpx 30rpx;\n  text-align: center;\n}\n\n.empty-icon {\n  font-size: 48rpx;\n  margin-bottom: 20rpx;\n  opacity: 0.6;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n\n\n\n/* 新闻列表 */\n.news-section {\n  margin: 0 30rpx 30rpx;\n}\n\n.news-list {\n  padding: 0 30rpx 30rpx;\n}\n\n.news-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 20rpx;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n  transition: all 0.3s ease;\n}\n\n.news-item:last-child {\n  border-bottom: none;\n}\n\n.news-item:active {\n  background: rgba(30, 144, 255, 0.05);\n  margin: 0 -30rpx;\n  padding: 20rpx 30rpx;\n  border-radius: 12rpx;\n}\n\n.news-cover {\n  width: 120rpx;\n  height: 80rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  flex-shrink: 0;\n  background: #f5f5f5;\n}\n\n.news-cover-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.news-cover-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f0f0f0, #e8e8e8);\n  font-size: 32rpx;\n  color: #999;\n}\n\n/* 不同类型新闻的占位符样式 */\n.news-cover-policy {\n  background: linear-gradient(135deg, #ff9a9e, #fecfef) !important;\n  color: #d63384 !important;\n}\n\n.news-cover-meeting {\n  background: linear-gradient(135deg, #a8edea, #fed6e3) !important;\n  color: #0d6efd !important;\n}\n\n.news-cover-data {\n  background: linear-gradient(135deg, #ffeaa7, #fab1a0) !important;\n  color: #fd7e14 !important;\n}\n\n.news-cover-notice {\n  background: linear-gradient(135deg, #74b9ff, #0984e3) !important;\n  color: #ffffff !important;\n}\n\n.news-cover-news {\n  background: linear-gradient(135deg, #fd79a8, #fdcb6e) !important;\n  color: #e84393 !important;\n}\n\n.news-cover-industry {\n  background: linear-gradient(135deg, #6c5ce7, #a29bfe) !important;\n  color: #ffffff !important;\n}\n\n.news-cover-law {\n  background: linear-gradient(135deg, #00b894, #00cec9) !important;\n  color: #ffffff !important;\n}\n\n.news-cover-guide {\n  background: linear-gradient(135deg, #e17055, #fab1a0) !important;\n  color: #ffffff !important;\n}\n\n.news-cover-market {\n  background: linear-gradient(135deg, #00b894, #55efc4) !important;\n  color: #ffffff !important;\n}\n\n.news-cover-default {\n  background: linear-gradient(135deg, #ddd6fe, #e879f9) !important;\n  color: #8b5cf6 !important;\n}\n\n.news-cover-test {\n  background: linear-gradient(135deg, #ff7675, #fd79a8) !important;\n  color: #ffffff !important;\n}\n\n.news-cover-finance {\n  background: linear-gradient(135deg, #fdcb6e, #e17055) !important;\n  color: #ffffff !important;\n}\n\n.news-content {\n  flex: 1;\n  min-width: 0;\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.news-title {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.4;\n  font-weight: 500;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n\n\n.news-meta {\n  display: flex;\n  gap: 16rpx;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.news-category {\n  font-size: 20rpx;\n  color: #1E90FF;\n  background: rgba(30, 144, 255, 0.1);\n  padding: 4rpx 8rpx;\n  border-radius: 8rpx;\n  flex-shrink: 0;\n}\n\n.news-date {\n  font-size: 20rpx;\n  color: #999;\n}\n\n.news-views {\n  font-size: 20rpx;\n  color: #999;\n  display: flex;\n  align-items: center;\n  gap: 2rpx;\n}\n\n.news-arrow {\n  font-size: 32rpx;\n  color: #ccc;\n  font-weight: bold;\n  align-self: center;\n  flex-shrink: 0;\n}\n\n/* 热门提问 */\n.faq-section {\n  margin: 0 30rpx 30rpx;\n}\n\n.faq-list {\n  padding: 0 30rpx 30rpx;\n}\n\n.faq-item {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n  transition: all 0.3s ease;\n}\n\n.faq-item:last-child {\n  border-bottom: none;\n}\n\n.faq-item:active {\n  background: rgba(30, 144, 255, 0.05);\n  margin: 0 -30rpx;\n  padding: 20rpx 30rpx;\n  border-radius: 12rpx;\n}\n\n.faq-icon {\n  font-size: 24rpx;\n  width: 40rpx;\n  height: 40rpx;\n  background: rgba(255, 193, 7, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.faq-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.faq-question {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.4;\n  margin-bottom: 8rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.faq-stats {\n  display: flex;\n  gap: 20rpx;\n}\n\n.faq-stat {\n  font-size: 22rpx;\n  color: #999;\n}\n\n.faq-arrow {\n  font-size: 32rpx;\n  color: #ccc;\n  font-weight: bold;\n}\n\n/* 政策解读 */\n.interpretation-section {\n  margin: 0 20rpx 30rpx;\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);\n}\n\n\n\n.interpretation-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 15rpx;\n  padding: 0 30rpx 30rpx;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.interpretation-item {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  min-height: 160rpx;\n  transition: all 0.3s ease;\n}\n\n.interpretation-item:active {\n  transform: translateY(-2rpx);\n}\n\n.interpretation-cover {\n  position: relative;\n  width: 100%;\n  height: 100rpx;\n  background: #F0F0F0;\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-bottom: 10rpx;\n  flex-shrink: 0;\n}\n\n.interpretation-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 当没有真实图片时使用的渐变背景 */\n.interpretation-bg-1 {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.interpretation-bg-2 {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.interpretation-bg-3 {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.interpretation-bg-4 {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.interpretation-bg-5 {\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\n}\n\n.interpretation-bg-6 {\n  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n}\n\n.play-icon {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80rpx;\n  height: 80rpx;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 32rpx;\n  pointer-events: none;\n}\n\n.text-icon {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80rpx;\n  height: 80rpx;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 40rpx;\n  pointer-events: none;\n}\n\n.interpretation-item-title {\n  padding: 20rpx;\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.3;\n  text-align: center;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  word-break: break-all;\n  height: 52rpx;\n  flex-shrink: 0;\n}\n\n\n</style> \n ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=********&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=********&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051451\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}