<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52" width="52" height="52">
  <defs>
    <linearGradient id="bankGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#DC143C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF6B6B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="26" cy="26" r="24" fill="url(#bankGradient)" stroke="#fff" stroke-width="3"/>
  
  <!-- 银行图标 -->
  <g transform="translate(13, 13)">
    <!-- 银行建筑主体 -->
    <rect x="2" y="12" width="22" height="12" fill="#fff" rx="1"/>
    
    <!-- 银行柱子 -->
    <rect x="4" y="8" width="2" height="8" fill="#fff"/>
    <rect x="8" y="8" width="2" height="8" fill="#fff"/>
    <rect x="12" y="8" width="2" height="8" fill="#fff"/>
    <rect x="16" y="8" width="2" height="8" fill="#fff"/>
    <rect x="20" y="8" width="2" height="8" fill="#fff"/>
    
    <!-- 银行屋顶 -->
    <polygon points="13,4 2,8 24,8" fill="#fff"/>
    
    <!-- 门 -->
    <rect x="11" y="16" width="4" height="6" fill="url(#bankGradient)"/>
    
    <!-- 台阶 -->
    <rect x="1" y="22" width="24" height="2" fill="#fff" opacity="0.8"/>
  </g>
</svg> 