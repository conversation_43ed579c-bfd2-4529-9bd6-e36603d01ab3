@charset "UTF-8";
.policy-detail.data-v-f4b705b6 {
  background: linear-gradient(180deg, #f0f9ff 0%, #f8fafc 40%, #f1f5f9 100%);
  min-height: 100vh;
  position: relative;
}
/* 顶部蓝色渐变背景 */
.policy-detail.data-v-f4b705b6::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, #1E90FF 0%, #007bff 30%, #60a5fa 60%, rgba(96, 165, 250, 0.6) 80%, rgba(147, 197, 253, 0.3) 90%, transparent 100%);
  z-index: 1;
}
/* 重庆山城剪影装饰 */
.policy-detail.data-v-f4b705b6::after {
  content: '';
  position: absolute;
  top: 140rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}
.content.data-v-f4b705b6 {
  padding: 40rpx 30rpx 180rpx;
  /* 增加底部 padding 为悬浮按钮留出空间 */
  position: relative;
  z-index: 3;
}
.policy-container.data-v-f4b705b6 {
  background: #ffffff;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}
.title-section.data-v-f4b705b6 {
  margin-bottom: 30rpx;
}
.divider.data-v-f4b705b6 {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #e5e7eb 80%, transparent 100%);
  margin: 30rpx 0;
}
.title.data-v-f4b705b6 {
  font-size: 44rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 30rpx;
  text-align: justify;
}
.meta-info.data-v-f4b705b6 {
  color: #888;
  font-size: 26rpx;
}
.stats-bar.data-v-f4b705b6 {
  display: inline;
  margin-left: 10rpx;
}
.content-section.data-v-f4b705b6 {
  margin-top: 20rpx;
}
.content-text.data-v-f4b705b6 {
  font-size: 32rpx;
  line-height: 1.8;
  color: #34495e;
}
.attachment-section.data-v-f4b705b6 {
  margin-top: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.attachment-title.data-v-f4b705b6 {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.attachment-item.data-v-f4b705b6 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #e9ecef;
  border-radius: 8rpx;
}
.attachment-icon.data-v-f4b705b6 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.attachment-name.data-v-f4b705b6 {
  color: #007bff;
  font-size: 28rpx;
  text-decoration: underline;
}
.action-bar-sticky.data-v-f4b705b6 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.action-btn.data-v-f4b705b6 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  transition: color 0.2s ease-in-out, -webkit-transform 0.1s ease;
  transition: color 0.2s ease-in-out, transform 0.1s ease;
  transition: color 0.2s ease-in-out, transform 0.1s ease, -webkit-transform 0.1s ease;
}
.action-btn.data-v-f4b705b6::after {
  border: none;
}
.action-btn.data-v-f4b705b6:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.action-btn.active.data-v-f4b705b6 {
  color: #3b82f6;
}
.action-btn .icon.data-v-f4b705b6 {
  font-size: 44rpx;
  margin-bottom: 6rpx;
  transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  opacity: 0.7;
  -webkit-filter: grayscale(80%);
          filter: grayscale(80%);
}
.action-btn.active .icon.data-v-f4b705b6 {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  opacity: 1;
  -webkit-filter: grayscale(0%);
          filter: grayscale(0%);
}
.action-btn .action-text.data-v-f4b705b6 {
  font-size: 24rpx;
}
.loading.data-v-f4b705b6 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
}

