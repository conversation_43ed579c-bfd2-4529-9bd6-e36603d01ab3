<template>
  <view class="interpretation-list-page">
    <!-- 顶部导航 -->
    <view class="page-header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="header-title">
        <text class="title-text">📺 政策解读</text>
        <text class="title-subtitle">重庆跨境政策专业解读</text>
      </view>
      <view class="header-right">
        <text class="refresh-btn" @click="refreshList">🔄</text>
      </view>
    </view>
    


    <!-- 筛选标签 -->
    <view class="filter-section" v-if="categories.length > 0">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-tags">
          <view 
            class="filter-tag" 
            :class="{ active: selectedCategory === '' }"
            @click="selectCategory('')"
          >
            全部
          </view>
          <view 
            v-for="category in categories" 
            :key="category"
            class="filter-tag" 
            :class="{ active: selectedCategory === category }"
            @click="selectCategory(category)"
          >
            {{ category }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 解读列表 -->
    <view class="interpretation-content">
      <view v-if="loading" class="loading-section">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载政策解读...</text>
      </view>

      <view v-else-if="filteredInterpretationList.length === 0" class="empty-section">
        <text class="empty-icon">📺</text>
        <text class="empty-title">暂无相关政策解读</text>
        <text class="empty-subtitle">稍后再来看看</text>
      </view>

      <view v-else class="interpretation-list">
        <view 
          class="interpretation-item" 
          v-for="(item, index) in filteredInterpretationList" 
          :key="item.id" 
          @click="toInterpretationDetail(item.id)"
        >
          <view class="interpretation-cover">
            <image 
              v-if="getVideoThumbnail(item, index)" 
              :src="getVideoThumbnail(item, index)" 
              class="interpretation-image"
              mode="aspectFill"
              @error="onVideoThumbnailError(item, index)"
            />
            <view 
              v-else 
              class="interpretation-image"
              :class="'interpretation-bg-' + (index % 6 + 1)"
            ></view>
            <view v-if="item.video_url" class="play-icon">
              <text>▶</text>
            </view>
            <view v-else class="text-icon">
              <text>📄</text>
            </view>
          </view>
          
          <view class="interpretation-info">
            <text class="interpretation-title">{{ item.title }}</text>
            <view class="interpretation-meta">
              <text class="interpretation-category" v-if="item.category">{{ item.category }}</text>
              <text class="interpretation-date">{{ formatDate(item.created_at) }}</text>
            </view>
            <view class="interpretation-stats">
              <view class="stat-item">
                <text class="stat-icon">👁</text>
                <text class="stat-value">{{ formatViewCount(item.view_count) }}</text>
              </view>
              <view class="stat-item">
                <text class="stat-icon">👍</text>
                <text class="stat-value">{{ item.like_count || 0 }}</text>
              </view>
              <view class="stat-item">
                <text class="stat-icon">💬</text>
                <text class="stat-value">{{ item.comment_count || 0 }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading" class="load-more">
        <text class="load-more-btn" @click="loadMore">加载更多</text>
    </view>
    </view>
  </view>
</template>

<script>
import { api } from '@/utils/api.js'

export default {
  data() {
    return {
      interpretationList: [],
      filteredInterpretationList: [],
      loading: true,
      selectedCategory: '',
      categories: [],
      page: 1,
      perPage: 20,
      hasMore: true,

    }
  },

  onLoad() {
    this.loadInterpretationList()
  },

  onPullDownRefresh() {
    this.refreshList()
  },

  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadInterpretationList() {
      try {
        this.loading = true
        const params = {
          page: this.page,
          per_page: this.perPage
        }
        
        if (this.selectedCategory) {
          params.category = this.selectedCategory
        }
        


        const res = await api.getInterpretations(params)
        console.log('政策解读列表API返回:', res)
        
        if (res.success && res.data) {
          const interpretations = res.data.items || []
          
          if (this.page === 1) {
            this.interpretationList = interpretations
          } else {
            this.interpretationList = [...this.interpretationList, ...interpretations]
          }
          
          this.filteredInterpretationList = this.interpretationList
          this.hasMore = res.data.has_next || false
          
          // 提取分类
          if (this.page === 1) {
            this.extractCategories()
          }
        } else {
          console.error('API返回数据格式错误:', res)
          uni.showToast({
            title: '数据格式错误',
            icon: 'error'
          })
        }
        

        
      } catch (error) {
        console.error('加载政策解读列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },



    extractCategories() {
      const categories = [...new Set(this.interpretationList.map(item => item.category).filter(Boolean))]
      this.categories = categories
    },

    refreshList() {
      this.page = 1
      this.hasMore = true
      this.loadInterpretationList()
    },

    loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++
        this.loadInterpretationList()
      }
    },



    selectCategory(category) {
      this.selectedCategory = category
      this.page = 1
      this.hasMore = true
      this.loadInterpretationList()
    },

    toInterpretationDetail(id) {
      uni.navigateTo({
        url: `/pages/interpretation/detail?id=${id}`
      })
    },

    getVideoThumbnail(item, index) {
      // 模拟视频缩略图
      if (item.video_url) {
        return `https://example.com/thumbnail${index + 1}.jpg`
      }
      return null
    },

    onVideoThumbnailError(item, index) {
      console.log('视频缩略图加载失败:', item, index)
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) {
        return '昨天'
      } else if (diffDays <= 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    },

    formatViewCount(count) {
      if (!count) return '0'
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w'
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k'
      }
      return count.toString()
    }
  }
}
</script>

<style scoped>
.interpretation-list-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.title-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 4rpx;
}

.header-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.refresh-btn {
  font-size: 36rpx;
  color: #1E90FF;
}



.filter-section {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.filter-tag {
  padding: 12rpx 24rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #1E90FF;
  color: white;
}

.interpretation-content {
  padding: 20rpx 0;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
}

.interpretation-list {
  padding: 0 30rpx;
}

.interpretation-item {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  display: flex;
  gap: 20rpx;
}

.interpretation-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.interpretation-cover {
  position: relative;
  width: 200rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.interpretation-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 当没有真实图片时使用的渐变背景 */
.interpretation-bg-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.interpretation-bg-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.interpretation-bg-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.interpretation-bg-4 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.interpretation-bg-5 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.interpretation-bg-6 {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
  pointer-events: none;
}

.text-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
  pointer-events: none;
}

.interpretation-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.interpretation-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.interpretation-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
  margin-bottom: 12rpx;
}

.interpretation-category {
  font-size: 22rpx;
  color: #1E90FF;
  background: rgba(30, 144, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.interpretation-date {
  font-size: 22rpx;
  color: #999;
}

.interpretation-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.stat-icon {
  font-size: 22rpx;
}

.stat-value {
  font-size: 22rpx;
  color: #999;
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #e8e8e8;
}
</style> 