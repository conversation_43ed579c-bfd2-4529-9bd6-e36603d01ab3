<view class="profile-page data-v-14bc1b43"><block wx:if="{{isLoggedIn}}"><view class="user-card cq-decoration data-v-14bc1b43"><view class="user-avatar-section data-v-14bc1b43"><image class="user-avatar data-v-14bc1b43" src="{{userInfo.avatar||'/static/images/default-avatar.png'}}"></image><view class="user-info data-v-14bc1b43"><text class="user-name data-v-14bc1b43">{{userInfo.nickname||'微信用户'}}</text><text class="user-desc data-v-14bc1b43">零距离·汇万家服务用户</text></view></view><view class="user-stats data-v-14bc1b43"><view class="stat-item data-v-14bc1b43"><text class="stat-number data-v-14bc1b43">{{userStats.collectionCount||0}}</text><text class="stat-label data-v-14bc1b43">收藏</text></view><view class="stat-item data-v-14bc1b43"><text class="stat-number data-v-14bc1b43">{{userStats.inquiryCount||0}}</text><text class="stat-label data-v-14bc1b43">咨询</text></view><view class="stat-item data-v-14bc1b43"><text class="stat-number data-v-14bc1b43">{{userStats.viewCount||0}}</text><text class="stat-label data-v-14bc1b43">浏览</text></view></view></view></block><block wx:else><view class="login-prompt-card cq-decoration data-v-14bc1b43"><view class="prompt-info data-v-14bc1b43"><text class="prompt-title data-v-14bc1b43">登录体验更多服务</text><text class="prompt-desc data-v-14bc1b43">收藏、咨询、浏览记录一目了然</text></view><button data-event-opts="{{[['tap',[['handleLogin',['$event']]]]]}}" class="login-btn data-v-14bc1b43" bindtap="__e"><text class="login-btn-text data-v-14bc1b43">微信一键登录</text></button></view></block><view class="menu-section section data-v-14bc1b43"><view class="menu-list data-v-14bc1b43"><view data-event-opts="{{[['tap',[['toCollectionPage',['$event']]]]]}}" class="menu-item data-v-14bc1b43" bindtap="__e"><view class="menu-icon data-v-14bc1b43">⭐</view><text class="menu-title data-v-14bc1b43">我的收藏</text><view class="menu-extra data-v-14bc1b43"><text class="menu-count data-v-14bc1b43">{{userStats.collectionCount||0}}</text><text class="menu-arrow data-v-14bc1b43">></text></view></view><view data-event-opts="{{[['tap',[['toInquiryPage',['$event']]]]]}}" class="menu-item data-v-14bc1b43" bindtap="__e"><view class="menu-icon data-v-14bc1b43">💬</view><text class="menu-title data-v-14bc1b43">我的咨询</text><view class="menu-extra data-v-14bc1b43"><text class="menu-count data-v-14bc1b43">{{userStats.inquiryCount||0}}</text><text class="menu-arrow data-v-14bc1b43">></text></view></view><view data-event-opts="{{[['tap',[['toHistoryPage',['$event']]]]]}}" class="menu-item data-v-14bc1b43" bindtap="__e"><view class="menu-icon data-v-14bc1b43">📖</view><text class="menu-title data-v-14bc1b43">浏览历史</text><view class="menu-extra data-v-14bc1b43"><text class="menu-arrow data-v-14bc1b43">></text></view></view></view></view><view class="service-section section cq-decoration data-v-14bc1b43"><view class="section-title data-v-14bc1b43">联系我们</view><view class="service-list data-v-14bc1b43"><view data-event-opts="{{[['tap',[['openService',['hotline']]]]]}}" class="service-item data-v-14bc1b43" bindtap="__e"><view class="service-icon data-v-14bc1b43">📞</view><view class="service-info data-v-14bc1b43"><text class="service-title data-v-14bc1b43">外汇局咨询热线</text><text class="service-desc data-v-14bc1b43">023-67677161</text></view></view><view data-event-opts="{{[['tap',[['openService',['location']]]]]}}" class="service-item data-v-14bc1b43" bindtap="__e"><view class="service-icon data-v-14bc1b43">📍</view><view class="service-info data-v-14bc1b43"><text class="service-title data-v-14bc1b43">银行网点一览</text><text class="service-desc data-v-14bc1b43">就近银行机构查询</text></view></view></view></view><view class="settings-section section data-v-14bc1b43"><view class="menu-list data-v-14bc1b43"><view data-event-opts="{{[['tap',[['clearCache',['$event']]]]]}}" class="menu-item data-v-14bc1b43" bindtap="__e"><view class="menu-icon data-v-14bc1b43">🗑️</view><text class="menu-title data-v-14bc1b43">清除缓存</text><view class="menu-extra data-v-14bc1b43"><text class="menu-arrow data-v-14bc1b43">></text></view></view><view data-event-opts="{{[['tap',[['aboutApp',['$event']]]]]}}" class="menu-item data-v-14bc1b43" bindtap="__e"><view class="menu-icon data-v-14bc1b43">ℹ️</view><text class="menu-title data-v-14bc1b43">关于我们</text><view class="menu-extra data-v-14bc1b43"><text class="menu-arrow data-v-14bc1b43">></text></view></view></view></view><view class="version-info data-v-14bc1b43"><text class="version-text data-v-14bc1b43">零距离·汇万家服务</text><text class="copyright-text data-v-14bc1b43">© 2025 重庆市商务委员会</text></view><block wx:if="{{showFeedbackModal}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="modal-overlay data-v-14bc1b43" bindtap="__e"><view class="feedback-modal data-v-14bc1b43" catchtap="true"><view class="modal-header data-v-14bc1b43"><text class="modal-title data-v-14bc1b43">意见反馈</text><text data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="modal-close data-v-14bc1b43" bindtap="__e">×</text></view><view class="modal-content data-v-14bc1b43"><textarea class="feedback-textarea data-v-14bc1b43" placeholder="请描述您遇到的问题或建议..." maxlength="500" data-event-opts="{{[['input',[['__set_model',['','feedbackContent','$event',[]]]]]]}}" value="{{feedbackContent}}" bindinput="__e"></textarea><view class="char-count data-v-14bc1b43">{{$root.g0+"/500"}}</view></view><view class="modal-footer data-v-14bc1b43"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="modal-btn cancel-btn data-v-14bc1b43" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['submitFeedback',['$event']]]]]}}" class="modal-btn submit-btn data-v-14bc1b43" bindtap="__e">提交</view></view></view></view></block><custom-tab-bar vue-id="fa7063ba-1" class="data-v-14bc1b43" bind:__l="__l"></custom-tab-bar></view>