{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/bank/list.vue?67bd", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/bank/list.vue?971a", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/bank/list.vue?b2aa", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/bank/list.vue?fa98", "uni-app:///pages/bank/list.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/bank/list.vue?238e", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/bank/list.vue?94cf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bankList", "searchKeyword", "currentPage", "totalCount", "hasMore", "loading", "showMap", "onLoad", "console", "onPullDownRefresh", "then", "uni", "catch", "onReachBottom", "methods", "loadBanks", "isRefresh", "params", "page", "per_page", "api", "res", "newBanks", "banksWithCoords", "bank", "latitude", "longitude", "loadMockData", "id", "name", "contact_person", "phone", "address", "searchBanks", "refreshBanks", "random", "title", "icon", "Promise", "loadMore", "viewBankDetail", "content", "showCancel", "confirmText", "callBank", "phoneNumber", "fail", "showLocation", "showMapView", "closeMapView", "openBankLocation", "consultBank", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAA+2B,CAAgB,g0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsKn4B;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;MACA;QACAC;MACA;IACA;MACAA;IACA;EACA;EAEAC;IACA,oBACAC;MACAC;IACA,GACAC;MACAJ;MACAG;IACA;EACA;EAEAE;IACA;MACA;IACA;EACA;EAEAC;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGAC;kBACAC;kBACAC;gBACA;gBAEA;kBACAF;gBACA;gBAAA;gBAAA,OAEAG;cAAA;gBAAAC;gBACAC,uGAEA;gBACAC;kBAAA,uCACAC;oBACAC;oBACAC;kBAAA;gBAAA,CACA;gBAEA;kBACA;kBACA;gBACA;kBACA;gBACA;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAlB;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACAG;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgB;MAAA;MACA,iBACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAP;QACAC;MACA,GACA;QACAE;QACAC;QACAC;QACAC;QACAC;QACAP;QACAC;MACA,GACA;QACAE;QACAC;QACAC;QACAC;QACAC;QACAP;QACAC;MACA,EACA;MAEA;QACA;MACA;QACA;MACA;MAEA;MACA;IACA;IAEAO;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAd;kBAAAD;kBAAAgB;gBAAA;cAAA;gBAAAd;gBACAC,oMAEA;gBACA;kBAAA,uCACAE;oBACAC;oBACAC;kBAAA;gBAAA,CACA;gBAEA;gBAEAf;kBACAyB;kBACAC;gBACA;gBAAA,kCAEAC;cAAA;gBAAA;gBAAA;gBAEA9B;gBACA;gBACA;gBACAG;kBACAyB;kBACAC;gBACA;gBAAA,kCAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;IACA;IAEAC;MACA7B;QACAyB;QACAK;QACAC;QACAC;MACA;IACA;IAEAC;MACAjC;QACAkC;QACAC;UACAtC;UACAG;YACAyB;YACAC;UACA;QACA;MACA;IACA;IAEAU;MACA;QACApC;UACAyB;UACAC;QACA;QACA;MACA;MAEA1B;QACAc;QACAC;QACAG;QACAG;QACAc;UACAtC;UACAG;YACAyB;YACAC;UACA;QACA;MACA;IACA;IAEAW;MACA;QACArC;UACAyB;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEAY;MACA;IACA;IAEAC;MACA;QACAvC;UACAyB;UACAC;QACA;QACA;MACA;MAEA1B;QACAc;QACAC;QACAG;QACAG;QACAc;UACAtC;UACAG;YACAyB;YACAC;UACA;QACA;MACA;IACA;IAEAc;MACA;MACAxC;QACAyC;MACA;;MAEA;MACA;QACAzC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrbA;AAAA;AAAA;AAAA;AAA0oD,CAAgB,++CAAG,EAAC,C;;;;;;;;;;;ACA9pD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/bank/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/bank/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=6fa623e8&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=6fa623e8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6fa623e8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/bank/list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=6fa623e8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.bankList.length\n  var g1 = _vm.hasMore && _vm.bankList.length > 0\n  var g2 = _vm.bankList.length === 0 && !_vm.loading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"bank-list\">\r\n    <!-- 搜索头部 -->\r\n    <view class=\"search-header\">\r\n      <view class=\"search-box\">\r\n        <input \r\n          v-model=\"searchKeyword\" \r\n          placeholder=\"搜索银行名称\"\r\n          class=\"search-input\"\r\n          @confirm=\"searchBanks\"\r\n        />\r\n        <view @click=\"searchBanks\" class=\"search-btn\">\r\n          <image src=\"/static/icons/search.svg\" class=\"search-icon\" mode=\"aspectFit\"></image>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 功能按钮 -->\r\n    <view class=\"function-section\">\r\n      <view @click=\"refreshBanks\" class=\"function-btn\">\r\n        <text class=\"btn-icon\">🔄</text>\r\n        <text class=\"btn-text\">换一换</text>\r\n      </view>\r\n      <view @click=\"showMapView\" class=\"function-btn\">\r\n        <text class=\"btn-icon\">🗺️</text>\r\n        <text class=\"btn-text\">地图查看</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 银行列表 -->\r\n    <view class=\"banks-section\">\r\n      <!-- 结果统计 -->\r\n      <view class=\"result-summary\" v-if=\"bankList.length > 0\">\r\n        <text class=\"summary-icon\">🏦</text>\r\n        <text class=\"summary-text\">银行机构</text>\r\n        <text class=\"summary-count\">({{ totalCount }}家)</text>\r\n      </view>\r\n      \r\n      <view class=\"bank-list-container\">\r\n        <view \r\n          v-for=\"bank in bankList\" \r\n          :key=\"bank.id\"\r\n          class=\"bank-item\"\r\n          @click=\"viewBankDetail(bank)\"\r\n        >\r\n          <!-- 银行基本信息 -->\r\n          <view class=\"bank-main\">\r\n            <view class=\"bank-icon-wrapper\">\r\n              <image \r\n                v-if=\"bank.icon\" \r\n                :src=\"bank.icon\" \r\n                class=\"bank-icon\"\r\n                mode=\"aspectFit\"\r\n              />\r\n              <view v-else class=\"bank-icon-placeholder\">\r\n                <text>🏦</text>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"bank-info\">\r\n              <view class=\"bank-name\">{{ bank.name }}</view>\r\n              <view class=\"bank-contact\">\r\n                <text class=\"contact-label\">联系人：</text>\r\n                <text class=\"contact-name\">{{ bank.contact_person }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 联系方式 -->\r\n          <view class=\"bank-contact-info\">\r\n            <view class=\"contact-item\">\r\n              <text class=\"contact-icon\">📞</text>\r\n              <text class=\"contact-text\">{{ bank.phone }}</text>\r\n              <view @tap=\"callBank(bank.phone)\" class=\"call-btn\" catchtap=\"true\">\r\n                <text>拨打</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"contact-item\">\r\n              <text class=\"contact-icon\">📍</text>\r\n              <text class=\"contact-text\">{{ bank.address }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 操作按钮 -->\r\n          <view class=\"bank-actions\">\r\n            <view @tap=\"showLocation(bank)\" class=\"action-btn location-btn\" catchtap=\"true\">\r\n              <text class=\"action-icon\">📍</text>\r\n              <text class=\"action-text\">位置</text>\r\n            </view>\r\n            <view @tap=\"consultBank(bank)\" class=\"action-btn consult-btn\" catchtap=\"true\">\r\n              <text class=\"action-icon\">💬</text>\r\n              <text class=\"action-text\">咨询</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 加载更多 -->\r\n      <view class=\"load-more-section\" v-if=\"hasMore && bankList.length > 0\">\r\n        <view class=\"load-more\" v-if=\"!loading\" @click=\"loadMore\">\r\n          <text class=\"load-more-text\">加载更多银行</text>\r\n          <text class=\"load-more-icon\">↓</text>\r\n        </view>\r\n        \r\n        <view class=\"loading-more\" v-if=\"loading\">\r\n          <text class=\"loading-more-text\">正在加载...</text>\r\n          <text class=\"loading-more-icon\">⏳</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 无数据提示 -->\r\n      <view v-if=\"bankList.length === 0 && !loading\" class=\"empty-state\">\r\n        <text class=\"empty-icon\">🏦</text>\r\n        <text class=\"empty-text\">暂无银行机构信息</text>\r\n        <text class=\"empty-tip\">请尝试刷新或使用其他关键词搜索</text>\r\n        <view @click=\"refreshBanks\" class=\"refresh-btn\">\r\n          <text>刷新重试</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 地图视图模态框 -->\r\n    <view v-if=\"showMap\" class=\"map-modal\" @click=\"closeMapView\">\r\n              <view class=\"map-container\" catchtap=\"true\">\r\n        <view class=\"map-header\">\r\n          <text class=\"map-title\">银行位置分布</text>\r\n          <view @click=\"closeMapView\" class=\"close-btn\">\r\n            <text>✕</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 银行位置列表 -->\r\n        <scroll-view class=\"location-list\" scroll-y>\r\n          <view \r\n            v-for=\"bank in bankList\" \r\n            :key=\"bank.id\"\r\n            class=\"location-item\"\r\n            @click=\"openBankLocation(bank)\"\r\n          >\r\n            <view class=\"location-info\">\r\n              <view class=\"bank-name-loc\">{{ bank.name }}</view>\r\n              <view class=\"bank-address\">{{ bank.address }}</view>\r\n              <view class=\"location-meta\">\r\n                <text class=\"distance\">📍 点击打开地图导航</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"location-actions\">\r\n              <view @tap=\"callBank(bank.phone)\" class=\"action-call\" catchtap=\"true\">\r\n                <text class=\"action-icon\">📞</text>\r\n              </view>\r\n              <view @tap=\"openBankLocation(bank)\" class=\"action-nav\" catchtap=\"true\">\r\n                <text class=\"action-icon\">🧭</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n        \r\n        <view class=\"map-footer\">\r\n          <text class=\"map-tip\">点击银行信息打开系统地图导航</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/utils/api.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      bankList: [],\r\n      searchKeyword: '',\r\n      currentPage: 1,\r\n      totalCount: 0,\r\n      hasMore: true,\r\n      loading: false,\r\n      showMap: false\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    try {\r\n      this.loadBanks().catch(err => {\r\n        console.error('银行列表加载失败:', err)\r\n      })\r\n    } catch (err) {\r\n      console.error('onLoad错误:', err)\r\n    }\r\n  },\r\n\r\n  onPullDownRefresh() {\r\n    this.refreshBanks()\r\n      .then(() => {\r\n        uni.stopPullDownRefresh()\r\n      })\r\n      .catch(err => {\r\n        console.error('下拉刷新失败:', err)\r\n        uni.stopPullDownRefresh()\r\n      })\r\n  },\r\n\r\n  onReachBottom() {\r\n    if (this.hasMore && !this.loading) {\r\n      this.loadMore()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    async loadBanks(isRefresh = false) {\r\n      if (this.loading) return\r\n      \r\n      this.loading = true\r\n      \r\n      try {\r\n        const params = {\r\n          page: isRefresh ? 1 : this.currentPage,\r\n          per_page: 10\r\n        }\r\n        \r\n        if (this.searchKeyword) {\r\n          params.name = this.searchKeyword\r\n        }\r\n        \r\n        const res = await api.getBanks(params)\r\n        const newBanks = res.data?.items || []\r\n        \r\n        // 为每个银行添加默认坐标（如果没有的话）\r\n        const banksWithCoords = newBanks.map((bank, index) => ({\r\n          ...bank,\r\n          latitude: bank.latitude || (29.5647 + (Math.random() - 0.5) * 0.1),\r\n          longitude: bank.longitude || (106.5507 + (Math.random() - 0.5) * 0.1)\r\n        }))\r\n        \r\n        if (isRefresh) {\r\n          this.bankList = banksWithCoords\r\n          this.currentPage = 1\r\n        } else {\r\n          this.bankList = [...this.bankList, ...banksWithCoords]\r\n        }\r\n        \r\n        this.totalCount = res.data?.total || 0\r\n        this.hasMore = res.data?.page < res.data?.pages\r\n        this.currentPage = res.data?.page + 1\r\n        \r\n      } catch (error) {\r\n        console.error('加载银行列表失败:', error)\r\n        // 使用模拟数据\r\n        this.loadMockData(isRefresh)\r\n      } finally {\r\n        this.loading = false\r\n        uni.stopPullDownRefresh()\r\n      }\r\n    },\r\n\r\n    loadMockData(isRefresh = false) {\r\n      const mockBanks = [\r\n        {\r\n          id: 1,\r\n          name: '中国银行重庆分行',\r\n          contact_person: '张经理',\r\n          phone: '023-********',\r\n          address: '重庆市渝中区解放碑步行街123号',\r\n          latitude: 29.5647,\r\n          longitude: 106.5507\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '建设银行重庆分行',\r\n          contact_person: '李经理',\r\n          phone: '023-********',\r\n          address: '重庆市江北区观音桥步行街456号',\r\n          latitude: 29.5734,\r\n          longitude: 106.5347\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '工商银行重庆分行',\r\n          contact_person: '王经理',\r\n          phone: '023-********',\r\n          address: '重庆市沙坪坝区三峡广场789号',\r\n          latitude: 29.5267,\r\n          longitude: 106.4564\r\n        }\r\n      ]\r\n      \r\n      if (isRefresh) {\r\n        this.bankList = mockBanks\r\n      } else {\r\n        this.bankList = [...this.bankList, ...mockBanks]\r\n      }\r\n      \r\n      this.totalCount = mockBanks.length\r\n      this.hasMore = false\r\n    },\r\n\r\n    searchBanks() {\r\n      this.currentPage = 1\r\n      this.hasMore = true\r\n      this.loadBanks(true)\r\n    },\r\n\r\n    async refreshBanks() {\r\n      try {\r\n        const res = await api.getBanks({ per_page: 10, random: true })\r\n        const newBanks = res.data?.data || res.data?.items || []\r\n        \r\n        // 添加坐标信息\r\n        this.bankList = newBanks.map(bank => ({\r\n          ...bank,\r\n          latitude: bank.latitude || (29.5647 + (Math.random() - 0.5) * 0.1),\r\n          longitude: bank.longitude || (106.5507 + (Math.random() - 0.5) * 0.1)\r\n        }))\r\n        \r\n        this.totalCount = res.data?.total || 0\r\n        \r\n        uni.showToast({\r\n          title: '已刷新',\r\n          icon: 'success'\r\n        })\r\n        \r\n        return Promise.resolve()\r\n      } catch (error) {\r\n        console.error('刷新银行列表失败:', error)\r\n        // 使用模拟数据刷新\r\n        this.loadMockData(true)\r\n        uni.showToast({\r\n          title: '已刷新',\r\n          icon: 'success'\r\n        })\r\n        \r\n        return Promise.resolve() // 即使出错也返回resolved，因为我们有fallback\r\n      }\r\n    },\r\n\r\n    loadMore() {\r\n      this.loadBanks()\r\n    },\r\n\r\n    viewBankDetail(bank) {\r\n      uni.showModal({\r\n        title: bank.name,\r\n        content: `联系人：${bank.contact_person}\\n电话：${bank.phone}\\n地址：${bank.address}`,\r\n        showCancel: false,\r\n        confirmText: '我知道了'\r\n      })\r\n    },\r\n\r\n    callBank(phone) {\r\n      uni.makePhoneCall({\r\n        phoneNumber: phone,\r\n        fail: (err) => {\r\n          console.error('拨打电话失败:', err)\r\n          uni.showToast({\r\n            title: '拨打失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    showLocation(bank) {\r\n      if (!bank.latitude || !bank.longitude) {\r\n        uni.showToast({\r\n          title: '该银行暂无位置信息',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n      \r\n      uni.openLocation({\r\n        latitude: parseFloat(bank.latitude),\r\n        longitude: parseFloat(bank.longitude),\r\n        name: bank.name,\r\n        address: bank.address,\r\n        fail: (err) => {\r\n          console.error('打开地图失败:', err)\r\n          uni.showToast({\r\n            title: '地图打开失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    showMapView() {\r\n      if (this.bankList.length === 0) {\r\n        uni.showToast({\r\n          title: '暂无银行数据',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n      \r\n      this.showMap = true\r\n    },\r\n\r\n    closeMapView() {\r\n      this.showMap = false\r\n    },\r\n\r\n    openBankLocation(bank) {\r\n      if (!bank.latitude || !bank.longitude) {\r\n        uni.showToast({\r\n          title: '该银行暂无位置信息',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n      \r\n      uni.openLocation({\r\n        latitude: parseFloat(bank.latitude),\r\n        longitude: parseFloat(bank.longitude),\r\n        name: bank.name,\r\n        address: bank.address,\r\n        fail: (err) => {\r\n          console.error('打开地图失败:', err)\r\n          uni.showToast({\r\n            title: '地图打开失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    consultBank(bank) {\r\n      // 跳转到咨询页面，并预填银行信息\r\n      uni.switchTab({\r\n        url: '/pages/consultation/index'\r\n      })\r\n      \r\n      // 通过事件总线传递银行信息\r\n      this.$nextTick(() => {\r\n        uni.$emit('selectBank', bank)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bank-list {\r\n  min-height: 100vh;\r\n  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);\r\n  position: relative;\r\n}\r\n\r\n/* 顶部重庆山城风格背景 */\r\n.bank-list::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 200rpx;\r\n  background: linear-gradient(180deg, \r\n    #1E90FF 0%, \r\n    #4A90E2 30%, \r\n    #6495ED 60%, \r\n    rgba(100, 149, 237, 0.6) 80%, \r\n    rgba(156, 196, 255, 0.3) 90%, \r\n    transparent 100%\r\n  );\r\n  z-index: 1;\r\n}\r\n\r\n/* 重庆山城剪影装饰 */\r\n.bank-list::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 140rpx;\r\n  left: 0;\r\n  right: 0;\r\n  height: 80rpx;\r\n  background: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E\") repeat-x;\r\n  background-size: 1200rpx 80rpx;\r\n  z-index: 2;\r\n  opacity: 0.7;\r\n}\r\n\r\n// 搜索头部样式\r\n.search-header {\r\n  background: transparent;\r\n  padding: 30rpx;\r\n  position: relative;\r\n  z-index: 3;\r\n  \r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: linear-gradient(135deg, rgba(30, 144, 255, 0.9) 0%, rgba(74, 144, 226, 0.9) 100%);\r\n    backdrop-filter: blur(10px);\r\n  }\r\n}\r\n\r\n.search-box {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 60rpx;\r\n  padding: 0 30rpx;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  border: none;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  \r\n  &::placeholder {\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.search-btn {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  backdrop-filter: blur(10px);\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n    background: rgba(255, 255, 255, 0.3);\r\n  }\r\n}\r\n\r\n.search-icon {\r\n  width: 28rpx;\r\n  height: 28rpx;\r\n  filter: invert(1);\r\n}\r\n\r\n// 功能按钮样式\r\n.function-section {\r\n  display: flex;\r\n  gap: 20rpx;\r\n  padding: 20rpx 30rpx;\r\n  background: white;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.function-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  padding: 16rpx 24rpx;\r\n  background: linear-gradient(135deg, #f8f8f8 0%, #fff 100%);\r\n  border: 1rpx solid #e0e0e0;\r\n  border-radius: 25rpx;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n    background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 100%);\r\n  }\r\n  \r\n  .btn-icon {\r\n    font-size: 28rpx;\r\n  }\r\n}\r\n\r\n// 银行列表样式\r\n.banks-section {\r\n  padding: 30rpx;\r\n}\r\n\r\n.result-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n  padding: 20rpx 30rpx;\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.summary-icon {\r\n  font-size: 40rpx;\r\n  margin-right: 15rpx;\r\n}\r\n\r\n.summary-text {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.summary-count {\r\n  font-size: 26rpx;\r\n  color: #1E90FF;\r\n  font-weight: normal;\r\n}\r\n\r\n.bank-list-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n}\r\n\r\n.bank-item {\r\n  background: white;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  border: 1rpx solid #f0f0f0;\r\n  \r\n  &:active {\r\n    transform: translateY(-4rpx);\r\n    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);\r\n  }\r\n}\r\n\r\n// 银行基本信息\r\n.bank-main {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n  margin-bottom: 24rpx;\r\n  padding-bottom: 20rpx;\r\n  border-bottom: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.bank-icon-wrapper {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.bank-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.bank-icon-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 40rpx;\r\n  color: white;\r\n}\r\n\r\n.bank-info {\r\n  flex: 1;\r\n}\r\n\r\n.bank-name {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  line-height: 1.2;\r\n}\r\n\r\n.bank-contact {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.contact-label {\r\n  margin-right: 5rpx;\r\n}\r\n\r\n.contact-name {\r\n  color: #1E90FF;\r\n  font-weight: 500;\r\n}\r\n\r\n// 联系方式\r\n.bank-contact-info {\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n  font-size: 28rpx;\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.contact-icon {\r\n  font-size: 28rpx;\r\n  margin-right: 12rpx;\r\n  width: 40rpx;\r\n  text-align: center;\r\n}\r\n\r\n.contact-text {\r\n  flex: 1;\r\n  color: #333;\r\n  margin-right: 20rpx;\r\n  line-height: 1.4;\r\n}\r\n\r\n.call-btn {\r\n  padding: 8rpx 20rpx;\r\n  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 500;\r\n  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);\r\n  }\r\n}\r\n\r\n// 操作按钮\r\n.bank-actions {\r\n  display: flex;\r\n  gap: 20rpx;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  padding: 16rpx 28rpx;\r\n  border: 2rpx solid #e0e0e0;\r\n  border-radius: 25rpx;\r\n  background: white;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  &.location-btn {\r\n    &:active {\r\n      transform: scale(0.95);\r\n      border-color: #ccc;\r\n      background: #f8f8f8;\r\n    }\r\n  }\r\n  \r\n  &.consult-btn {\r\n    background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);\r\n    color: white;\r\n    border-color: #1E90FF;\r\n    box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);\r\n    \r\n    &:active {\r\n      transform: scale(0.95);\r\n      box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);\r\n    }\r\n  }\r\n  \r\n  .action-icon {\r\n    font-size: 28rpx;\r\n  }\r\n}\r\n\r\n// 加载更多\r\n.load-more-section {\r\n  margin-top: 40rpx;\r\n  text-align: center;\r\n}\r\n\r\n.load-more {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 20rpx 40rpx;\r\n  background: linear-gradient(135deg, #f8f8f8 0%, #fff 100%);\r\n  color: #666;\r\n  border: 2rpx solid #e0e0e0;\r\n  border-radius: 30rpx;\r\n  font-size: 28rpx;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n    background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 100%);\r\n  }\r\n}\r\n\r\n.load-more-text {\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.load-more-icon {\r\n  font-size: 28rpx;\r\n  animation: bounce 2s infinite;\r\n}\r\n\r\n.loading-more {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 20rpx 40rpx;\r\n  background: #f8f8f8;\r\n  color: #999;\r\n  border-radius: 30rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.loading-more-text {\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.loading-more-icon {\r\n  font-size: 28rpx;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n// 空状态\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 100rpx 0;\r\n  text-align: center;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 120rpx;\r\n  margin-bottom: 30rpx;\r\n  opacity: 0.3;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 32rpx;\r\n  color: #666;\r\n  margin-bottom: 15rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.empty-tip {\r\n  font-size: 26rpx;\r\n  color: #999;\r\n  margin-bottom: 40rpx;\r\n  line-height: 1.4;\r\n}\r\n\r\n.refresh-btn {\r\n  padding: 20rpx 40rpx;\r\n  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 30rpx;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);\r\n  }\r\n}\r\n\r\n// 地图模态框\r\n.map-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(4px);\r\n}\r\n\r\n.map-container {\r\n  background: white;\r\n  border-radius: 20rpx;\r\n  width: 90%;\r\n  max-width: 800rpx;\r\n  height: 80%;\r\n  max-height: 800rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.map-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);\r\n}\r\n\r\n.map-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: white;\r\n}\r\n\r\n.close-btn {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  backdrop-filter: blur(10px);\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.9);\r\n    background: rgba(255, 255, 255, 0.3);\r\n  }\r\n}\r\n\r\n.location-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.location-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n  \r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.location-info {\r\n  flex: 1;\r\n}\r\n\r\n.bank-name-loc {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  line-height: 1.2;\r\n}\r\n\r\n.bank-address {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.4;\r\n}\r\n\r\n.location-meta {\r\n  font-size: 26rpx;\r\n  color: #999;\r\n  margin-top: 8rpx;\r\n}\r\n\r\n.location-actions {\r\n  display: flex;\r\n  gap: 20rpx;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-call {\r\n  padding: 8rpx 20rpx;\r\n  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 500;\r\n  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);\r\n  }\r\n}\r\n\r\n.action-nav {\r\n  padding: 8rpx 20rpx;\r\n  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 500;\r\n  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: scale(0.95);\r\n    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);\r\n  }\r\n}\r\n\r\n.map-footer {\r\n  padding: 20rpx 30rpx;\r\n  text-align: center;\r\n  background: #f8f8f8;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.map-tip {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n// 动画\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-8rpx);\r\n  }\r\n  60% {\r\n    transform: translateY(-4rpx);\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=6fa623e8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=6fa623e8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051457\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}