<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>创建tabBar图标</title>
    <style>
        .icon-container {
            display: inline-block;
            width: 60px;
            height: 60px;
            font-size: 40px;
            text-align: center;
            line-height: 60px;
            margin: 10px;
            border: 1px solid #ccc;
        }
        .active {
            background-color: #ffebee;
        }
    </style>
</head>
<body>
    <h2>tabBar图标预览</h2>
    
    <div>
        <h3>首页</h3>
        <div class="icon-container">🏠</div>
        <div class="icon-container active">🏡</div>
    </div>
    
    <div>
        <h3>政策</h3>
        <div class="icon-container">📋</div>
        <div class="icon-container active">📊</div>
    </div>
    
    <div>
        <h3>咨询</h3>
        <div class="icon-container">💬</div>
        <div class="icon-container active">💭</div>
    </div>
    
    <div>
        <h3>个人中心</h3>
        <div class="icon-container">👤</div>
        <div class="icon-container active">👨‍💼</div>
    </div>
    
    <script>
        // 这个文件可以用来预览图标效果
        // 实际项目中我们将直接使用emoji字符
    </script>
</body>
</html> 