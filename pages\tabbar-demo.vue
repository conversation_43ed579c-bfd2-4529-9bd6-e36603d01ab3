<template>
  <view class="tabbar-demo-page">
    <!-- 页面头部 -->
    <view class="header bg-primary text-white p-3 text-center">
      <text class="text-xl font-bold">自定义TabBar演示</text>
    </view>

    <!-- 演示内容 -->
    <view class="content p-3">
      <!-- 当前状态显示 -->
      <view class="status-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">当前状态</text>
        <view class="status-info">
          <view class="info-item flex-between mb-2">
            <text class="text-base text-dark">当前Tab索引:</text>
            <text class="text-base text-primary font-bold">{{ currentTabIndex }}</text>
          </view>
          <view class="info-item flex-between mb-2">
            <text class="text-base text-dark">当前Tab名称:</text>
            <text class="text-base text-primary font-bold">{{ currentTabName }}</text>
          </view>
          <view class="info-item flex-between">
            <text class="text-base text-dark">当前页面路径:</text>
            <text class="text-sm text-gray">{{ currentPagePath }}</text>
          </view>
        </view>
      </view>

      <!-- 功能演示 -->
      <view class="demo-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">功能演示</text>
        
        <!-- 手动切换Tab -->
        <view class="demo-section mb-3">
          <text class="text-base text-dark mb-2">手动切换Tab:</text>
          <view class="button-group flex-row justify-around">
            <button 
              v-for="(item, index) in tabList" 
              :key="index"
              class="demo-btn"
              :class="{ active: currentTabIndex === index }"
              @click="switchToTab(index)"
            >
              {{ item.text }}
            </button>
          </view>
        </view>

        <!-- TabBar信息 -->
        <view class="demo-section">
          <text class="text-base text-dark mb-2">TabBar配置信息:</text>
          <view class="config-list">
            <view 
              v-for="(item, index) in tabList" 
              :key="index"
              class="config-item p-2 mb-2 rounded"
              :class="{ 'bg-primary': currentTabIndex === index, 'bg-gray': currentTabIndex !== index }"
            >
              <view class="flex-between align-center">
                <view class="flex-row align-center">
                  <image 
                    class="config-icon mr-2" 
                    :src="currentTabIndex === index ? item.activeIcon : item.icon"
                    mode="aspectFit"
                  />
                  <text 
                    class="text-sm"
                    :class="{ 'text-white': currentTabIndex === index, 'text-dark': currentTabIndex !== index }"
                  >
                    {{ item.text }}
                  </text>
                </view>
                <text 
                  class="text-xs"
                  :class="{ 'text-white': currentTabIndex === index, 'text-gray': currentTabIndex !== index }"
                >
                  索引: {{ index }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 样式演示 -->
      <view class="style-demo bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">样式演示</text>
        
        <!-- 颜色演示 -->
        <view class="color-demo mb-3">
          <text class="text-base text-dark mb-2">使用的颜色变量:</text>
          <view class="color-list flex-row justify-around">
            <view class="color-item flex-col align-center">
              <view class="color-box bg-primary rounded mb-1"></view>
              <text class="text-xs text-gray">主色调</text>
            </view>
            <view class="color-item flex-col align-center">
              <view class="color-box bg-secondary rounded mb-1"></view>
              <text class="text-xs text-gray">次要色</text>
            </view>
            <view class="color-item flex-col align-center">
              <view class="color-box bg-gray rounded mb-1"></view>
              <text class="text-xs text-gray">灰色</text>
            </view>
            <view class="color-item flex-col align-center">
              <view class="color-box bg-white border rounded mb-1"></view>
              <text class="text-xs text-gray">白色</text>
            </view>
          </view>
        </view>

        <!-- 间距演示 -->
        <view class="spacing-demo">
          <text class="text-base text-dark mb-2">使用的间距变量:</text>
          <view class="spacing-list">
            <view class="spacing-item flex-between align-center mb-1">
              <text class="text-sm text-dark">$spacing-1</text>
              <view class="spacing-bar bg-primary" style="width: 10rpx; height: 20rpx;"></view>
              <text class="text-xs text-gray">10rpx</text>
            </view>
            <view class="spacing-item flex-between align-center mb-1">
              <text class="text-sm text-dark">$spacing-2</text>
              <view class="spacing-bar bg-primary" style="width: 20rpx; height: 20rpx;"></view>
              <text class="text-xs text-gray">20rpx</text>
            </view>
            <view class="spacing-item flex-between align-center">
              <text class="text-sm text-dark">$spacing-3</text>
              <view class="spacing-bar bg-primary" style="width: 30rpx; height: 20rpx;"></view>
              <text class="text-xs text-gray">30rpx</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="usage-card bg-white rounded-lg p-3">
        <text class="text-lg font-bold text-primary mb-2">使用说明</text>
        <view class="usage-list">
          <view class="usage-item mb-2">
            <text class="text-sm text-dark">• 使用SCSS公共样式系统，统一设计风格</text>
          </view>
          <view class="usage-item mb-2">
            <text class="text-sm text-dark">• 支持本地图标资源，提高加载速度</text>
          </view>
          <view class="usage-item mb-2">
            <text class="text-sm text-dark">• 响应式设计，适配不同屏幕尺寸</text>
          </view>
          <view class="usage-item mb-2">
            <text class="text-sm text-dark">• 安全区域适配，兼容各种设备</text>
          </view>
          <view class="usage-item">
            <text class="text-sm text-dark">• 完善的错误处理和用户反馈</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部占位，避免被tabbar遮挡 -->
    <view class="tabbar-placeholder"></view>
  </view>
</template>

<script>
export default {
  name: 'TabbarDemo',
  data() {
    return {
      currentTabIndex: 0,
      currentTabName: '首页',
      currentPagePath: '/pages/index/index',
      tabList: [
        {
          icon: '/static/icons/home.svg',
          activeIcon: '/static/icons/home-active.svg',
          text: '首页',
          pagePath: '/pages/index/index'
        },
        {
          icon: '/static/icons/policy.svg',
          activeIcon: '/static/icons/policy-active.svg',
          text: '政策',
          pagePath: '/pages/policy/index'
        },
        {
          icon: '/static/icons/consult.svg',
          activeIcon: '/static/icons/consult-active.svg',
          text: '咨询',
          pagePath: '/pages/consultation/index'
        },
        {
          icon: '/static/icons/profile.svg',
          activeIcon: '/static/icons/profile-active.svg',
          text: '个人中心',
          pagePath: '/pages/profile/index'
        }
      ]
    }
  },
  
  methods: {
    // 切换到指定Tab
    switchToTab(index) {
      if (index >= 0 && index < this.tabList.length) {
        this.currentTabIndex = index;
        this.currentTabName = this.tabList[index].text;
        this.currentPagePath = this.tabList[index].pagePath;
        
        // 这里可以调用实际的tabbar组件方法
        // this.$refs.tabbar?.updateSelected(index);
        
        uni.showToast({
          title: `切换到${this.tabList[index].text}`,
          icon: 'none',
          duration: 1500
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.tabbar-demo-page {
  min-height: 100vh;
  background-color: $gray-light;
}

.content {
  padding-bottom: 200rpx; // 为tabbar留出空间
}

.status-card,
.demo-card,
.style-demo,
.usage-card {
  @include card-shadow('md');
}

.demo-btn {
  @include button-style($gray-light, $text-color, 'small');
  min-width: 120rpx;
  
  &.active {
    @include button-style($primary-color, white, 'small');
  }
}

.config-icon {
  width: 32rpx;
  height: 32rpx;
}

.color-box {
  width: 60rpx;
  height: 40rpx;
}

.spacing-bar {
  border-radius: 2rpx;
}

.tabbar-placeholder {
  height: 200rpx; // 为tabbar留出空间
}

// 响应式适配
@include respond-to('md') {
  .button-group {
    justify-content: space-between;
  }
  
  .color-list {
    justify-content: space-between;
  }
}
</style>
