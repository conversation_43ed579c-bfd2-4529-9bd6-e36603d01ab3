<template>
  <view class="index-page">
    <!-- 顶部搜索栏 -->
    <view class="search-header">
      <view class="search-box" @click="toSearch">
        <uni-icons type="search" color="#999" size="18"></uni-icons>
        <text class="search-placeholder">🏔️ 搜索山城跨境政策、咨询...</text>
      </view>
    </view>

    <!-- 轮播图 -->
    <view class="banner-section">
      <swiper class="banner-swiper" autoplay interval="3000" duration="500" circular indicator-dots indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#1E90FF">
        <swiper-item v-for="(banner, index) in bannerList" :key="banner.id || index" @click="onBannerClick(banner)">
          <image 
            :src="banner.image" 
            class="banner-image" 
            mode="aspectFill"
            @error="onBannerImageError(banner, index)"
            @load="onBannerImageLoad(banner)"
          ></image>
          <view class="banner-overlay">
            <text class="banner-title">{{ banner.title }}</text>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section section">
      <view class="menu-grid">
        <view class="menu-item" @click="toPolicyList">
          <view class="menu-icon">
            <text class="icon-font" style="color: #1E90FF; font-size: 48rpx;">📋</text>
          </view>
          <text class="menu-text">政策文件</text>
        </view>
        <view class="menu-item" @click="toFAQList">
          <view class="menu-icon">
            <text class="icon-font" style="color: #1E90FF; font-size: 48rpx;">🤔</text>
          </view>
          <text class="menu-text">热门问答</text>
        </view>
        <view class="menu-item" @click="toPage('/pages/consultation/index')">
          <view class="menu-icon">
            <text class="icon-font" style="color: #1E90FF; font-size: 48rpx;">💬</text>
          </view>
          <text class="menu-text">业务咨询</text>
        </view>
        <view class="menu-item" @click="toInterpretationList">
          <view class="menu-icon">
            <text class="icon-font" style="color: #1E90FF; font-size: 48rpx;">📺</text>
          </view>
          <text class="menu-text">政策解读</text>
        </view>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-section">
      <view class="stats-header">
        <text class="stats-title">🏙️ 服务数据统计</text>
        <text class="stats-time">截至：{{ getCurrentTime() }}</text>
      </view>
      <view class="stats-cards">
        <view class="stats-card">
          <view class="stats-icon">📋</view>
          <view class="stats-info">
            <text class="stats-number">{{ statistics.policyCount || 0 }}</text>
            <text class="stats-unit">份</text>
          </view>
          <text class="stats-label">已收录政策文件</text>
        </view>
        
        <view class="stats-card">
          <view class="stats-icon">💬</view>
          <view class="stats-info">
            <text class="stats-number">{{ statistics.consultCount || 0 }}</text>
            <text class="stats-unit">次</text>
          </view>
          <text class="stats-label">已解答业务咨询</text>
        </view>
        
        <view class="stats-card">
          <view class="stats-icon">👀</view>
          <view class="stats-info">
            <text class="stats-number">{{ statistics.visitCount || 0 }}</text>
            <text class="stats-unit">次</text>
          </view>
          <text class="stats-label">累计访问量</text>
        </view>

        <view class="stats-card">
          <view class="stats-icon">🏦</view>
          <view class="stats-info">
            <text class="stats-number">{{ statistics.bankCount || 0 }}</text>
            <text class="stats-unit">个</text>
          </view>
          <text class="stats-label">在线机构数量</text>
        </view>
      </view>
    </view>

    <!-- 银行机构 -->
    <view class="bank-section section">
      <view class="section-header">
        <view class="section-title-wrap">
          <text class="section-icon">🏦金融机构</text>
          <text class="section-subtitle">服务重庆跨境融资发展</text>
        </view>
        <view class="more-actions">
          <text class="action-btn refresh-btn" @click="refreshBanks">🔄</text>
          <text class="action-btn more-btn" @click="toBankListPage">更多</text>
        </view>
      </view>
      <view class="bank-grid">
        <view v-if="bankList && bankList.length > 0" class="bank-card" v-for="bank in bankList.slice(0, 3)" :key="bank.id" @click="viewBankDetail(bank)">
          <view class="bank-card-header">
            <view class="bank-logo">
              <image 
                v-if="getBankIcon(bank)" 
                :src="getBankIcon(bank)" 
                class="bank-logo-image"
                mode="aspectFill"
                @error="onBankIconError(bank)"
              />
              <view 
                v-else 
                class="bank-logo-placeholder"
              >
                🏦
              </view>
            </view>
            <view class="bank-basic-info">
              <text class="bank-name">{{ bank.name || '银行名称' }}</text>
              <view class="bank-contact-row">
                <text class="bank-contact">{{ bank.contact_person || '联系人' }}</text>
                <text class="bank-phone" @click.stop="callPhone(bank.phone)">{{ bank.phone || '电话号码' }}</text>
              </view>
            </view>
            <view class="bank-actions">
              <view class="bank-location-btn" @click.stop="openMap(bank)">
                <text class="location-icon">📍</text>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="bank-empty">
          <text class="empty-icon">🏦</text>
          <text class="empty-text">正在加载银行信息...</text>
        </view>
      </view>
    </view>

    <!-- 最新新闻 -->
    <view class="news-section section">
      <view class="section-header">
        <view class="section-title-wrap">
          <text class="section-icon">🌉最新资讯</text>
          <text class="section-subtitle">山城跨境金融最新动态</text>
        </view>
        <text class="action-btn more-btn" @click="toNewsList">更多</text>
      </view>
      <view class="news-list">
        <view class="news-item" v-for="news in newsList.slice(0, 3)" :key="news.id" @click="toNewsDetail(news.id)">
          <view class="news-cover">
            <image 
              v-if="getNewsCover(news)" 
              :src="getNewsCover(news)" 
              class="news-cover-image"
              mode="aspectFill"
              @error="onNewsCoverError(news)"
            />
            <view 
              v-else 
              class="news-cover-placeholder"
              :class="'news-cover-' + getNewsCategoryType(news.category)"
            >
              {{ getNewsCategoryIcon(news.category) }}
            </view>
          </view>
          <view class="news-content">
            <text class="news-title">{{ news.title }}</text>
            <view class="news-meta">
              <text class="news-category">{{ news.category }}</text>
              <text class="news-date">{{ formatDate(news.publish_date) }}</text>
              <text class="news-views">👁 {{ formatViewCount(news.view_count) }}</text>
            </view>
          </view>
          <text class="news-arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 热门提问 -->
    <view class="faq-section section">
      <view class="section-header">
        <view class="section-title-wrap">
          <text class="section-icon">🤔热门问答</text>
          <text class="section-subtitle">山城企业常见问题解答</text>
        </view>
        <text class="action-btn more-btn" @click="toFAQList">更多</text>
      </view>
      <view class="faq-list">
        <view class="faq-item" v-for="faq in faqList.slice(0, 4)" :key="faq.id" @click="toFAQDetail(faq.id)">
          <view class="faq-icon">❓</view>
          <view class="faq-content">
            <text class="faq-question">{{ faq.question }}</text>
            <view class="faq-stats">
              <text class="faq-stat">👁 {{ faq.view_count || 0 }}</text>
              <text class="faq-stat">👍 {{ faq.like_count || 0 }}</text>
            </view>
          </view>
          <text class="faq-arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 政策解读 -->
    <view class="interpretation-section section">
      <view class="section-header">
        <view class="section-title-wrap">
          <text class="section-icon">📺政策解读</text>
          <text class="section-subtitle">重庆跨境政策专业解读</text>
        </view>
        <text class="action-btn more-btn" @click="toInterpretationList">更多</text>
      </view>
      <view class="interpretation-grid">
        <view class="interpretation-item" v-for="(item, index) in interpretationList.slice(0, 6)" :key="item.id" @click="toInterpretationDetail(item.id)">
          <view class="interpretation-cover">
            <image 
              v-if="getVideoThumbnail(item, index)" 
              :src="getVideoThumbnail(item, index)" 
              class="interpretation-image"
              mode="aspectFill"
              @error="onVideoThumbnailError(item, index)"
            />
            <view 
              v-else 
              class="interpretation-image"
              :class="'interpretation-bg-' + (index % 6 + 1)"
            ></view>
            <view v-if="item.video_url" class="play-icon">
              <text>▶</text>
            </view>
            <view v-else class="text-icon">
              <text>📄</text>
            </view>
          </view>
          <text class="interpretation-item-title">{{ item.title || `课程${index + 1}：利用...` }}</text>
        </view>
      </view>
    </view>
    
    <!-- 自定义TabBar -->
    <custom-tab-bar />
  </view>
</template>

<script>
import { api } from '@/utils/api'
import CustomTabBar from '@/custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      bannerList: [],
      statistics: {
        policyCount: 0,
        visitCount: 0,
        consultCount: 0
      },
      bankList: [],
      newsList: [],
      faqList: [],
      interpretationList: []
    }
  },
  onLoad() {
    try {
      this.loadPageData().catch(err => {
        console.error('页面数据加载失败:', err)
      })
    } catch (err) {
      console.error('onLoad错误:', err)
    }
  },
  onPullDownRefresh() {
    this.loadPageData()
      .then(() => {
        uni.stopPullDownRefresh()
      })
      .catch(err => {
        console.error('下拉刷新失败:', err)
        uni.stopPullDownRefresh()
      })
  },
  onShow() {
    if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {
      this.$root.$mp.page.getTabBar().$vm.updateSelected(0)
    }
    // 记录页面访问
    this.recordPageView()
  },
  methods: {
    async loadPageData() {
      try {
        uni.showLoading({ title: '加载中...' })
        
        // 并行加载数据
        await Promise.all([
          this.loadBanners(),
          this.loadStatistics(),
          this.loadBanks(),
          this.loadNews(),
          this.loadFAQ(),
          this.loadInterpretations()
        ])
        
        uni.hideLoading()
      } catch (error) {
        uni.hideLoading()
        console.error('数据加载失败:', error)
      }
    },

    async loadBanners() {
      try {
        const res = await api.getBanners()
        console.log('轮播图API返回:', res)
        
        // 根据实际API返回，轮播图数据直接在res.data中（数组格式）
        const banners = res.data || []
        console.log('原始轮播图数据:', banners)
        
        // 处理轮播图数据，映射字段名
        this.bannerList = banners
          .filter(banner => banner.is_active) // 只显示启用的轮播图
          .sort((a, b) => a.sort - b.sort) // 按sort字段排序
          .map((banner, index) => {
            // 处理图片URL
            let imageUrl = banner.image
            
            // 如果是本地API文件URL，确保可以正常访问
            if (imageUrl && imageUrl.includes('localhost:5000')) {
              // 如果图片URL看起来正常，保持原样
              console.log(`轮播图${index + 1}图片URL:`, imageUrl)
            } else if (imageUrl && imageUrl.startsWith('https://example.com')) {
              // 替换示例URL为默认图片
              imageUrl = `/static/images/banner/cq-${index % 3 === 0 ? 'sanxia' : index % 3 === 1 ? 'hongyadong' : 'jiefangbei'}.jpg`
              console.log(`轮播图${index + 1}使用默认图片:`, imageUrl)
            }
            
            return {
              id: banner.id,
              title: banner.title,
              image: imageUrl,
              link_url: banner.link === '#' ? '' : banner.link, // 处理无效链接
              sort_order: banner.sort,
              is_active: banner.is_active,
              created_at: banner.created_at
            }
          })
        
        console.log('处理后的轮播图数据:', this.bannerList)
        
        // 如果后端没有轮播图数据或数据为空，使用默认图片
        if (this.bannerList.length === 0) {
          console.log('使用默认轮播图')
          this.bannerList = [
            {
              id: 1,
              title: '重庆长江三峡',
              image: '/static/images/banner/cq-sanxia.jpg',
              link_url: '',
              sort_order: 1,
              is_active: true
            },
            {
              id: 2,
              title: '重庆洪崖洞夜景',
              image: '/static/images/banner/cq-hongyadong.jpg',
              link_url: '',
              sort_order: 2,
              is_active: true
            },
            {
              id: 3,
              title: '重庆解放碑',
              image: '/static/images/banner/cq-jiefangbei.jpg',
              link_url: '',
              sort_order: 3,
              is_active: true
            }
          ]
        }
      } catch (error) {
        console.error('轮播图数据加载失败:', error)
        
        // API失败时使用默认轮播图
        this.bannerList = [
          {
            id: 1,
            title: '重庆长江三峡',
            image: '/static/images/banner/cq-sanxia.jpg',
            link_url: '',
            sort_order: 1,
            is_active: true
          },
          {
            id: 2,
            title: '重庆洪崖洞夜景',
            image: '/static/images/banner/cq-hongyadong.jpg',
            link_url: '',
            sort_order: 2,
            is_active: true
          },
          {
            id: 3,
            title: '重庆解放碑',
            image: '/static/images/banner/cq-jiefangbei.jpg',
            link_url: '',
            sort_order: 3,
            is_active: true
          }
        ]
      }
    },

    async loadStatistics() {
      try {
        const res = await api.getStatistics()
        console.log('统计数据API返回:', res)
        
        // 处理统计数据，确保字段映射正确
        if (res.data) {
          this.statistics = {
            policyCount: res.data.policyCount || res.data.policy_count || 0,
            bankCount: res.data.bankCount || res.data.bank_count || 0,
            consultCount: res.data.consultCount || res.data.consult_count || 0,
            visitCount: res.data.visitCount || res.data.visit_count || 0
          }
        }
        console.log('处理后的统计数据:', this.statistics)
      } catch (error) {
        console.error('统计数据加载失败:', error)
        // API失败时使用默认数据
        this.statistics = {
          policyCount: 7,
          bankCount: 6,
          consultCount: 9,
          visitCount: 22
        }
      }
    },

    async loadBanks() {
      try {
        const res = await api.getBanks({ per_page: 3 })
        console.log('银行API返回:', res)
        
        // 尝试不同的数据结构
        let banks = []
        if (res.data?.items) {
          banks = res.data.items
        } else if (res.data?.data) {
          banks = res.data.data
        } else if (Array.isArray(res.data)) {
          banks = res.data
        }
        
        this.bankList = banks || []
        console.log('处理后的银行列表:', this.bankList)
        
        // 如果没有获取到数据，使用模拟数据
        if (this.bankList.length === 0) {
          console.log('使用银行模拟数据')
          this.bankList = [
            {
              id: 1,
              name: '中国银行重庆分行',
              contact_person: '张经理',
              phone: '023-********',
              address: '重庆市渝中区解放碑步行街123号',
              latitude: 29.559434,
              longitude: 106.577011,
              icon: 'https://example.com/bank1.png',
              created_at: '2025-06-22T09:29:49'
            },
            {
              id: 2,
              name: '建设银行重庆分行',
              contact_person: '李经理',
              phone: '023-********',
              address: '重庆市江北区观音桥步行街456号',
              latitude: 29.574639,
              longitude: 106.539285,
              icon: 'https://example.com/bank2.png',
              created_at: '2025-06-22T09:29:49'
            },
            {
              id: 3,
              name: '工商银行重庆分行',
              contact_person: '王经理',
              phone: '023-********',
              address: '重庆市沙坪坝区三峡广场789号',
              latitude: 29.544606,
              longitude: 106.456878,
              icon: null,
              created_at: '2025-06-22T09:29:49'
            }
          ]
        }
      } catch (error) {
        console.error('银行数据加载失败:', error)
        // API失败时使用模拟数据
        this.bankList = [
          {
            id: 1,
            name: '中国银行重庆分行',
            contact_person: '张经理',
            phone: '023-********',
            address: '重庆市渝中区解放碑步行街123号',
            latitude: 29.559434,
            longitude: 106.577011,
            icon: 'https://example.com/bank1.png',
            created_at: '2025-06-22T09:29:49'
          },
          {
            id: 2,
            name: '建设银行重庆分行',
            contact_person: '李经理',
            phone: '023-********',
            address: '重庆市江北区观音桥步行街456号',
            latitude: 29.574639,
            longitude: 106.539285,
            icon: 'https://example.com/bank2.png',
            created_at: '2025-06-22T09:29:49'
          },
          {
            id: 3,
            name: '工商银行重庆分行',
            contact_person: '王经理',
            phone: '023-********',
            address: '重庆市沙坪坝区三峡广场789号',
            latitude: 29.544606,
            longitude: 106.456878,
            icon: null,
            created_at: '2025-06-22T09:29:49'
          }
        ]
      }
    },

    async loadNews() {
      try {
        const res = await api.getNews({ per_page: 3 })
        console.log('新闻API返回:', res)
        
        // 尝试不同的数据结构
        let news = []
        if (res.data?.items) {
          news = res.data.items
        } else if (res.data?.data) {
          news = res.data.data
        } else if (Array.isArray(res.data)) {
          news = res.data
        }
        
        this.newsList = news || []
        console.log('处理后的新闻列表:', this.newsList)
        
        // 如果没有数据，使用模拟数据
        if (this.newsList.length === 0) {
          console.log('使用新闻模拟数据')
          this.newsList = [
            {
              id: 1,
              title: '重庆自贸区跨境融资便利化措施正式发布',
              content: '<p>重庆自贸区管委会今日正式发布跨境融资便利化措施，新措施将有效降低企业融资成本，提升跨境融资效率...</p>',
              category: '政策发布',
              cover_img: 'https://example.com/news1.jpg',
              view_count: 1256,
              like_count: 89,
              collect_count: 23,
              forward_count: 12,
              publish_date: '2024-03-15T09:30:00',
              created_at: '2024-03-15T09:25:00'
            },
            {
              id: 2,
              title: '外汇局重庆分局召开跨境融资政策解读会',
              content: '<p>为帮助企业更好理解和运用跨境融资政策，外汇局重庆分局召开专题解读会，详细解读了最新的跨境融资监管政策和操作流程...</p>',
              category: '会议活动',
              cover_img: 'https://example.com/news2.jpg',
              view_count: 892,
              like_count: 67,
              collect_count: 15,
              forward_count: 8,
              publish_date: '2024-03-14T14:20:00',
              created_at: '2024-03-14T14:15:00'
            },
            {
              id: 3,
              title: '重庆企业跨境融资规模创历史新高',
              content: '<p>据重庆市金融办统计数据显示，2024年第一季度，重庆企业跨境融资总规模达到新高度，同比增长35%...</p>',
              category: '市场动态',
              cover_img: 'https://example.com/news3.jpg',
              view_count: 2134,
              like_count: 156,
              collect_count: 43,
              forward_count: 25,
              publish_date: '2024-03-13T16:45:00',
              created_at: '2024-03-13T16:40:00'
            }
          ]
        }
      } catch (error) {
        console.error('新闻数据加载失败:', error)
        // API失败时使用模拟数据
        this.newsList = [
          {
            id: 1,
            title: '重庆自贸区跨境融资便利化措施正式发布',
            content: '<p>重庆自贸区管委会今日正式发布跨境融资便利化措施，新措施将有效降低企业融资成本，提升跨境融资效率...</p>',
            category: '政策发布',
            cover_img: 'https://example.com/news1.jpg',
            view_count: 1256,
            like_count: 89,
            collect_count: 23,
            forward_count: 12,
            publish_date: '2024-03-15T09:30:00',
            created_at: '2024-03-15T09:25:00'
          },
          {
            id: 2,
            title: '外汇局重庆分局召开跨境融资政策解读会',
            content: '<p>为帮助企业更好理解和运用跨境融资政策，外汇局重庆分局召开专题解读会，详细解读了最新的跨境融资监管政策和操作流程...</p>',
            category: '会议活动',
            cover_img: 'https://example.com/news2.jpg',
            view_count: 892,
            like_count: 67,
            collect_count: 15,
            forward_count: 8,
            publish_date: '2024-03-14T14:20:00',
            created_at: '2024-03-14T14:15:00'
          },
          {
            id: 3,
            title: '重庆企业跨境融资规模创历史新高',
            content: '<p>据重庆市金融办统计数据显示，2024年第一季度，重庆企业跨境融资总规模达到新高度，同比增长35%...</p>',
            category: '市场动态',
            cover_img: 'https://example.com/news3.jpg',
            view_count: 2134,
            like_count: 156,
            collect_count: 43,
            forward_count: 25,
            publish_date: '2024-03-13T16:45:00',
            created_at: '2024-03-13T16:40:00'
          }
        ]
      }
    },

    async loadFAQ() {
      try {
        const res = await api.getFaqs({ per_page: 5, sort: 'view_count' })
        this.faqList = res.data?.items || []
      } catch (error) {
        console.error('FAQ数据加载失败:', error)
      }
    },

    async loadInterpretations() {
      try {
        const res = await api.getInterpretations({ per_page: 6 })
        console.log('政策解读API返回:', res)
        
        // 尝试不同的数据结构
        let interpretations = []
        if (res.data?.items) {
          interpretations = res.data.items
        } else if (res.data?.data) {
          interpretations = res.data.data
        } else if (Array.isArray(res.data)) {
          interpretations = res.data
        }
        
        this.interpretationList = interpretations || []
        console.log('处理后的政策解读列表:', this.interpretationList)
        
        // 如果没有数据，使用模拟数据
        if (this.interpretationList.length === 0) {
          console.log('使用政策解读模拟数据')
          this.interpretationList = [
            { 
              id: 1, 
              title: '跨境融资实务操作指南',
              content: '<p>详细介绍跨境融资的实务操作流程和注意事项...</p>',
              video_url: 'https://example.com/video1.mp4',
              view_count: 356,
              like_count: 53,
              collect_count: 28,
              publish_date: '2024-01-23T14:30:00'
            },
            { 
              id: 2, 
              title: '外汇收支便民措施详解',
              content: '<p>全面解读最新的外汇收支便民措施...</p>',
              video_url: 'https://example.com/video2.mp4',
              view_count: 298,
              like_count: 44,
              collect_count: 25,
              publish_date: '2024-02-01T09:15:00'
            },
            { 
              id: 3, 
              title: '重庆自贸区金融创新政策解读',
              content: '<p>专门针对重庆自贸区的金融创新政策进行深入解读...</p>',
              video_url: 'https://example.com/video3.mp4',
              view_count: 387,
              like_count: 58,
              collect_count: 31,
              publish_date: '2024-02-08T11:45:00'
            },
            { 
              id: 4, 
              title: '企业境外投资合规要点',
              content: '<p>从合规角度详细讲解企业境外投资的各项要求...</p>',
              video_url: 'https://example.com/video4.mp4',
              view_count: 267,
              like_count: 41,
              collect_count: 22,
              publish_date: '2024-02-15T15:20:00'
            },
            { 
              id: 5, 
              title: '测试政策解答',
              content: '这是测试政策解答内容',
              video_url: null,
              view_count: 2,
              like_count: 0,
              collect_count: 0,
              publish_date: '2025-06-22T00:00:00'
            },
            { 
              id: 6, 
              title: '跨境电商外汇支付新规解读',
              content: '<p>针对跨境电商的最新外汇支付规定进行详细解读...</p>',
              video_url: 'https://example.com/video6.mp4',
              view_count: 234,
              like_count: 36,
              collect_count: 19,
              publish_date: '2024-02-20T10:00:00'
            }
          ]
        }
      } catch (error) {
        console.error('政策解读数据加载失败:', error)
        // API失败时使用模拟数据
        this.interpretationList = [
          { 
            id: 1, 
            title: '跨境融资实务操作指南',
            content: '<p>详细介绍跨境融资的实务操作流程和注意事项...</p>',
            video_url: 'https://example.com/video1.mp4',
            view_count: 356,
            like_count: 53,
            collect_count: 28,
            publish_date: '2024-01-23T14:30:00'
          },
          { 
            id: 2, 
            title: '外汇收支便民措施详解',
            content: '<p>全面解读最新的外汇收支便民措施...</p>',
            video_url: 'https://example.com/video2.mp4',
            view_count: 298,
            like_count: 44,
            collect_count: 25,
            publish_date: '2024-02-01T09:15:00'
          },
          { 
            id: 3, 
            title: '重庆自贸区金融创新政策解读',
            content: '<p>专门针对重庆自贸区的金融创新政策进行深入解读...</p>',
            video_url: 'https://example.com/video3.mp4',
            view_count: 387,
            like_count: 58,
            collect_count: 31,
            publish_date: '2024-02-08T11:45:00'
          },
          { 
            id: 4, 
            title: '企业境外投资合规要点',
            content: '<p>从合规角度详细讲解企业境外投资的各项要求...</p>',
            video_url: 'https://example.com/video4.mp4',
            view_count: 267,
            like_count: 41,
            collect_count: 22,
            publish_date: '2024-02-15T15:20:00'
          },
          { 
            id: 5, 
            title: '测试政策解答',
            content: '这是测试政策解答内容',
            video_url: null,
            view_count: 2,
            like_count: 0,
            collect_count: 0,
            publish_date: '2025-06-22T00:00:00'
          },
          { 
            id: 6, 
            title: '跨境电商外汇支付新规解读',
            content: '<p>针对跨境电商的最新外汇支付规定进行详细解读...</p>',
            video_url: 'https://example.com/video6.mp4',
            view_count: 234,
            like_count: 36,
            collect_count: 19,
            publish_date: '2024-02-20T10:00:00'
          }
        ]
      }
    },

    toSearch() {
      uni.navigateTo({
        url: '/pages/search/index'
      })
    },

    toPage(url) {
      uni.switchTab({
        url: url
      })
    },

    toNewsDetail(id) {
      uni.navigateTo({
        url: `/pages/news/detail?id=${id}`
      })
    },

    toFAQDetail(id) {
      uni.navigateTo({
        url: `/pages/faq/detail?id=${id}`
      })
    },

    toInterpretationDetail(id) {
      uni.navigateTo({
        url: `/pages/interpretation/detail?id=${id}`
      })
    },

    toNewsList() {
      uni.navigateTo({
        url: '/pages/news/list'
      })
    },

    toFAQList() {
      uni.navigateTo({
        url: '/pages/faq/list'
      })
    },

    toInterpretationList() {
      uni.navigateTo({
        url: '/pages/interpretation/list'
      })
    },

    toPolicyList() {
      uni.navigateTo({
        url: '/pages/policy/list'
      })
    },

    toBankListPage() {
      uni.navigateTo({
        url: '/pages/bank/list'
      })
    },

    async refreshBanks() {
      try {
        const res = await api.getBanks({ per_page: 3, random: true })
        console.log('刷新银行API返回:', res)
        
        // 尝试不同的数据结构
        let banks = []
        if (res.data?.items) {
          banks = res.data.items
        } else if (res.data?.data) {
          banks = res.data.data
        } else if (Array.isArray(res.data)) {
          banks = res.data
        }
        
        this.bankList = banks || []
        
        // 如果没有获取到数据，随机显示模拟数据
        if (this.bankList.length === 0) {
          const mockBanks = [
            {
              id: 1,
              name: '中国银行重庆分行',
              contact_person: '张经理',
              phone: '023-********',
              address: '重庆市渝中区解放碑步行街123号',
              latitude: 29.559434,
              longitude: 106.577011,
              icon: 'https://example.com/bank1.png',
              created_at: '2025-06-22T09:29:49'
            },
            {
              id: 2,
              name: '建设银行重庆分行',
              contact_person: '李经理',
              phone: '023-********',
              address: '重庆市江北区观音桥步行街456号',
              latitude: 29.574639,
              longitude: 106.539285,
              icon: 'https://example.com/bank2.png',
              created_at: '2025-06-22T09:29:49'
            },
            {
              id: 3,
              name: '工商银行重庆分行',
              contact_person: '王经理',
              phone: '023-********',
              address: '重庆市沙坪坝区三峡广场789号',
              latitude: 29.544606,
              longitude: 106.456878,
              icon: null,
              created_at: '2025-06-22T09:29:49'
            },
            {
              id: 4,
              name: '农业银行重庆分行',
              contact_person: '刘经理',
              phone: '023-********',
              address: '重庆市九龙坡区杨家坪步行街456号',
              latitude: 29.503143,
              longitude: 106.511470,
              icon: 'https://example.com/bank4.png',
              created_at: '2025-06-22T09:29:49'
            },
            {
              id: 5,
              name: '交通银行重庆分行',
              contact_person: '陈经理',
              phone: '023-********',
              address: '重庆市南岸区南坪步行街789号',
              latitude: 29.523456,
              longitude: 106.560789,
              icon: null,
              created_at: '2025-06-22T09:29:49'
            }
          ]
          // 随机选择3个
          const shuffled = mockBanks.sort(() => 0.5 - Math.random())
          this.bankList = shuffled.slice(0, 3)
        }
        
        uni.showToast({
          title: '已刷新',
          icon: 'success'
        })
      } catch (error) {
        console.error('刷新银行列表失败:', error)
        // 使用模拟数据并随机排序
        const mockBanks = [
          {
            id: 1,
            name: '中国银行重庆分行',
            contact_person: '张经理',
            phone: '023-********',
            address: '重庆市渝中区解放碑步行街123号',
            latitude: 29.559434,
            longitude: 106.577011,
            icon: 'https://example.com/bank1.png',
            created_at: '2025-06-22T09:29:49'
          },
          {
            id: 2,
            name: '建设银行重庆分行',
            contact_person: '李经理',
            phone: '023-********',
            address: '重庆市江北区观音桥步行街456号',
            latitude: 29.574639,
            longitude: 106.539285,
            icon: 'https://example.com/bank2.png',
            created_at: '2025-06-22T09:29:49'
          },
          {
            id: 3,
            name: '工商银行重庆分行',
            contact_person: '王经理',
            phone: '023-********',
            address: '重庆市沙坪坝区三峡广场789号',
            latitude: 29.544606,
            longitude: 106.456878,
            icon: null,
            created_at: '2025-06-22T09:29:49'
          },
          {
            id: 4,
            name: '农业银行重庆分行',
            contact_person: '刘经理',
            phone: '023-********',
            address: '重庆市九龙坡区杨家坪步行街456号',
            latitude: 29.503143,
            longitude: 106.511470,
            icon: 'https://example.com/bank4.png',
            created_at: '2025-06-22T09:29:49'
          },
          {
            id: 5,
            name: '交通银行重庆分行',
            contact_person: '陈经理',
            phone: '023-********',
            address: '重庆市南岸区南坪步行街789号',
            latitude: 29.523456,
            longitude: 106.560789,
            icon: null,
            created_at: '2025-06-22T09:29:49'
          }
        ]
        // 随机选择3个
        const shuffled = mockBanks.sort(() => 0.5 - Math.random())
        this.bankList = shuffled.slice(0, 3)
        
        uni.showToast({
          title: '已刷新',
          icon: 'success'
        })
      }
    },

    viewBankDetail(bank) {
      uni.showModal({
        title: bank.name,
        content: `联系人：${bank.contact_person}\n电话：${bank.phone}\n地址：${bank.address}`,
        showCancel: false
      })
    },

    callPhone(phone) {
      if (!phone || phone === '电话号码') {
        uni.showToast({
          title: '电话号码无效',
          icon: 'none'
        })
        return
      }
      
      uni.showModal({
        title: '拨打电话',
        content: `是否拨打 ${phone}？`,
        confirmText: '拨打',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: phone,
              fail: (err) => {
                console.error('拨打电话失败:', err)
                uni.showToast({
                  title: '拨打失败',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    },

    openMap(bank) {
      if (!bank.address || bank.address === '地址') {
        uni.showToast({
          title: '地址信息无效',
          icon: 'none'
        })
        return
      }
      
      // 如果有经纬度信息，直接使用
      if (bank.latitude && bank.longitude) {
        const latitude = typeof bank.latitude === 'number' ? bank.latitude : parseFloat(bank.latitude)
        const longitude = typeof bank.longitude === 'number' ? bank.longitude : parseFloat(bank.longitude)
        
        uni.openLocation({
          latitude: latitude,
          longitude: longitude,
          name: bank.name,
          address: bank.address,
          fail: (err) => {
            console.error('打开地图失败:', err)
            this.fallbackToAddressSearch(bank)
          }
        })
      } else {
        // 没有经纬度信息，使用地址搜索
        this.fallbackToAddressSearch(bank)
      }
    },

    fallbackToAddressSearch(bank) {
      // 备用方案：使用地址搜索
      uni.showModal({
        title: '查看位置',
        content: `地址：${bank.address}\n\n是否在地图中搜索该地址？`,
        confirmText: '打开地图',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 尝试使用默认的重庆坐标
            uni.openLocation({
              latitude: 29.563761,
              longitude: 106.550464,
              name: bank.name,
              address: bank.address,
              fail: (err) => {
                console.error('打开地图失败:', err)
                uni.showToast({
                  title: '无法打开地图',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    },

    recordPageView() {
      // 记录页面访问量
      api.recordInteraction({
        action: 'view',
        item_type: 'page',
        item_id: 'index'
      }).catch(err => {
        console.error('记录访问失败:', err)
      })
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) {
        return '今天'
      } else if (days === 1) {
        return '昨天'
      } else if (days < 7) {
        return `${days}天前`
      } else {
        return date.toLocaleDateString()
      }
    },

    onBannerClick(banner) {
      if (!banner.link_url) {
        return
      }
      
      // 记录轮播图点击事件
      this.recordBannerClick(banner)
      
      // 判断链接类型并进行相应跳转
      if (banner.link_url.startsWith('http://') || banner.link_url.startsWith('https://')) {
        // 外部链接，复制到剪贴板并提示
        uni.setClipboardData({
          data: banner.link_url,
          success: () => {
            uni.showToast({
              title: '链接已复制到剪贴板',
              icon: 'none'
            })
          }
        })
      } else if (banner.link_url.startsWith('/pages/')) {
        // 内部页面跳转
        uni.navigateTo({
          url: banner.link_url,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      } else {
        // 其他格式链接，尝试作为内部页面跳转
        uni.navigateTo({
          url: banner.link_url,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      }
    },

    // 记录轮播图点击事件
    async recordBannerClick(banner) {
      try {
        await api.recordView({
          content_type: 'banner',
          content_id: banner.id,
          action: 'click'
        })
      } catch (error) {
        console.error('记录轮播图点击失败:', error)
      }
    },

    onBannerImageError(banner, index) {
      console.error(`轮播图${index + 1}加载失败:`, banner)
      
      // 替换为默认图片
      const defaultImages = [
        '/static/images/banner/cq-sanxia.jpg',
        '/static/images/banner/cq-hongyadong.jpg',
        '/static/images/banner/cq-jiefangbei.jpg'
      ]
      
      // 更新该轮播图的图片URL
      if (this.bannerList[index]) {
        this.$set(this.bannerList[index], 'image', defaultImages[index % 3])
        console.log(`轮播图${index + 1}已替换为默认图片:`, defaultImages[index % 3])
      }
    },

    onBannerImageLoad(banner) {
      if (banner && banner.id) {
        console.log(`轮播图${banner.id}加载成功:`, banner.title)
      } else {
        console.log('轮播图加载成功，但数据异常:', banner)
      }
    },

    getCurrentTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hour = String(now.getHours()).padStart(2, '0')
      const minute = String(now.getMinutes()).padStart(2, '0')
      return `${year}年${month}月${day}日 ${hour}:${minute}`
    },

    getVideoThumbnail(item, index) {
      // 如果有真实的缩略图字段，使用真实的
      if (item && item.thumbnail) {
        return item.thumbnail
      }
      // 如果有封面图字段
      if (item && item.cover_image) {
        return item.cover_image
      }
      // 如果有视频地址且不为null，尝试生成缩略图
      if (item && item.video_url && item.video_url !== null) {
        // 对于示例URL，尝试生成缩略图URL
        if (item.video_url.includes('example.com')) {
          return item.video_url.replace('.mp4', '_thumb.jpg')
        }
        // 对于其他真实视频URL，可以根据视频平台的规则生成缩略图
        // 这里可以根据实际需求扩展
        return item.video_url.replace('.mp4', '_thumb.jpg')
      }
      // 返回空字符串，使用CSS渐变背景
      return ''
    },

    onVideoThumbnailError(item, index) {
      console.log(`视频${index + 1}缩略图加载失败:`, item)
      // 缩略图加载失败时，可以设置一个标记使用CSS背景
      this.$set(this.interpretationList[index], 'thumbnail_error', true)
    },

    getNewsCover(news) {
      // 如果已经标记为加载失败，返回空
      if (news && news.cover_error) {
        return ''
      }
      
      // 优先使用后端的cover_img字段
      if (news && news.cover_img && !news.cover_img.includes('example.com')) {
        return news.cover_img
      }
      // 其次使用thumbnail字段
      if (news && news.thumbnail && !news.thumbnail.includes('example.com')) {
        return news.thumbnail
      }
      // 再次使用cover_image字段
      if (news && news.cover_image && !news.cover_image.includes('example.com')) {
        return news.cover_image
      }
      // 如果有image字段也可以使用
      if (news && news.image && !news.image.includes('example.com')) {
        return news.image
      }
      // 返回空字符串使用占位符
      return ''
    },

    onNewsCoverError(news) {
      console.log('新闻封面加载失败:', news)
      // 封面加载失败时，设置标记使用占位符
      this.$set(news, 'cover_error', true)
    },

    formatViewCount(count) {
      if (!count || count === 0) return '0'
      if (count < 1000) return count.toString()
      if (count < 10000) return (count / 1000).toFixed(1) + 'k'
      return (count / 10000).toFixed(1) + 'w'
    },

    getNewsCategoryIcon(category) {
      const iconMap = {
        '政策发布': '📋',
        '会议活动': '🏛️',
        '数据统计': '📊',
        '通知公告': '📢',
        '新闻动态': '📰',
        '行业资讯': '💼',
        '法规解读': '⚖️',
        '业务指导': '📖',
        '市场分析': '📈',
        '市场动态': '📈',
        '测试分类': '🧪',
        '政策解读': '📋',
        '金融创新': '💰',
        '跨境融资': '🌐',
        '监管政策': '⚖️'
      }
      return iconMap[category] || '📰'
    },

    getNewsCategoryType(category) {
      const typeMap = {
        '政策发布': 'policy',
        '会议活动': 'meeting',
        '数据统计': 'data',
        '通知公告': 'notice',
        '新闻动态': 'news',
        '行业资讯': 'industry',
        '法规解读': 'law',
        '业务指导': 'guide',
        '市场分析': 'market',
        '市场动态': 'market',
        '测试分类': 'test',
        '政策解读': 'policy',
        '金融创新': 'finance',
        '跨境融资': 'finance',
        '监管政策': 'law'
      }
      return typeMap[category] || 'default'
    },

    getBankIcon(bank) {
      // 如果已经标记为加载失败，返回空
      if (bank && bank.icon_error) {
        return ''
      }
      
      // 优先使用后端的icon字段
      if (bank && bank.icon && !bank.icon.includes('example.com')) {
        return bank.icon
      }
      
      // 返回空字符串使用占位符
      return ''
    },

    onBankIconError(bank) {
      console.log('银行图标加载失败:', bank)
      // 图标加载失败时，设置标记使用占位符
      this.$set(bank, 'icon_error', true)
    }
  }
}
</script>

<style lang="scss" scoped>
.index-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  position: relative;
  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */
}

/* 顶部重庆山城风格背景 */
.index-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 280rpx;
  background: linear-gradient(180deg, 
    #1E90FF 0%, 
    #4A90E2 20%, 
    #6BA3E8 40%, 
    rgba(107, 163, 232, 0.7) 65%, 
    rgba(138, 180, 240, 0.4) 80%, 
    rgba(169, 197, 248, 0.2) 90%, 
    rgba(200, 214, 255, 0.1) 95%, 
    transparent 100%
  );
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.index-page::after {
  content: '';
  position: absolute;
  top: 200rpx;
  left: 0;
  right: 0;
  height: 120rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M0,100 L50,95 L100,85 L150,90 L200,80 L250,85 L300,75 L350,80 L400,70 L450,75 L500,65 L550,70 L600,60 L650,65 L700,55 L750,60 L800,50 L850,55 L900,45 L950,50 L1000,40 L1050,45 L1100,35 L1150,40 L1200,30 L1200,120 L0,120 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 120rpx;
  z-index: 2;
  opacity: 0.6;
}

/* 搜索头部 */
.search-header {
  padding: 20rpx 20rpx 20rpx;
  background: transparent;
  position: relative;
  z-index: 2;
}

.search-box {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 30rpx 30rpx;
  backdrop-filter: blur(15rpx);
  box-shadow: 0 4rpx 20rpx rgba(255, 255, 255, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.search-placeholder {
  margin-left: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 轮播图 */
.banner-section {
  margin: 15rpx 20rpx 30rpx 20rpx; /* 稍微调整上边距 */
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 
    0 12rpx 40rpx rgba(0, 0, 0, 0.1), 
    0 6rpx 20rpx rgba(30, 144, 255, 0.15),
    0 2rpx 8rpx rgba(248, 113, 162, 0.2); /* 多层柔和阴影 */
  background: #ffffff;
  position: relative;
  z-index: 3;
  border: 1rpx solid rgba(255, 255, 255, 0.3); /* 添加微妙的边框 */
}

.banner-swiper {
  height: 300rpx;
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 30rpx 20rpx;
}

.banner-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 功能菜单 */
.menu-section {
  margin: 0 20rpx 20rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40rpx;
  padding: 50rpx 30rpx;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.menu-icon {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #E5F0FF, #F0F8FF);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(30, 144, 255, 0.25);
  position: relative;
  overflow: hidden;
}

/* 菜单图标山城装饰 */
.menu-icon::before {
  content: '';
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: radial-gradient(circle, rgba(30, 144, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.icon-font {
  line-height: 1;
}

/* 数据统计 - 重庆山城风格 */
.stats-section {
  margin: 20rpx 30rpx 30rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.3);
  position: relative;
}

/* 山城装饰纹理 */
.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 30l15-15v30zM15 0l15 15H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  border-radius: 16rpx;
  pointer-events: none;
}

.stats-header {
  background: rgba(255, 255, 255, 0.1);
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.stats-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.stats-time {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 20rpx;
  position: relative;
  z-index: 1;
}

.stats-card {
  background: rgb(255, 255, 255);
  border-radius: 12rpx;
  padding: 20rpx 10rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stats-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15);
}

.stats-icon {
  font-size: 40rpx;
  margin-bottom: 15rpx;
  display: block;
}

.stats-info {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 10rpx;
}

.stats-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #705e67;
  line-height: 1;
}

.stats-unit {
  font-size: 24rpx;
  color: #705e67;
  margin-left: 4rpx;
}

.stats-label {
  font-size: 16rpx;
  color: #666;
  line-height: 1.2;
}

/* 通用区块样式 - 重庆风格 */
.section {
  background: white;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  position: relative;
}

/* 为区块添加重庆山城微妙装饰 */
.section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(45deg, rgba(220, 20, 60, 0.05) 0%, rgba(220, 20, 60, 0.02) 50%, transparent 100%);
  border-radius: 0 24rpx 0 80rpx;
  pointer-events: none;
}

/* 银行机构 */
.bank-section {
  background: white;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx 30rpx 20rpx;
  position: relative;
  z-index: 1;
}

.section-title-wrap {
  display: flex;
  flex-direction: column;
}

.section-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
}

.more-actions {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.refresh-btn {
  background: rgba(30, 144, 255, 0.1);
  color: #1E90FF;
}

.more-btn {
  background: #1E90FF;
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}

.bank-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 30rpx 30rpx;
}

.bank-section .bank-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4rpx solid transparent;
  color: #333;
}

.bank-section .bank-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 30rpx rgba(30, 144, 255, 0.15);
  border-left-color: #1E90FF;
}

.bank-section .bank-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.bank-logo {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  overflow: hidden;
  background: #f5f5f5;
}

.bank-logo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.bank-logo-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
}

.bank-basic-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.bank-section .bank-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333 !important;
  margin-bottom: 4rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bank-contact-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.bank-section .bank-contact {
  font-size: 24rpx;
  color: #666 !important;
  flex-shrink: 0;
}

.bank-section .bank-phone {
  font-size: 22rpx;
  color: #1E90FF !important;
  text-decoration: underline;
  transition: all 0.3s ease;
}

.bank-section .bank-phone:active {
  opacity: 0.7;
  transform: scale(0.98);
}

.bank-actions {
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
}

.bank-location-btn {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
}

.bank-location-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
}

.location-icon {
  font-size: 24rpx;
  color: white;
}

.bank-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  text-align: center;
}

.empty-icon {
  font-size: 48rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}




/* 新闻列表 */
.news-section {
  margin: 0 30rpx 30rpx;
}

.news-list {
  padding: 0 30rpx 30rpx;
}

.news-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.news-item:last-child {
  border-bottom: none;
}

.news-item:active {
  background: rgba(30, 144, 255, 0.05);
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.news-cover {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  background: #f5f5f5;
}

.news-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f0f0, #e8e8e8);
  font-size: 32rpx;
  color: #999;
}

/* 不同类型新闻的占位符样式 */
.news-cover-policy {
  background: linear-gradient(135deg, #ff9a9e, #fecfef) !important;
  color: #d63384 !important;
}

.news-cover-meeting {
  background: linear-gradient(135deg, #a8edea, #fed6e3) !important;
  color: #0d6efd !important;
}

.news-cover-data {
  background: linear-gradient(135deg, #ffeaa7, #fab1a0) !important;
  color: #fd7e14 !important;
}

.news-cover-notice {
  background: linear-gradient(135deg, #74b9ff, #0984e3) !important;
  color: #ffffff !important;
}

.news-cover-news {
  background: linear-gradient(135deg, #fd79a8, #fdcb6e) !important;
  color: #e84393 !important;
}

.news-cover-industry {
  background: linear-gradient(135deg, #6c5ce7, #a29bfe) !important;
  color: #ffffff !important;
}

.news-cover-law {
  background: linear-gradient(135deg, #00b894, #00cec9) !important;
  color: #ffffff !important;
}

.news-cover-guide {
  background: linear-gradient(135deg, #e17055, #fab1a0) !important;
  color: #ffffff !important;
}

.news-cover-market {
  background: linear-gradient(135deg, #00b894, #55efc4) !important;
  color: #ffffff !important;
}

.news-cover-default {
  background: linear-gradient(135deg, #ddd6fe, #e879f9) !important;
  color: #8b5cf6 !important;
}

.news-cover-test {
  background: linear-gradient(135deg, #ff7675, #fd79a8) !important;
  color: #ffffff !important;
}

.news-cover-finance {
  background: linear-gradient(135deg, #fdcb6e, #e17055) !important;
  color: #ffffff !important;
}

.news-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.news-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}



.news-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
  flex-wrap: wrap;
}

.news-category {
  font-size: 20rpx;
  color: #1E90FF;
  background: rgba(30, 144, 255, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.news-date {
  font-size: 20rpx;
  color: #999;
}

.news-views {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 2rpx;
}

.news-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
  align-self: center;
  flex-shrink: 0;
}

/* 热门提问 */
.faq-section {
  margin: 0 30rpx 30rpx;
}

.faq-list {
  padding: 0 30rpx 30rpx;
}

.faq-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item:active {
  background: rgba(30, 144, 255, 0.05);
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.faq-icon {
  font-size: 24rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 193, 7, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.faq-content {
  flex: 1;
  min-width: 0;
}

.faq-question {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.faq-stats {
  display: flex;
  gap: 20rpx;
}

.faq-stat {
  font-size: 22rpx;
  color: #999;
}

.faq-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
}

/* 政策解读 */
.interpretation-section {
  margin: 0 20rpx 30rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}



.interpretation-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
  padding: 0 30rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.interpretation-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 160rpx;
  transition: all 0.3s ease;
}

.interpretation-item:active {
  transform: translateY(-2rpx);
}

.interpretation-cover {
  position: relative;
  width: 100%;
  height: 100rpx;
  background: #F0F0F0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
  flex-shrink: 0;
}

.interpretation-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 当没有真实图片时使用的渐变背景 */
.interpretation-bg-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.interpretation-bg-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.interpretation-bg-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.interpretation-bg-4 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.interpretation-bg-5 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.interpretation-bg-6 {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  pointer-events: none;
}

.text-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40rpx;
  pointer-events: none;
}

.interpretation-item-title {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.3;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  height: 52rpx;
  flex-shrink: 0;
}


</style> 
 