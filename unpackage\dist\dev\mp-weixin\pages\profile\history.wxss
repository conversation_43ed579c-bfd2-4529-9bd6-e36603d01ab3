.history-page.data-v-4c035876 {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding: 20rpx 0;
}
.history-list.data-v-4c035876 {
  padding: 0 30rpx;
}
.history-item.data-v-4c035876 {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.item-header.data-v-4c035876 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.type-tag.data-v-4c035876 {
  background-color: #1E90FF;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.date.data-v-4c035876 {
  font-size: 24rpx;
  color: #999;
}
.item-title.data-v-4c035876 {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
}
.item-content.data-v-4c035876 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.empty-state.data-v-4c035876, .loading-tip.data-v-4c035876 {
  text-align: center;
  padding-top: 100rpx;
  color: #999;
}
.empty-icon.data-v-4c035876 {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

