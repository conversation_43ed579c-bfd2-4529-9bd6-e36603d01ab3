# 公共样式使用说明

## 概述

`common.css` 文件包含了项目中常用的工具类样式，已经在 `App.vue` 中全局引入，所有页面都可以直接使用这些样式类。

## 样式类别

### 1. Flex布局类

#### 基础Flex方向
- `.flex-col` - 垂直方向的flex布局
- `.flex-row` - 水平方向的flex布局

#### 水平对齐
- `.justify-start` - 左对齐
- `.justify-center` - 居中对齐
- `.justify-end` - 右对齐
- `.justify-between` - 两端对齐
- `.justify-around` - 环绕对齐
- `.justify-evenly` - 平均分布

#### 垂直对齐
- `.align-start` - 顶部对齐
- `.align-center` - 垂直居中
- `.align-end` - 底部对齐

#### 组合布局类
- `.flex-center` - 水平垂直居中
- `.flex-between` - 垂直居中，水平两端对齐
- `.flex-start` - 垂直居中，水平左对齐
- `.flex-end` - 垂直居中，水平右对齐

### 2. 间距工具类

#### 外边距 (margin)
- `.m-0` 到 `.m-4` - 全方向外边距 (0, 10rpx, 20rpx, 30rpx, 40rpx)
- `.mt-0` 到 `.mt-4` - 上外边距
- `.mb-0` 到 `.mb-4` - 下外边距
- `.ml-0` 到 `.ml-4` - 左外边距
- `.mr-0` 到 `.mr-4` - 右外边距

#### 内边距 (padding)
- `.p-0` 到 `.p-4` - 全方向内边距
- `.pt-0` 到 `.pt-4` - 上内边距
- `.pb-0` 到 `.pb-4` - 下内边距
- `.pl-0` 到 `.pl-4` - 左内边距
- `.pr-0` 到 `.pr-4` - 右内边距

### 3. 文本工具类

#### 文本对齐
- `.text-left` - 左对齐
- `.text-center` - 居中对齐
- `.text-right` - 右对齐

#### 字体大小
- `.text-xs` - 20rpx
- `.text-sm` - 24rpx
- `.text-base` - 28rpx
- `.text-lg` - 32rpx
- `.text-xl` - 36rpx
- `.text-2xl` - 40rpx

#### 字体粗细
- `.font-normal` - 正常粗细
- `.font-bold` - 加粗

### 4. 颜色工具类

#### 文字颜色
- `.text-primary` - 主色调文字 (#1E90FF)
- `.text-secondary` - 次要色文字 (#4A90E2)
- `.text-gray` - 灰色文字 (#999999)
- `.text-dark` - 深色文字 (#333333)
- `.text-white` - 白色文字

#### 背景颜色
- `.bg-primary` - 主色调背景
- `.bg-secondary` - 次要色背景
- `.bg-white` - 白色背景
- `.bg-gray` - 灰色背景

### 5. 边框工具类

#### 边框
- `.border` - 全边框
- `.border-t` - 上边框
- `.border-b` - 下边框
- `.border-l` - 左边框
- `.border-r` - 右边框

#### 圆角
- `.rounded` - 小圆角 (8rpx)
- `.rounded-lg` - 大圆角 (16rpx)
- `.rounded-full` - 圆形

### 6. 文本省略

- `.ellipsis` - 单行文本省略
- `.ellipsis-2` - 两行文本省略
- `.ellipsis-3` - 三行文本省略

## 使用示例

### 基础布局示例
```html
<!-- 水平居中的卡片 -->
<view class="flex-center p-3 bg-white rounded-lg">
  <text class="text-lg font-bold text-primary">标题</text>
</view>

<!-- 两端对齐的列表项 -->
<view class="flex-between p-2 border-b">
  <text class="text-base text-dark">项目名称</text>
  <text class="text-sm text-gray">2024-01-01</text>
</view>

<!-- 垂直布局的内容区域 -->
<view class="flex-col p-3">
  <text class="text-xl font-bold mb-2">标题</text>
  <text class="text-base text-gray ellipsis-2">这是一段很长的描述文字...</text>
</view>
```

### 网格布局示例
```html
<!-- 功能菜单网格 -->
<view class="flex-row justify-around p-3">
  <view class="flex-col align-center">
    <view class="w-full h-full bg-primary rounded-lg mb-1"></view>
    <text class="text-sm">功能1</text>
  </view>
  <view class="flex-col align-center">
    <view class="w-full h-full bg-secondary rounded-lg mb-1"></view>
    <text class="text-sm">功能2</text>
  </view>
</view>
```

### 列表项示例
```html
<!-- 新闻列表项 -->
<view class="flex-row p-3 bg-white rounded border-b">
  <image class="rounded mr-2" style="width: 120rpx; height: 80rpx;"></image>
  <view class="flex-col flex-1">
    <text class="text-base font-bold ellipsis-2 mb-1">新闻标题</text>
    <text class="text-sm text-gray">2024-01-01</text>
  </view>
</view>
```

## 注意事项

1. 所有样式类都已经全局引入，无需在单个页面中重复引入
2. 尺寸单位使用 `rpx`，适配不同屏幕尺寸
3. 颜色值与项目主题色保持一致
4. 建议优先使用工具类，减少自定义样式的编写
5. 如需扩展样式，可以在 `common.css` 中添加新的工具类

## 扩展建议

如果需要添加新的工具类，建议按照以下规范：
- 保持命名的一致性和语义化
- 使用项目统一的尺寸和颜色变量
- 添加相应的文档说明
- 考虑响应式设计的需求
