# tabBar图标说明

## 图标列表 (SVG矢量图标)

### 首页图标
- **普通状态**: 🏠 房屋图标
- **激活状态**: 🏡 实心房屋图标
- **文件**: home.svg / home-active.svg

### 政策图标
- **普通状态**: 📄 文档图标
- **激活状态**: 📋 带内容的文档图标
- **文件**: policy.svg / policy-active.svg

### 咨询图标
- **普通状态**: 💬 聊天气泡图标
- **激活状态**: 💭 带装饰的聊天气泡
- **文件**: consult.svg / consult-active.svg

### 个人中心图标
- **普通状态**: 👤 用户轮廓图标
- **激活状态**: 👥 带装饰的用户图标
- **文件**: profile.svg / profile-active.svg

## SVG图标优势

1. **矢量无损**: 在任何分辨率下都保持清晰
2. **体积小**: 比PNG图标文件更小
3. **可定制**: 可以通过代码修改颜色和样式
4. **跨平台**: 支持所有现代浏览器和设备

## 技术规格

1. **尺寸**: 52x52px (在SVG中设置width和height)
2. **格式**: SVG矢量图形
3. **颜色**:
   - 普通状态：#999999 (灰色)
   - 激活状态：#DC143C (深红色)
4. **ViewBox**: 0 0 24 24 (标准Material Design规格)

## SVG图标特点

- **首页**: Material Design房屋图标
- **政策**: 文档图标，激活状态添加内容线条
- **咨询**: 聊天气泡图标，激活状态添加对话点
- **个人中心**: 用户图标，激活状态添加白色装饰

## 在线SVG工具

- [Iconfont](https://www.iconfont.cn/) - 阿里巴巴矢量图标库
- [Material Design Icons](https://materialdesignicons.com/) - Material Design图标库
- [Feather Icons](https://feathericons.com/) - 简洁的SVG图标集 