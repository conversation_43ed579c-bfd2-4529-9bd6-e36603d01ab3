<template>
  <view class="demo-page">
    <!-- 页面头部 -->
    <view class="header bg-primary text-white p-3 text-center">
      <text class="text-xl font-bold">底部菜单状态演示</text>
    </view>

    <!-- 演示说明 -->
    <view class="content p-3">
      <view class="info-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">不同页面选中状态</text>
        <view class="info-list">
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 首页选中：首页图标激活，文字蓝色</text>
          </view>
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 政策选中：政策图标激活，文字蓝色</text>
          </view>
          <view class="info-item mb-2">
            <text class="text-sm text-dark">✅ 咨询选中：咨询图标激活，文字蓝色</text>
          </view>
          <view class="info-item">
            <text class="text-sm text-dark">✅ 我的选中：我的图标激活，文字蓝色</text>
          </view>
        </view>
      </view>

      <!-- 状态切换演示 -->
      <view class="switch-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">状态切换演示</text>
        <view class="switch-buttons flex-row justify-around mb-3">
          <button 
            class="switch-btn"
            :class="{ active: currentState === 0 }"
            @click="switchToState(0)"
          >
            首页状态
          </button>
          <button 
            class="switch-btn"
            :class="{ active: currentState === 1 }"
            @click="switchToState(1)"
          >
            政策状态
          </button>
          <button 
            class="switch-btn"
            :class="{ active: currentState === 2 }"
            @click="switchToState(2)"
          >
            咨询状态
          </button>
          <button 
            class="switch-btn"
            :class="{ active: currentState === 3 }"
            @click="switchToState(3)"
          >
            我的状态
          </button>
        </view>
        
        <!-- 当前状态显示 -->
        <view class="current-state p-2 bg-gray rounded">
          <text class="text-sm text-dark">当前状态: {{ getStateName(currentState) }}</text>
        </view>
      </view>

      <!-- 图标状态对比 -->
      <view class="compare-card bg-white rounded-lg p-3 mb-3">
        <text class="text-lg font-bold text-primary mb-2">图标状态对比</text>
        
        <!-- 首页状态 -->
        <view class="state-section mb-3">
          <text class="text-base text-dark mb-2">首页选中状态:</text>
          <view class="state-demo flex-row justify-around p-2 bg-gray rounded">
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/home-icon-active.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(31,115,255,1.000000);">首页</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/policy-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">政策</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/consult-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">咨询</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/profile-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">我的</text>
            </view>
          </view>
        </view>

        <!-- 政策状态 -->
        <view class="state-section mb-3">
          <text class="text-base text-dark mb-2">政策选中状态:</text>
          <view class="state-demo flex-row justify-around p-2 bg-gray rounded">
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/home-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">首页</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/policy-icon-active.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(31,115,255,1.000000);">政策</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/consult-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">咨询</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/profile-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">我的</text>
            </view>
          </view>
        </view>

        <!-- 咨询状态 -->
        <view class="state-section mb-3">
          <text class="text-base text-dark mb-2">咨询选中状态:</text>
          <view class="state-demo flex-row justify-around p-2 bg-gray rounded">
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/home-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">首页</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/policy-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">政策</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/consult-icon-active.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(31,115,255,1.000000);">咨询</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/profile-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">我的</text>
            </view>
          </view>
        </view>

        <!-- 我的状态 -->
        <view class="state-section">
          <text class="text-base text-dark mb-2">我的选中状态:</text>
          <view class="state-demo flex-row justify-around p-2 bg-gray rounded">
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/home-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">首页</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/policy-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">政策</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/consult-icon.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(147,152,160,1.000000);">咨询</text>
            </view>
            <view class="demo-item flex-col align-center">
              <image class="demo-icon mb-1" src="/static/images/tabbar/profile-icon-active.png" mode="aspectFit"/>
              <text class="demo-text text-xs" style="color: rgba(31,115,255,1.000000);">我的</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 技术说明 -->
      <view class="tech-card bg-white rounded-lg p-3">
        <text class="text-lg font-bold text-primary mb-2">技术实现说明</text>
        <view class="tech-list">
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">图标管理:</text>
            <text class="text-xs text-gray block mt-1">每个功能都有普通和激活两种状态的图标</text>
          </view>
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">颜色系统:</text>
            <text class="text-xs text-gray block mt-1">激活: rgba(31,115,255,1.000000), 普通: rgba(147,152,160,1.000000)</text>
          </view>
          <view class="tech-item mb-2">
            <text class="text-sm text-dark font-bold">状态管理:</text>
            <text class="text-xs text-gray block mt-1">通过updateTabState方法动态切换图标和颜色</text>
          </view>
          <view class="tech-item">
            <text class="text-sm text-dark font-bold">设计还原:</text>
            <text class="text-xs text-gray block mt-1">完全按照设计稿的不同页面选中状态实现</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部占位 -->
    <view class="tabbar-placeholder"></view>
  </view>
</template>

<script>
export default {
  name: 'TabbarStatesDemo',
  data() {
    return {
      currentState: 0
    }
  },
  
  methods: {
    switchToState(index) {
      this.currentState = index;
      uni.showToast({
        title: `切换到${this.getStateName(index)}状态`,
        icon: 'none',
        duration: 1500
      });
    },
    
    getStateName(index) {
      const names = ['首页', '政策', '咨询', '我的'];
      return names[index] || '未知';
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/static/css/variables.scss';
@import '@/static/css/mixins.scss';

.demo-page {
  min-height: 100vh;
  background-color: $gray-light;
}

.content {
  padding-bottom: 200rpx; // 为tabbar留出空间
}

.info-card,
.switch-card,
.compare-card,
.tech-card {
  @include card-shadow('md');
}

.switch-btn {
  @include button-style($gray-light, $text-color, 'small');
  min-width: 120rpx;
  
  &.active {
    @include button-style($primary-color, white, 'small');
  }
}

.demo-icon {
  width: 32rpx;
  height: 32rpx;
}

.demo-item {
  width: 60rpx;
}

.demo-text {
  text-align: center;
  line-height: 1.2;
}

.tabbar-placeholder {
  height: 200rpx; // 为tabbar留出空间
}

// 响应式适配
@include respond-to('md') {
  .switch-buttons {
    justify-content: space-between;
  }
  
  .state-demo {
    justify-content: space-between;
  }
}
</style>
