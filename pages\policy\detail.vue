<template>
  <view class="policy-detail">
    <view v-if="policyDetail" class="content">
    

      <!-- 政策内容 -->
      <view class="policy-container">
        <!-- 标题部分 -->
        <view class="title-section">
          <view class="title">{{ policyDetail.title }}</view>
          <view class="meta-info">
            <text class="publish-date">{{ formatDate(policyDetail.publish_date) }}</text>
            <text class="stats-bar"> | {{ policyDetail.view_count }}人阅读</text>
          </view>
        </view>

        <!-- 分隔线 -->
        <view class="divider"></view>

        <!-- 正文部分 -->
        <view class="content-section" @tap="handleContentTap">
          <view class="content-text" v-html="processedContent"></view>
        </view>
      </view>

      <!-- 附件 -->
      <view v-if="policyDetail.attachment_url" class="attachment-section">
        <view class="attachment-title">附件：</view>
        <view class="attachment-item" @click="openAttachment">
          <text class="attachment-icon">📎</text>
          <text class="attachment-name">{{ policyDetail.attachment_name || '点击查看附件' }}</text>
        </view>
      </view>

    </view>
    
    <!-- 悬浮操作栏 -->
    <view v-if="policyDetail" class="action-bar-sticky">
      <button @click="toggleLike" class="action-btn" :class="{ active: isLiked }">
        <text class="icon">👍</text>
        <text class="action-text">{{ policyDetail.like_count > 0 ? policyDetail.like_count : '点赞' }}</text>
      </button>
      <button @click="toggleCollect" class="action-btn" :class="{ active: isCollected }">
        <text class="icon">{{ isCollected ? '⭐' : '☆' }}</text>
        <text class="action-text">{{ policyDetail.collect_count > 0 ? policyDetail.collect_count : '收藏' }}</text>
      </button>
      <button open-type="share" class="action-btn">
        <text class="icon">📤</text>
        <text class="action-text">{{ policyDetail.forward_count > 0 ? policyDetail.forward_count : '分享' }}</text>
      </button>
    </view>

    <view v-else class="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      policyDetail: null,
      policyId: null,
      isLiked: false,
      isCollected: false,
      guideSteps: [
        {
          title: '准备材料',
          desc: '根据业务类型准备相应的申请材料和证明文件'
        },
        {
          title: '提交申请',
          desc: '通过银行或外汇管理部门指定渠道提交申请'
        },
        {
          title: '审核批准',
          desc: '相关部门进行审核，符合条件的予以批准'
        },
        {
          title: '办理业务',
          desc: '获得批准后按照规定办理相关业务手续'
        }
      ]
    }
  },

  computed: {
    processedContent() {
      if (!this.policyDetail || !this.policyDetail.content) {
        return ''
      }

      // 处理相对路径的链接，转换为绝对路径
      let content = this.policyDetail.content

      // 将相对路径的文件链接转换为完整的下载链接
      content = content.replace(
        /href="\/files\//g,
        'href="http://kjrzymt.com/files/'
      )

      // 为所有链接添加样式和数据属性
      content = content.replace(
        /<a\s+([^>]*?)href="([^"]*?)"([^>]*?)>/g,
        '<a $1href="$2"$3 data-link="$2" style="color: #1E90FF; text-decoration: underline; cursor: pointer;">'
      )

      return content
    }
  },

  onLoad(options) {
    this.policyId = options.id
    if (this.policyId) {
      this.loadPolicyDetail()
    }
  },

  methods: {
    async loadPolicyDetail() {
      try {
        const res = await api.getPolicyDetail(this.policyId)
        this.policyDetail = res.data
        
        // 详情加载成功后，开始检查状态和记录浏览
        this.recordView()
        this.checkStatus()
      } catch (error) {
        console.error('加载政策详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    async checkStatus() {
      const token = uni.getStorageSync('token');
      if (!token) return;

      try {
        const params = {
          item_type: 'policy',
          item_id: parseInt(this.policyId)
        };
        const [likeRes, collectRes] = await Promise.all([
          api.checkInteractionStatus({ ...params, actions: ['like'] }),
          api.checkCollectionStatus(params)
        ]);

        if (likeRes.data.like) {
          this.isLiked = true;
        }
        if (collectRes.data.is_collected) {
          this.isCollected = true;
        }
      } catch (error) {
        console.error('检查状态失败:', error);
      }
    },

    async recordView() {
      const token = uni.getStorageSync('token');
      if (!token) {
        return; // 未登录，不记录
      }
      
      const userInfo = uni.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        console.error('无法获取用户信息，无法记录浏览');
        return;
      }

      try {
        await api.recordView({
          item_type: 'policy',
          item_id: parseInt(this.policyId)
          // 后端会从token中解析user_id，因此前端无需传递
        });
        if (this.policyDetail) {
          this.policyDetail.view_count++;
        }
      } catch (error) {
        console.error('记录浏览失败:', error);
      }
    },

    async toggleLike() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('点赞');
        return;
      }

      try {
        const res = await api.toggleInteraction({
          action: 'like',
          item_type: 'policy',
          item_id: parseInt(this.policyId)
        });
        
        this.isLiked = res.data.action === 'added';
        this.policyDetail.like_count = res.data.current_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('点赞操作失败:', error);
      }
    },

    async toggleCollect() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('收藏');
        return;
      }

      try {
        const res = await api.toggleCollection({
          item_type: 'policy',
          item_id: parseInt(this.policyId)
        });
        
        this.isCollected = res.data.action === 'added';
        this.policyDetail.collect_count = res.data.current_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('收藏操作失败:', error);
      }
    },

    consultPolicy() {
      uni.switchTab({
        url: '/pages/consultation/index'
      })
    },

    promptLogin(action) {
      uni.showModal({
        title: '请先登录',
        content: `登录后才能${action}哦`,
        success: (res) => {
          if (res.confirm) {
            uni.switchTab({
              url: '/pages/profile/index'
            });
          }
        }
      });
    },

    formatDate(dateStr) {
      return new Date(dateStr).toLocaleDateString()
    },

    // 处理内容区域的点击事件
    handleContentTap(event) {
      // 在 H5 环境下，尝试获取点击的元素
      if (typeof window !== 'undefined' && event.target) {
        const target = event.target

        // 检查是否点击了链接
        if (target.tagName === 'A' || target.closest('a')) {
          const link = target.tagName === 'A' ? target : target.closest('a')
          const url = link.getAttribute('href') || link.getAttribute('data-link')

          if (url) {
            event.preventDefault()
            event.stopPropagation()
            this.handleLinkClick(url)
          }
        }
      }
    },

    // 处理富文本中的链接点击
    handleLinkClick(url) {
      console.log('点击链接:', url)

      if (url.startsWith('http://') || url.startsWith('https://')) {
        // 外部链接，复制到剪贴板
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制到剪贴板，请在浏览器中打开',
              showCancel: false
            })
          }
        })
      } else if (url.startsWith('/pages/')) {
        // 内部页面跳转
        uni.navigateTo({
          url: url,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      } else {
        // 其他链接，也复制到剪贴板
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制到剪贴板，请在浏览器中打开',
              showCancel: false
            })
          }
        })
      }
    },

    openAttachment() {
      if (!this.policyDetail.attachment_url) return;
      uni.showLoading({ title: '正在打开附件' });
      uni.downloadFile({
        url: this.policyDetail.attachment_url,
        success: (res) => {
          const filePath = res.tempFilePath;
          uni.openDocument({
            filePath: filePath,
            showMenu: true,
            success: () => {
              uni.hideLoading();
            },
            fail: (err) => {
              uni.hideLoading();
              uni.showToast({
                title: '打开附件失败',
                icon: 'none'
              });
              console.error('打开附件失败', err);
            }
          });
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({
            title: '下载附件失败',
            icon: 'none'
          });
          console.error('下载附件失败', err);
        }
      });
    },

    onShareAppMessage() {
      return {
        title: this.policyDetail.title,
        path: `/pages/policy/detail?id=${this.policyId}`,
        imageUrl: '', // 你可以在这里设置一个默认的分享图片
      };
    },
  }
}
</script>

<style lang="scss" scoped>
.policy-detail {
  background: linear-gradient(180deg, #f0f9ff 0%, #f8fafc 40%, #f1f5f9 100%);
  min-height: 100vh;
  position: relative;
}

/* 顶部蓝色渐变背景 */
.policy-detail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, 
    #1E90FF 0%, 
    #007bff 30%, 
    #60a5fa 60%, 
    rgba(96, 165, 250, 0.6) 80%, 
    rgba(147, 197, 253, 0.3) 90%, 
    transparent 100%
  );
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.policy-detail::after {
  content: '';
  position: absolute;
  top: 140rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}

.content {
  padding: 40rpx 30rpx 180rpx; /* 增加底部 padding 为悬浮按钮留出空间 */
  position: relative;
  z-index: 3;
}

.policy-container {
  background: #ffffff;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.title-section {
  margin-bottom: 30rpx;
}

.divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #e5e7eb 80%, transparent 100%);
  margin: 30rpx 0;
}

.title {
  font-size: 44rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 30rpx;
  text-align: justify;
}

.meta-info {
  color: #888;
  font-size: 26rpx;
}

.stats-bar {
  display: inline;
  margin-left: 10rpx;
}

.content-section {
  margin-top: 20rpx;
}

.content-text {
  font-size: 32rpx;
  line-height: 1.8;
  color: #34495e;
}

.attachment-section {
  margin-top: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.attachment-title {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #e9ecef;
  border-radius: 8rpx;
}

.attachment-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.attachment-name {
  color: #007bff;
  font-size: 28rpx;
  text-decoration: underline;
}

.action-bar-sticky {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  transition: color 0.2s ease-in-out, transform 0.1s ease;

  &::after {
    border: none;
  }
  
  &:active {
    transform: scale(0.9);
  }
  
  &.active {
    color: #3b82f6;
  }
  
  .icon {
    font-size: 44rpx;
    margin-bottom: 6rpx;
    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    opacity: 0.7;
    filter: grayscale(80%);
  }
  
  &.active .icon {
    transform: scale(1.1);
    opacity: 1;
    filter: grayscale(0%);
  }
  
  .action-text {
    font-size: 24rpx;
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
}
</style> 