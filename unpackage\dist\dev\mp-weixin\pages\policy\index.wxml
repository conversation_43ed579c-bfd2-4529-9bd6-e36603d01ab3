<view class="policy-page data-v-223ccfa4"><view class="search-section data-v-223ccfa4"><view data-event-opts="{{[['tap',[['toSearch',['$event']]]]]}}" class="search-box data-v-223ccfa4" bindtap="__e"><text class="search-icon data-v-223ccfa4">🔍</text><text class="search-placeholder data-v-223ccfa4">搜索政策文件...</text></view></view><view class="filter-section data-v-223ccfa4"><scroll-view class="filter-scroll data-v-223ccfa4" scroll-x="{{true}}"><view class="filter-list data-v-223ccfa4"><block wx:for="{{categoryList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['selectCategory',['$0'],[[['categoryList','value',item.value,'value']]]]]]]}}" class="{{['filter-item','data-v-223ccfa4',(currentCategory===item.value)?'active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view></scroll-view></view><view class="policy-list-section data-v-223ccfa4"><block wx:for="{{$root.l0}}" wx:for-item="policy" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['toPolicyDetail',['$0'],[[['policyList','id',policy.$orig.id,'id']]]]]]]}}" class="policy-item data-v-223ccfa4" bindtap="__e"><view class="policy-header data-v-223ccfa4"><text class="policy-title ellipsis-2 data-v-223ccfa4">{{policy.$orig.title}}</text><view class="policy-category data-v-223ccfa4">{{policy.$orig.category1}}</view></view><view class="policy-content ellipsis-2 data-v-223ccfa4"><rich-text nodes="{{policy.$orig.content}}"></rich-text></view><view class="policy-footer data-v-223ccfa4"><view class="policy-date data-v-223ccfa4">{{policy.m0}}</view><view class="policy-stats data-v-223ccfa4"><text class="stat-item data-v-223ccfa4"><text class="icon data-v-223ccfa4">👁️</text>{{''+(policy.$orig.view_count||0)+''}}</text><text class="stat-item data-v-223ccfa4"><text class="icon data-v-223ccfa4">👍</text>{{''+(policy.$orig.like_count||0)+''}}</text><text class="stat-item data-v-223ccfa4"><text class="icon data-v-223ccfa4">⭐</text>{{''+(policy.$orig.collect_count||0)+''}}</text><text class="stat-item data-v-223ccfa4"><text class="icon data-v-223ccfa4">📤</text>{{''+(policy.$orig.forward_count||0)+''}}</text></view></view></view></block></view><block wx:if="{{hasMore}}"><view class="load-more data-v-223ccfa4"><view data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" class="load-more-btn data-v-223ccfa4" bindtap="__e"><text class="data-v-223ccfa4">加载更多</text></view></view></block><block wx:else><block wx:if="{{$root.g0>0}}"><view class="no-more data-v-223ccfa4"><text class="data-v-223ccfa4">没有更多数据了</text></view></block></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-223ccfa4"><text class="empty-icon data-v-223ccfa4">📄</text><text class="empty-text data-v-223ccfa4">暂无政策文件</text></view></block><custom-tab-bar vue-id="255c22d0-1" class="data-v-223ccfa4" bind:__l="__l"></custom-tab-bar></view>