<template>
  <view class="news-list-page">
    <!-- 顶部导航 -->
    <view class="page-header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="header-title">
        <text class="title-text">🌉 最新资讯</text>
        <text class="title-subtitle">山城跨境金融最新动态</text>
      </view>
      <view class="header-right">
        <text class="refresh-btn" @click="refreshList">🔄</text>
      </view>
    </view>



    <!-- 筛选标签 -->
    <view class="filter-section" v-if="categories.length > 0">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-tags">
          <view 
            class="filter-tag" 
            :class="{ active: selectedCategory === '' }"
            @click="selectCategory('')"
          >
            全部
          </view>
          <view 
            v-for="category in categories" 
            :key="category"
            class="filter-tag" 
            :class="{ active: selectedCategory === category }"
            @click="selectCategory(category)"
          >
            {{ category }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 新闻列表 -->
    <view class="news-content">
      <view v-if="loading" class="loading-section">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载新闻资讯...</text>
      </view>

      <view v-else-if="filteredNewsList.length === 0" class="empty-section">
        <text class="empty-icon">🌉</text>
        <text class="empty-title">暂无相关新闻资讯</text>
        <text class="empty-subtitle">稍后再来看看</text>
      </view>

      <view v-else class="news-list">
        <view 
          class="news-item" 
          v-for="news in filteredNewsList" 
          :key="news.id" 
          @click="toNewsDetail(news.id)"
        >
          <view class="news-cover">
            <image 
              v-if="getNewsCover(news)" 
              :src="getNewsCover(news)" 
              class="news-cover-image"
              mode="aspectFill"
              @error="onNewsCoverError(news)"
            />
            <view 
              v-else 
              class="news-cover-placeholder"
              :class="'news-cover-' + getNewsCategoryType(news.category)"
            >
              {{ getNewsCategoryIcon(news.category) }}
            </view>
          </view>
          
          <view class="news-content">
            <text class="news-title">{{ news.title }}</text>
            <view class="news-meta">
              <text class="news-category">{{ news.category }}</text>
              <text class="news-date">{{ formatDate(news.publish_date) }}</text>
              <text class="news-views">👁 {{ formatViewCount(news.view_count) }}</text>
            </view>
          </view>
          <text class="news-arrow">›</text>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading" class="load-more">
        <text class="load-more-btn" @click="loadMore">加载更多</text>
      </view>
    </view>
  </view>
</template>

<script>
import { api } from '@/utils/api.js'

export default {
  data() {
    return {
      newsList: [],
      filteredNewsList: [],
      loading: true,
      selectedCategory: '',
      categories: [],
      page: 1,
      perPage: 20,
      hasMore: true,

    }
  },

  onLoad() {
    this.loadNewsList()
  },

  onPullDownRefresh() {
    this.refreshList()
  },

  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadNewsList() {
      try {
        this.loading = true
        const params = {
          page: this.page,
          per_page: this.perPage
        }
        
        if (this.selectedCategory) {
          params.category = this.selectedCategory
        }
        


        const res = await api.getNews(params)
        console.log('新闻列表API返回:', res)
        
        if (res.success && res.data) {
          const news = res.data.items || []
          
          if (this.page === 1) {
            this.newsList = news
          } else {
            this.newsList = [...this.newsList, ...news]
          }
          
          this.filteredNewsList = this.newsList
          this.hasMore = res.data.has_next || false
          
          // 提取分类
          if (this.page === 1) {
            this.extractCategories()
          }
        } else {
          console.error('API返回数据格式错误:', res)
          uni.showToast({
            title: '数据格式错误',
            icon: 'error'
          })
        }
        

        
      } catch (error) {
        console.error('加载新闻列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },



    extractCategories() {
      const categories = [...new Set(this.newsList.map(news => news.category).filter(Boolean))]
      this.categories = categories
    },

    refreshList() {
      this.page = 1
      this.hasMore = true
      this.loadNewsList()
    },

    loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++
        this.loadNewsList()
      }
    },



    selectCategory(category) {
      this.selectedCategory = category
      this.page = 1
      this.hasMore = true
      this.loadNewsList()
    },

    toNewsDetail(id) {
      uni.navigateTo({
        url: `/pages/news/detail?id=${id}`
      })
    },

    getNewsCover(news) {
      return news.cover_img || null
    },

    onNewsCoverError(news) {
      console.log('新闻封面加载失败:', news)
    },

    getNewsCategoryType(category) {
      const categoryMap = {
        '政策动态': 'policy',
        '产品创新': 'innovation',
        '风险防控': 'risk',
        '培训活动': 'training',
        '环境优化': 'environment',
        '数据报告': 'report'
      }
      return categoryMap[category] || 'default'
    },

    getNewsCategoryIcon(category) {
      const iconMap = {
        '政策动态': '📋',
        '产品创新': '💡',
        '风险防控': '🛡️',
        '培训活动': '🎓',
        '环境优化': '🌱',
        '数据报告': '📊'
      }
      return iconMap[category] || '📰'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) {
        return '昨天'
      } else if (diffDays <= 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    },

    formatViewCount(count) {
      if (!count) return '0'
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w'
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k'
      }
      return count.toString()
    }
  }
}
</script>

<style scoped>
.news-list-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.title-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 4rpx;
}

.header-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.refresh-btn {
  font-size: 36rpx;
  color: #1E90FF;
}



.filter-section {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.filter-tag {
  padding: 12rpx 24rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #1E90FF;
  color: white;
}

.news-content {
  padding: 20rpx 0;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
}

.news-list {
  padding: 0 30rpx;
}

.news-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.news-item:last-child {
  border-bottom: none;
}

.news-item:active {
  background: rgba(30, 144, 255, 0.05);
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.news-cover {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.news-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
}

.news-cover-policy {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.news-cover-innovation {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.news-cover-risk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.news-cover-training {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.news-cover-environment {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.news-cover-report {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.news-cover-default {
  background: linear-gradient(135deg, #ddd6fe, #e879f9);
  color: #8b5cf6;
}

.news-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.news-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
  flex-wrap: wrap;
}

.news-category {
  font-size: 20rpx;
  color: #1E90FF;
  background: rgba(30, 144, 255, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.news-date {
  font-size: 20rpx;
  color: #999;
}

.news-views {
  font-size: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  gap: 2rpx;
}

.news-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
  align-self: center;
  flex-shrink: 0;
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #e8e8e8;
}
</style> 