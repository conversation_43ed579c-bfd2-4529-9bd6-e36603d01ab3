<template>
  <view class="inquiry-history-page">
    <view v-if="inquiryList.length > 0" class="inquiry-list">
      <view v-for="item in inquiryList" :key="item.id" class="inquiry-item">
        <view class="item-header">
          <text class="type-tag">{{ item.type === 'business_consult' ? '业务咨询' : '政策需求' }}</text>
          <text class="date">{{ formatDate(item.created_at) }}</text>
        </view>
        <view class="item-content">
          <text>{{ item.content }}</text>
        </view>
        <view v-if="item.bank" class="item-footer">
          <text>相关银行：{{ item.bank.name }}</text>
        </view>
      </view>
    </view>

    <view v-if="!loading && inquiryList.length === 0" class="empty-state">
      <text class="empty-icon">💬</text>
      <text class="empty-text">暂无咨询记录</text>
    </view>

    <view class="loading-tip" v-if="loading">加载中...</view>
    <view class="loading-tip" v-if="!hasMore && inquiryList.length > 0">没有更多了</view>
  </view>
</template>

<script>
import { api } from '@/utils/api';

export default {
  data() {
    return {
      inquiryList: [],
      currentPage: 1,
      hasMore: true,
      loading: false,
      userId: null,
    };
  },
  onLoad() {
    const userInfo = uni.getStorageSync('userInfo');
    if (userInfo && userInfo.id) {
      this.userId = userInfo.id;
      this.loadInquiries(true);
    } else {
      uni.showToast({ title: '请先登录', icon: 'none' });
      uni.switchTab({ url: '/pages/profile/index' });
    }
  },
  onPullDownRefresh() {
    this.loadInquiries(true);
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadInquiries();
    }
  },
  methods: {
    async loadInquiries(reset = false) {
      if (this.loading) return;
      this.loading = true;

      if (reset) {
        this.currentPage = 1;
        this.inquiryList = [];
      }

      try {
        const res = await api.getMyInquiries(this.userId, {
          page: this.currentPage,
          per_page: 15,
        });
        
        const newList = res.data?.items || [];
        this.inquiryList = this.inquiryList.concat(newList);
        this.hasMore = newList.length === 15;
        this.currentPage++;
        
      } catch (error) {
        console.error("加载咨询列表失败", error);
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },
    formatDate(dateStr) {
      return new Date(dateStr).toLocaleDateString();
    }
  }
}
</script>

<style lang="scss" scoped>
.inquiry-history-page {
  background-color: #f4f5f7;
  min-height: 100vh;
  padding: 20rpx 0;
}

.inquiry-list {
  padding: 0 30rpx;
}

.inquiry-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.type-tag {
  background-color: #DC143C;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.date {
  font-size: 24rpx;
  color: #999;
}

.item-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-footer {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666;
}

.empty-state, .loading-tip {
  text-align: center;
  padding-top: 100rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}
</style> 