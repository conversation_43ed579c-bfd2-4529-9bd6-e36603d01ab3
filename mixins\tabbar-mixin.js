/**
 * TabBar状态同步Mixin
 * 用于在tabbar页面中自动同步底部菜单的选中状态
 */

export default {
  data() {
    return {
      // tabbar页面路径映射
      tabbarPages: {
        '/pages/index/index': 0,        // 首页
        '/pages/policy/index': 1,       // 政策
        '/pages/consultation/index': 2, // 咨询
        '/pages/profile/index': 3       // 我的
      }
    }
  },

  onShow() {
    // 页面显示时更新tabbar状态
    this.updateTabbarState();
  },

  onLoad() {
    // 页面加载时更新tabbar状态
    this.updateTabbarState();
  },

  methods: {
    /**
     * 更新tabbar状态
     */
    updateTabbarState() {
      try {
        // 获取当前页面路径
        const pages = getCurrentPages();
        if (pages.length === 0) return;
        
        const currentPage = pages[pages.length - 1];
        const currentRoute = '/' + currentPage.route;
        
        // 查找对应的tab索引
        const tabIndex = this.tabbarPages[currentRoute];
        
        if (typeof tabIndex === 'number') {
          // 通过事件通知tabbar组件更新状态
          uni.$emit('tabbar-update', tabIndex);
          
          // 如果页面中有tabbar组件的引用，直接调用
          if (this.$refs && this.$refs.tabbar && this.$refs.tabbar.updateSelected) {
            this.$refs.tabbar.updateSelected(tabIndex);
          }
          
          console.log(`TabBar状态更新: ${currentRoute} -> 索引 ${tabIndex}`);
        }
      } catch (error) {
        console.error('更新TabBar状态失败:', error);
      }
    },

    /**
     * 手动设置tabbar状态
     * @param {number} index - tab索引
     */
    setTabbarState(index) {
      if (typeof index === 'number' && index >= 0 && index <= 3) {
        uni.$emit('tabbar-update', index);
        
        if (this.$refs && this.$refs.tabbar && this.$refs.tabbar.updateSelected) {
          this.$refs.tabbar.updateSelected(index);
        }
      }
    }
  }
}
