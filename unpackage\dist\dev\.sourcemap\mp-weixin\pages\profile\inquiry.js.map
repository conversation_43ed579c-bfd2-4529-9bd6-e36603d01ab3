{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/inquiry.vue?c432", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/inquiry.vue?4759", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/inquiry.vue?2103", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/inquiry.vue?3d0d", "uni-app:///pages/profile/inquiry.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/inquiry.vue?47af", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/inquiry.vue?e390"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "inquiryList", "currentPage", "hasMore", "loading", "userId", "onLoad", "uni", "title", "icon", "url", "onPullDownRefresh", "onReachBottom", "methods", "loadInquiries", "reset", "api", "page", "per_page", "res", "newList", "console", "formatDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAk3B,CAAgB,m0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4Bt4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;MACAC;QAAAC;QAAAC;MAAA;MACAF;QAAAG;MAAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKAC;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;cAAA;gBAAA;gBAEA;gBACAd;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAe;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAA6oD,CAAgB,k/CAAG,EAAC,C;;;;;;;;;;;ACAjqD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/profile/inquiry.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/inquiry.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./inquiry.vue?vue&type=template&id=36452778&scoped=true&\"\nvar renderjs\nimport script from \"./inquiry.vue?vue&type=script&lang=js&\"\nexport * from \"./inquiry.vue?vue&type=script&lang=js&\"\nimport style0 from \"./inquiry.vue?vue&type=style&index=0&id=36452778&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"36452778\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/inquiry.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./inquiry.vue?vue&type=template&id=36452778&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.inquiryList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.inquiryList, function (item, __i0__) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.formatDate(item.created_at)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var g1 = !_vm.loading && _vm.inquiryList.length === 0\n  var g2 = !_vm.hasMore && _vm.inquiryList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./inquiry.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./inquiry.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"inquiry-history-page\">\r\n    <view v-if=\"inquiryList.length > 0\" class=\"inquiry-list\">\r\n      <view v-for=\"item in inquiryList\" :key=\"item.id\" class=\"inquiry-item\">\r\n        <view class=\"item-header\">\r\n          <text class=\"type-tag\">{{ item.type === 'business_consult' ? '业务咨询' : '政策需求' }}</text>\r\n          <text class=\"date\">{{ formatDate(item.created_at) }}</text>\r\n        </view>\r\n        <view class=\"item-content\">\r\n          <text>{{ item.content }}</text>\r\n        </view>\r\n        <view v-if=\"item.bank\" class=\"item-footer\">\r\n          <text>相关银行：{{ item.bank.name }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view v-if=\"!loading && inquiryList.length === 0\" class=\"empty-state\">\r\n      <text class=\"empty-icon\">💬</text>\r\n      <text class=\"empty-text\">暂无咨询记录</text>\r\n    </view>\r\n\r\n    <view class=\"loading-tip\" v-if=\"loading\">加载中...</view>\r\n    <view class=\"loading-tip\" v-if=\"!hasMore && inquiryList.length > 0\">没有更多了</view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { api } from '@/utils/api';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      inquiryList: [],\r\n      currentPage: 1,\r\n      hasMore: true,\r\n      loading: false,\r\n      userId: null,\r\n    };\r\n  },\r\n  onLoad() {\r\n    const userInfo = uni.getStorageSync('userInfo');\r\n    if (userInfo && userInfo.id) {\r\n      this.userId = userInfo.id;\r\n      this.loadInquiries(true);\r\n    } else {\r\n      uni.showToast({ title: '请先登录', icon: 'none' });\r\n      uni.switchTab({ url: '/pages/profile/index' });\r\n    }\r\n  },\r\n  onPullDownRefresh() {\r\n    this.loadInquiries(true);\r\n  },\r\n  onReachBottom() {\r\n    if (this.hasMore && !this.loading) {\r\n      this.loadInquiries();\r\n    }\r\n  },\r\n  methods: {\r\n    async loadInquiries(reset = false) {\r\n      if (this.loading) return;\r\n      this.loading = true;\r\n\r\n      if (reset) {\r\n        this.currentPage = 1;\r\n        this.inquiryList = [];\r\n      }\r\n\r\n      try {\r\n        const res = await api.getMyInquiries(this.userId, {\r\n          page: this.currentPage,\r\n          per_page: 15,\r\n        });\r\n        \r\n        const newList = res.data?.items || [];\r\n        this.inquiryList = this.inquiryList.concat(newList);\r\n        this.hasMore = newList.length === 15;\r\n        this.currentPage++;\r\n        \r\n      } catch (error) {\r\n        console.error(\"加载咨询列表失败\", error);\r\n      } finally {\r\n        this.loading = false;\r\n        uni.stopPullDownRefresh();\r\n      }\r\n    },\r\n    formatDate(dateStr) {\r\n      return new Date(dateStr).toLocaleDateString();\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.inquiry-history-page {\r\n  background-color: #f4f5f7;\r\n  min-height: 100vh;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.inquiry-list {\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.inquiry-item {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.item-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.type-tag {\r\n  background-color: #DC143C;\r\n  color: #fff;\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 8rpx;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.date {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.item-content {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  padding-bottom: 20rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.item-footer {\r\n  margin-top: 20rpx;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.empty-state, .loading-tip {\r\n  text-align: center;\r\n  padding-top: 100rpx;\r\n  color: #999;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 80rpx;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./inquiry.vue?vue&type=style&index=0&id=36452778&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./inquiry.vue?vue&type=style&index=0&id=36452778&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051458\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}