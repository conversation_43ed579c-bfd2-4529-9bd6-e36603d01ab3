/* SCSS变量文件 - variables.scss */
/* 可以在任何SCSS文件中引入使用 */

// 颜色变量定义
$primary-color: #1E90FF;
$secondary-color: #4A90E2;
$background-color: #FFFFFF;
$text-color: #333333;
$border-color: #E5E5E5;
$gray-light: #F5F5F5;
$gray-medium: #999999;

// 间距变量定义
$spacing-0: 0;
$spacing-1: 10rpx;
$spacing-2: 20rpx;
$spacing-3: 30rpx;
$spacing-4: 40rpx;
$spacing-5: 50rpx;
$spacing-6: 60rpx;

// 字体大小变量定义
$text-xs: 20rpx;
$text-sm: 24rpx;
$text-base: 28rpx;
$text-lg: 32rpx;
$text-xl: 36rpx;
$text-2xl: 40rpx;
$text-3xl: 48rpx;

// 圆角变量定义
$border-radius-sm: 8rpx;
$border-radius-lg: 16rpx;
$border-radius-xl: 24rpx;

// 阴影变量定义
$shadow-sm: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
$shadow-md: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
$shadow-lg: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);

// 动画变量定义
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 层级变量定义
$z-index-dropdown: 1000;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 断点变量定义（用于响应式设计）
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// 容器最大宽度
$container-max-width: 1200rpx;

// 导航栏高度
$navbar-height: 88rpx;
$tabbar-height: 100rpx;
