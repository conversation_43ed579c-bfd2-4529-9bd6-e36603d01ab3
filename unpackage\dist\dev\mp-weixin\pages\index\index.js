(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/index/index"],{

/***/ 35:
/*!************************************************************************************************!*\
  !*** F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/main.js?{"page":"pages%2Findex%2Findex"} ***!
  \************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pages/index/index.vue */ 36));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 36:
/*!*****************************************************************************!*\
  !*** F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue ***!
  \*****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=********&scoped=true& */ 37);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 39);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_********_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=********&lang=scss&scoped=true& */ 45);
/* harmony import */ var _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "********",
  null,
  false,
  _index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/index/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 37:
/*!************************************************************************************************************************!*\
  !*** F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?vue&type=template&id=********&scoped=true& ***!
  \************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=********&scoped=true& */ 38);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_********_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 38:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?vue&type=template&id=********&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getCurrentTime()
  var g0 = _vm.bankList && _vm.bankList.length > 0
  var l0 = _vm.__map(_vm.bankList.slice(0, 3), function (bank, __i0__) {
    var $orig = _vm.__get_orig(bank)
    var m1 = g0 ? _vm.getBankIcon(bank) : null
    var m2 = g0 && m1 ? _vm.getBankIcon(bank) : null
    return {
      $orig: $orig,
      m1: m1,
      m2: m2,
    }
  })
  var l1 = _vm.__map(_vm.newsList.slice(0, 3), function (news, __i1__) {
    var $orig = _vm.__get_orig(news)
    var m3 = _vm.getNewsCover(news)
    var m4 = m3 ? _vm.getNewsCover(news) : null
    var m5 = !m3 ? _vm.getNewsCategoryType(news.category) : null
    var m6 = !m3 ? _vm.getNewsCategoryIcon(news.category) : null
    var m7 = _vm.formatDate(news.publish_date)
    var m8 = _vm.formatViewCount(news.view_count)
    return {
      $orig: $orig,
      m3: m3,
      m4: m4,
      m5: m5,
      m6: m6,
      m7: m7,
      m8: m8,
    }
  })
  var l2 = _vm.faqList.slice(0, 4)
  var l3 = _vm.__map(
    _vm.interpretationList.slice(0, 6),
    function (item, index) {
      var $orig = _vm.__get_orig(item)
      var m9 = _vm.getVideoThumbnail(item, index)
      var m10 = m9 ? _vm.getVideoThumbnail(item, index) : null
      return {
        $orig: $orig,
        m9: m9,
        m10: m10,
      }
    }
  )
  if (!_vm._isMounted) {
    _vm.e0 = function ($event, bank) {
      var _temp = arguments[arguments.length - 1].currentTarget.dataset,
        _temp2 = _temp.eventParams || _temp["event-params"],
        bank = _temp2.bank
      var _temp, _temp2
      return _vm.viewBankDetail(bank)
    }
    _vm.e1 = function ($event, bank) {
      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp4 = _temp3.eventParams || _temp3["event-params"],
        bank = _temp4.bank
      var _temp3, _temp4
      return _vm.onBankIconError(bank)
    }
    _vm.e2 = function ($event, bank) {
      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp6 = _temp5.eventParams || _temp5["event-params"],
        bank = _temp6.bank
      var _temp5, _temp6
      $event.stopPropagation()
      return _vm.callPhone(bank.phone)
    }
    _vm.e3 = function ($event, bank) {
      var _temp7 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp8 = _temp7.eventParams || _temp7["event-params"],
        bank = _temp8.bank
      var _temp7, _temp8
      $event.stopPropagation()
      return _vm.openMap(bank)
    }
    _vm.e4 = function ($event, news) {
      var _temp9 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp10 = _temp9.eventParams || _temp9["event-params"],
        news = _temp10.news
      var _temp9, _temp10
      return _vm.toNewsDetail(news.id)
    }
    _vm.e5 = function ($event, news) {
      var _temp11 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp12 = _temp11.eventParams || _temp11["event-params"],
        news = _temp12.news
      var _temp11, _temp12
      return _vm.onNewsCoverError(news)
    }
    _vm.e6 = function ($event, faq) {
      var _temp13 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp14 = _temp13.eventParams || _temp13["event-params"],
        faq = _temp14.faq
      var _temp13, _temp14
      return _vm.toFAQDetail(faq.id)
    }
    _vm.e7 = function ($event, item) {
      var _temp15 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp16 = _temp15.eventParams || _temp15["event-params"],
        item = _temp16.item
      var _temp15, _temp16
      return _vm.toInterpretationDetail(item.id)
    }
    _vm.e8 = function ($event, item, index) {
      var _temp17 = arguments[arguments.length - 1].currentTarget.dataset,
        _temp18 = _temp17.eventParams || _temp17["event-params"],
        item = _temp18.item,
        index = _temp18.index
      var _temp17, _temp18
      return _vm.onVideoThumbnailError(item, index)
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        g0: g0,
        l0: l0,
        l1: l1,
        l2: l2,
        l3: l3,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 39:
/*!******************************************************************************************************!*\
  !*** F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 40);
/* harmony import */ var _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 40:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 41));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 43));
var _api = __webpack_require__(/*! @/utils/api */ 44);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var CustomTabBar = function CustomTabBar() {
  __webpack_require__.e(/*! require.ensure | custom-tab-bar/index */ "custom-tab-bar/index").then((function () {
    return resolve(__webpack_require__(/*! @/custom-tab-bar/index.vue */ 159));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    CustomTabBar: CustomTabBar
  },
  data: function data() {
    return {
      bannerList: [],
      statistics: {
        policyCount: 0,
        visitCount: 0,
        consultCount: 0
      },
      bankList: [],
      newsList: [],
      faqList: [],
      interpretationList: []
    };
  },
  onLoad: function onLoad() {
    try {
      this.loadPageData().catch(function (err) {
        console.error('页面数据加载失败:', err);
      });
    } catch (err) {
      console.error('onLoad错误:', err);
    }
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.loadPageData().then(function () {
      uni.stopPullDownRefresh();
    }).catch(function (err) {
      console.error('下拉刷新失败:', err);
      uni.stopPullDownRefresh();
    });
  },
  onShow: function onShow() {
    if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {
      this.$root.$mp.page.getTabBar().$vm.updateSelected(0);
    }
    // 记录页面访问
    this.recordPageView();
  },
  methods: {
    loadPageData: function loadPageData() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                uni.showLoading({
                  title: '加载中...'
                });

                // 并行加载数据
                _context.next = 4;
                return Promise.all([_this.loadBanners(), _this.loadStatistics(), _this.loadBanks(), _this.loadNews(), _this.loadFAQ(), _this.loadInterpretations()]);
              case 4:
                uni.hideLoading();
                _context.next = 11;
                break;
              case 7:
                _context.prev = 7;
                _context.t0 = _context["catch"](0);
                uni.hideLoading();
                console.error('数据加载失败:', _context.t0);
              case 11:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 7]]);
      }))();
    },
    loadBanners: function loadBanners() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res, banners;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return _api.api.getBanners();
              case 3:
                res = _context2.sent;
                console.log('轮播图API返回:', res);

                // 根据实际API返回，轮播图数据直接在res.data中（数组格式）
                banners = res.data || [];
                console.log('原始轮播图数据:', banners);

                // 处理轮播图数据，映射字段名
                _this2.bannerList = banners.filter(function (banner) {
                  return banner.is_active;
                }) // 只显示启用的轮播图
                .sort(function (a, b) {
                  return a.sort - b.sort;
                }) // 按sort字段排序
                .map(function (banner, index) {
                  // 处理图片URL
                  var imageUrl = banner.image;

                  // 如果是本地API文件URL，确保可以正常访问
                  if (imageUrl && imageUrl.includes('localhost:5000')) {
                    // 如果图片URL看起来正常，保持原样
                    console.log("\u8F6E\u64AD\u56FE".concat(index + 1, "\u56FE\u7247URL:"), imageUrl);
                  } else if (imageUrl && imageUrl.startsWith('https://example.com')) {
                    // 替换示例URL为默认图片
                    imageUrl = "/static/images/banner/cq-".concat(index % 3 === 0 ? 'sanxia' : index % 3 === 1 ? 'hongyadong' : 'jiefangbei', ".jpg");
                    console.log("\u8F6E\u64AD\u56FE".concat(index + 1, "\u4F7F\u7528\u9ED8\u8BA4\u56FE\u7247:"), imageUrl);
                  }
                  return {
                    id: banner.id,
                    title: banner.title,
                    image: imageUrl,
                    link_url: banner.link === '#' ? '' : banner.link,
                    // 处理无效链接
                    sort_order: banner.sort,
                    is_active: banner.is_active,
                    created_at: banner.created_at
                  };
                });
                console.log('处理后的轮播图数据:', _this2.bannerList);

                // 如果后端没有轮播图数据或数据为空，使用默认图片
                if (_this2.bannerList.length === 0) {
                  console.log('使用默认轮播图');
                  _this2.bannerList = [{
                    id: 1,
                    title: '重庆长江三峡',
                    image: '/static/images/banner/cq-sanxia.jpg',
                    link_url: '',
                    sort_order: 1,
                    is_active: true
                  }, {
                    id: 2,
                    title: '重庆洪崖洞夜景',
                    image: '/static/images/banner/cq-hongyadong.jpg',
                    link_url: '',
                    sort_order: 2,
                    is_active: true
                  }, {
                    id: 3,
                    title: '重庆解放碑',
                    image: '/static/images/banner/cq-jiefangbei.jpg',
                    link_url: '',
                    sort_order: 3,
                    is_active: true
                  }];
                }
                _context2.next = 16;
                break;
              case 12:
                _context2.prev = 12;
                _context2.t0 = _context2["catch"](0);
                console.error('轮播图数据加载失败:', _context2.t0);

                // API失败时使用默认轮播图
                _this2.bannerList = [{
                  id: 1,
                  title: '重庆长江三峡',
                  image: '/static/images/banner/cq-sanxia.jpg',
                  link_url: '',
                  sort_order: 1,
                  is_active: true
                }, {
                  id: 2,
                  title: '重庆洪崖洞夜景',
                  image: '/static/images/banner/cq-hongyadong.jpg',
                  link_url: '',
                  sort_order: 2,
                  is_active: true
                }, {
                  id: 3,
                  title: '重庆解放碑',
                  image: '/static/images/banner/cq-jiefangbei.jpg',
                  link_url: '',
                  sort_order: 3,
                  is_active: true
                }];
              case 16:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 12]]);
      }))();
    },
    loadStatistics: function loadStatistics() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var res;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return _api.api.getStatistics();
              case 3:
                res = _context3.sent;
                console.log('统计数据API返回:', res);

                // 处理统计数据，确保字段映射正确
                if (res.data) {
                  _this3.statistics = {
                    policyCount: res.data.policyCount || res.data.policy_count || 0,
                    bankCount: res.data.bankCount || res.data.bank_count || 0,
                    consultCount: res.data.consultCount || res.data.consult_count || 0,
                    visitCount: res.data.visitCount || res.data.visit_count || 0
                  };
                }
                console.log('处理后的统计数据:', _this3.statistics);
                _context3.next = 13;
                break;
              case 9:
                _context3.prev = 9;
                _context3.t0 = _context3["catch"](0);
                console.error('统计数据加载失败:', _context3.t0);
                // API失败时使用默认数据
                _this3.statistics = {
                  policyCount: 7,
                  bankCount: 6,
                  consultCount: 9,
                  visitCount: 22
                };
              case 13:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 9]]);
      }))();
    },
    loadBanks: function loadBanks() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var _res$data, _res$data2, res, banks;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                _context4.next = 3;
                return _api.api.getBanks({
                  per_page: 3
                });
              case 3:
                res = _context4.sent;
                console.log('银行API返回:', res);

                // 尝试不同的数据结构
                banks = [];
                if ((_res$data = res.data) !== null && _res$data !== void 0 && _res$data.items) {
                  banks = res.data.items;
                } else if ((_res$data2 = res.data) !== null && _res$data2 !== void 0 && _res$data2.data) {
                  banks = res.data.data;
                } else if (Array.isArray(res.data)) {
                  banks = res.data;
                }
                _this4.bankList = banks || [];
                console.log('处理后的银行列表:', _this4.bankList);

                // 如果没有获取到数据，使用模拟数据
                if (_this4.bankList.length === 0) {
                  console.log('使用银行模拟数据');
                  _this4.bankList = [{
                    id: 1,
                    name: '中国银行重庆分行',
                    contact_person: '张经理',
                    phone: '023-********',
                    address: '重庆市渝中区解放碑步行街123号',
                    latitude: 29.559434,
                    longitude: 106.577011,
                    icon: 'https://example.com/bank1.png',
                    created_at: '2025-06-22T09:29:49'
                  }, {
                    id: 2,
                    name: '建设银行重庆分行',
                    contact_person: '李经理',
                    phone: '023-********',
                    address: '重庆市江北区观音桥步行街456号',
                    latitude: 29.574639,
                    longitude: 106.539285,
                    icon: 'https://example.com/bank2.png',
                    created_at: '2025-06-22T09:29:49'
                  }, {
                    id: 3,
                    name: '工商银行重庆分行',
                    contact_person: '王经理',
                    phone: '023-********',
                    address: '重庆市沙坪坝区三峡广场789号',
                    latitude: 29.544606,
                    longitude: 106.456878,
                    icon: null,
                    created_at: '2025-06-22T09:29:49'
                  }];
                }
                _context4.next = 16;
                break;
              case 12:
                _context4.prev = 12;
                _context4.t0 = _context4["catch"](0);
                console.error('银行数据加载失败:', _context4.t0);
                // API失败时使用模拟数据
                _this4.bankList = [{
                  id: 1,
                  name: '中国银行重庆分行',
                  contact_person: '张经理',
                  phone: '023-********',
                  address: '重庆市渝中区解放碑步行街123号',
                  latitude: 29.559434,
                  longitude: 106.577011,
                  icon: 'https://example.com/bank1.png',
                  created_at: '2025-06-22T09:29:49'
                }, {
                  id: 2,
                  name: '建设银行重庆分行',
                  contact_person: '李经理',
                  phone: '023-********',
                  address: '重庆市江北区观音桥步行街456号',
                  latitude: 29.574639,
                  longitude: 106.539285,
                  icon: 'https://example.com/bank2.png',
                  created_at: '2025-06-22T09:29:49'
                }, {
                  id: 3,
                  name: '工商银行重庆分行',
                  contact_person: '王经理',
                  phone: '023-********',
                  address: '重庆市沙坪坝区三峡广场789号',
                  latitude: 29.544606,
                  longitude: 106.456878,
                  icon: null,
                  created_at: '2025-06-22T09:29:49'
                }];
              case 16:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 12]]);
      }))();
    },
    loadNews: function loadNews() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var _res$data3, _res$data4, res, news;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                _context5.next = 3;
                return _api.api.getNews({
                  per_page: 3
                });
              case 3:
                res = _context5.sent;
                console.log('新闻API返回:', res);

                // 尝试不同的数据结构
                news = [];
                if ((_res$data3 = res.data) !== null && _res$data3 !== void 0 && _res$data3.items) {
                  news = res.data.items;
                } else if ((_res$data4 = res.data) !== null && _res$data4 !== void 0 && _res$data4.data) {
                  news = res.data.data;
                } else if (Array.isArray(res.data)) {
                  news = res.data;
                }
                _this5.newsList = news || [];
                console.log('处理后的新闻列表:', _this5.newsList);

                // 如果没有数据，使用模拟数据
                if (_this5.newsList.length === 0) {
                  console.log('使用新闻模拟数据');
                  _this5.newsList = [{
                    id: 1,
                    title: '重庆自贸区跨境融资便利化措施正式发布',
                    content: '<p>重庆自贸区管委会今日正式发布跨境融资便利化措施，新措施将有效降低企业融资成本，提升跨境融资效率...</p>',
                    category: '政策发布',
                    cover_img: 'https://example.com/news1.jpg',
                    view_count: 1256,
                    like_count: 89,
                    collect_count: 23,
                    forward_count: 12,
                    publish_date: '2024-03-15T09:30:00',
                    created_at: '2024-03-15T09:25:00'
                  }, {
                    id: 2,
                    title: '外汇局重庆分局召开跨境融资政策解读会',
                    content: '<p>为帮助企业更好理解和运用跨境融资政策，外汇局重庆分局召开专题解读会，详细解读了最新的跨境融资监管政策和操作流程...</p>',
                    category: '会议活动',
                    cover_img: 'https://example.com/news2.jpg',
                    view_count: 892,
                    like_count: 67,
                    collect_count: 15,
                    forward_count: 8,
                    publish_date: '2024-03-14T14:20:00',
                    created_at: '2024-03-14T14:15:00'
                  }, {
                    id: 3,
                    title: '重庆企业跨境融资规模创历史新高',
                    content: '<p>据重庆市金融办统计数据显示，2024年第一季度，重庆企业跨境融资总规模达到新高度，同比增长35%...</p>',
                    category: '市场动态',
                    cover_img: 'https://example.com/news3.jpg',
                    view_count: 2134,
                    like_count: 156,
                    collect_count: 43,
                    forward_count: 25,
                    publish_date: '2024-03-13T16:45:00',
                    created_at: '2024-03-13T16:40:00'
                  }];
                }
                _context5.next = 16;
                break;
              case 12:
                _context5.prev = 12;
                _context5.t0 = _context5["catch"](0);
                console.error('新闻数据加载失败:', _context5.t0);
                // API失败时使用模拟数据
                _this5.newsList = [{
                  id: 1,
                  title: '重庆自贸区跨境融资便利化措施正式发布',
                  content: '<p>重庆自贸区管委会今日正式发布跨境融资便利化措施，新措施将有效降低企业融资成本，提升跨境融资效率...</p>',
                  category: '政策发布',
                  cover_img: 'https://example.com/news1.jpg',
                  view_count: 1256,
                  like_count: 89,
                  collect_count: 23,
                  forward_count: 12,
                  publish_date: '2024-03-15T09:30:00',
                  created_at: '2024-03-15T09:25:00'
                }, {
                  id: 2,
                  title: '外汇局重庆分局召开跨境融资政策解读会',
                  content: '<p>为帮助企业更好理解和运用跨境融资政策，外汇局重庆分局召开专题解读会，详细解读了最新的跨境融资监管政策和操作流程...</p>',
                  category: '会议活动',
                  cover_img: 'https://example.com/news2.jpg',
                  view_count: 892,
                  like_count: 67,
                  collect_count: 15,
                  forward_count: 8,
                  publish_date: '2024-03-14T14:20:00',
                  created_at: '2024-03-14T14:15:00'
                }, {
                  id: 3,
                  title: '重庆企业跨境融资规模创历史新高',
                  content: '<p>据重庆市金融办统计数据显示，2024年第一季度，重庆企业跨境融资总规模达到新高度，同比增长35%...</p>',
                  category: '市场动态',
                  cover_img: 'https://example.com/news3.jpg',
                  view_count: 2134,
                  like_count: 156,
                  collect_count: 43,
                  forward_count: 25,
                  publish_date: '2024-03-13T16:45:00',
                  created_at: '2024-03-13T16:40:00'
                }];
              case 16:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 12]]);
      }))();
    },
    loadFAQ: function loadFAQ() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var _res$data5, res;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                _context6.next = 3;
                return _api.api.getFaqs({
                  per_page: 5,
                  sort: 'view_count'
                });
              case 3:
                res = _context6.sent;
                _this6.faqList = ((_res$data5 = res.data) === null || _res$data5 === void 0 ? void 0 : _res$data5.items) || [];
                _context6.next = 10;
                break;
              case 7:
                _context6.prev = 7;
                _context6.t0 = _context6["catch"](0);
                console.error('FAQ数据加载失败:', _context6.t0);
              case 10:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 7]]);
      }))();
    },
    loadInterpretations: function loadInterpretations() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var _res$data6, _res$data7, res, interpretations;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                _context7.next = 3;
                return _api.api.getInterpretations({
                  per_page: 6
                });
              case 3:
                res = _context7.sent;
                console.log('政策解读API返回:', res);

                // 尝试不同的数据结构
                interpretations = [];
                if ((_res$data6 = res.data) !== null && _res$data6 !== void 0 && _res$data6.items) {
                  interpretations = res.data.items;
                } else if ((_res$data7 = res.data) !== null && _res$data7 !== void 0 && _res$data7.data) {
                  interpretations = res.data.data;
                } else if (Array.isArray(res.data)) {
                  interpretations = res.data;
                }
                _this7.interpretationList = interpretations || [];
                console.log('处理后的政策解读列表:', _this7.interpretationList);

                // 如果没有数据，使用模拟数据
                if (_this7.interpretationList.length === 0) {
                  console.log('使用政策解读模拟数据');
                  _this7.interpretationList = [{
                    id: 1,
                    title: '跨境融资实务操作指南',
                    content: '<p>详细介绍跨境融资的实务操作流程和注意事项...</p>',
                    video_url: 'https://example.com/video1.mp4',
                    view_count: 356,
                    like_count: 53,
                    collect_count: 28,
                    publish_date: '2024-01-23T14:30:00'
                  }, {
                    id: 2,
                    title: '外汇收支便民措施详解',
                    content: '<p>全面解读最新的外汇收支便民措施...</p>',
                    video_url: 'https://example.com/video2.mp4',
                    view_count: 298,
                    like_count: 44,
                    collect_count: 25,
                    publish_date: '2024-02-01T09:15:00'
                  }, {
                    id: 3,
                    title: '重庆自贸区金融创新政策解读',
                    content: '<p>专门针对重庆自贸区的金融创新政策进行深入解读...</p>',
                    video_url: 'https://example.com/video3.mp4',
                    view_count: 387,
                    like_count: 58,
                    collect_count: 31,
                    publish_date: '2024-02-08T11:45:00'
                  }, {
                    id: 4,
                    title: '企业境外投资合规要点',
                    content: '<p>从合规角度详细讲解企业境外投资的各项要求...</p>',
                    video_url: 'https://example.com/video4.mp4',
                    view_count: 267,
                    like_count: 41,
                    collect_count: 22,
                    publish_date: '2024-02-15T15:20:00'
                  }, {
                    id: 5,
                    title: '测试政策解答',
                    content: '这是测试政策解答内容',
                    video_url: null,
                    view_count: 2,
                    like_count: 0,
                    collect_count: 0,
                    publish_date: '2025-06-22T00:00:00'
                  }, {
                    id: 6,
                    title: '跨境电商外汇支付新规解读',
                    content: '<p>针对跨境电商的最新外汇支付规定进行详细解读...</p>',
                    video_url: 'https://example.com/video6.mp4',
                    view_count: 234,
                    like_count: 36,
                    collect_count: 19,
                    publish_date: '2024-02-20T10:00:00'
                  }];
                }
                _context7.next = 16;
                break;
              case 12:
                _context7.prev = 12;
                _context7.t0 = _context7["catch"](0);
                console.error('政策解读数据加载失败:', _context7.t0);
                // API失败时使用模拟数据
                _this7.interpretationList = [{
                  id: 1,
                  title: '跨境融资实务操作指南',
                  content: '<p>详细介绍跨境融资的实务操作流程和注意事项...</p>',
                  video_url: 'https://example.com/video1.mp4',
                  view_count: 356,
                  like_count: 53,
                  collect_count: 28,
                  publish_date: '2024-01-23T14:30:00'
                }, {
                  id: 2,
                  title: '外汇收支便民措施详解',
                  content: '<p>全面解读最新的外汇收支便民措施...</p>',
                  video_url: 'https://example.com/video2.mp4',
                  view_count: 298,
                  like_count: 44,
                  collect_count: 25,
                  publish_date: '2024-02-01T09:15:00'
                }, {
                  id: 3,
                  title: '重庆自贸区金融创新政策解读',
                  content: '<p>专门针对重庆自贸区的金融创新政策进行深入解读...</p>',
                  video_url: 'https://example.com/video3.mp4',
                  view_count: 387,
                  like_count: 58,
                  collect_count: 31,
                  publish_date: '2024-02-08T11:45:00'
                }, {
                  id: 4,
                  title: '企业境外投资合规要点',
                  content: '<p>从合规角度详细讲解企业境外投资的各项要求...</p>',
                  video_url: 'https://example.com/video4.mp4',
                  view_count: 267,
                  like_count: 41,
                  collect_count: 22,
                  publish_date: '2024-02-15T15:20:00'
                }, {
                  id: 5,
                  title: '测试政策解答',
                  content: '这是测试政策解答内容',
                  video_url: null,
                  view_count: 2,
                  like_count: 0,
                  collect_count: 0,
                  publish_date: '2025-06-22T00:00:00'
                }, {
                  id: 6,
                  title: '跨境电商外汇支付新规解读',
                  content: '<p>针对跨境电商的最新外汇支付规定进行详细解读...</p>',
                  video_url: 'https://example.com/video6.mp4',
                  view_count: 234,
                  like_count: 36,
                  collect_count: 19,
                  publish_date: '2024-02-20T10:00:00'
                }];
              case 16:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 12]]);
      }))();
    },
    toSearch: function toSearch() {
      uni.navigateTo({
        url: '/pages/search/index'
      });
    },
    toPage: function toPage(url) {
      uni.switchTab({
        url: url
      });
    },
    toNewsDetail: function toNewsDetail(id) {
      uni.navigateTo({
        url: "/pages/news/detail?id=".concat(id)
      });
    },
    toFAQDetail: function toFAQDetail(id) {
      uni.navigateTo({
        url: "/pages/faq/detail?id=".concat(id)
      });
    },
    toInterpretationDetail: function toInterpretationDetail(id) {
      uni.navigateTo({
        url: "/pages/interpretation/detail?id=".concat(id)
      });
    },
    toNewsList: function toNewsList() {
      uni.navigateTo({
        url: '/pages/search/index?type=news'
      });
    },
    toFAQList: function toFAQList() {
      uni.navigateTo({
        url: '/pages/search/index?type=faq'
      });
    },
    toInterpretationList: function toInterpretationList() {
      uni.navigateTo({
        url: '/pages/search/index?type=interpretation'
      });
    },
    toBankListPage: function toBankListPage() {
      uni.navigateTo({
        url: '/pages/bank/list'
      });
    },
    refreshBanks: function refreshBanks() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var _res$data8, _res$data9, res, banks, mockBanks, shuffled, _mockBanks, _shuffled;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                _context8.prev = 0;
                _context8.next = 3;
                return _api.api.getBanks({
                  per_page: 3,
                  random: true
                });
              case 3:
                res = _context8.sent;
                console.log('刷新银行API返回:', res);

                // 尝试不同的数据结构
                banks = [];
                if ((_res$data8 = res.data) !== null && _res$data8 !== void 0 && _res$data8.items) {
                  banks = res.data.items;
                } else if ((_res$data9 = res.data) !== null && _res$data9 !== void 0 && _res$data9.data) {
                  banks = res.data.data;
                } else if (Array.isArray(res.data)) {
                  banks = res.data;
                }
                _this8.bankList = banks || [];

                // 如果没有获取到数据，随机显示模拟数据
                if (_this8.bankList.length === 0) {
                  mockBanks = [{
                    id: 1,
                    name: '中国银行重庆分行',
                    contact_person: '张经理',
                    phone: '023-********',
                    address: '重庆市渝中区解放碑步行街123号',
                    latitude: 29.559434,
                    longitude: 106.577011,
                    icon: 'https://example.com/bank1.png',
                    created_at: '2025-06-22T09:29:49'
                  }, {
                    id: 2,
                    name: '建设银行重庆分行',
                    contact_person: '李经理',
                    phone: '023-********',
                    address: '重庆市江北区观音桥步行街456号',
                    latitude: 29.574639,
                    longitude: 106.539285,
                    icon: 'https://example.com/bank2.png',
                    created_at: '2025-06-22T09:29:49'
                  }, {
                    id: 3,
                    name: '工商银行重庆分行',
                    contact_person: '王经理',
                    phone: '023-********',
                    address: '重庆市沙坪坝区三峡广场789号',
                    latitude: 29.544606,
                    longitude: 106.456878,
                    icon: null,
                    created_at: '2025-06-22T09:29:49'
                  }, {
                    id: 4,
                    name: '农业银行重庆分行',
                    contact_person: '刘经理',
                    phone: '023-********',
                    address: '重庆市九龙坡区杨家坪步行街456号',
                    latitude: 29.503143,
                    longitude: 106.511470,
                    icon: 'https://example.com/bank4.png',
                    created_at: '2025-06-22T09:29:49'
                  }, {
                    id: 5,
                    name: '交通银行重庆分行',
                    contact_person: '陈经理',
                    phone: '023-********',
                    address: '重庆市南岸区南坪步行街789号',
                    latitude: 29.523456,
                    longitude: 106.560789,
                    icon: null,
                    created_at: '2025-06-22T09:29:49'
                  }]; // 随机选择3个
                  shuffled = mockBanks.sort(function () {
                    return 0.5 - Math.random();
                  });
                  _this8.bankList = shuffled.slice(0, 3);
                }
                uni.showToast({
                  title: '已刷新',
                  icon: 'success'
                });
                _context8.next = 19;
                break;
              case 12:
                _context8.prev = 12;
                _context8.t0 = _context8["catch"](0);
                console.error('刷新银行列表失败:', _context8.t0);
                // 使用模拟数据并随机排序
                _mockBanks = [{
                  id: 1,
                  name: '中国银行重庆分行',
                  contact_person: '张经理',
                  phone: '023-********',
                  address: '重庆市渝中区解放碑步行街123号',
                  latitude: 29.559434,
                  longitude: 106.577011,
                  icon: 'https://example.com/bank1.png',
                  created_at: '2025-06-22T09:29:49'
                }, {
                  id: 2,
                  name: '建设银行重庆分行',
                  contact_person: '李经理',
                  phone: '023-********',
                  address: '重庆市江北区观音桥步行街456号',
                  latitude: 29.574639,
                  longitude: 106.539285,
                  icon: 'https://example.com/bank2.png',
                  created_at: '2025-06-22T09:29:49'
                }, {
                  id: 3,
                  name: '工商银行重庆分行',
                  contact_person: '王经理',
                  phone: '023-********',
                  address: '重庆市沙坪坝区三峡广场789号',
                  latitude: 29.544606,
                  longitude: 106.456878,
                  icon: null,
                  created_at: '2025-06-22T09:29:49'
                }, {
                  id: 4,
                  name: '农业银行重庆分行',
                  contact_person: '刘经理',
                  phone: '023-********',
                  address: '重庆市九龙坡区杨家坪步行街456号',
                  latitude: 29.503143,
                  longitude: 106.511470,
                  icon: 'https://example.com/bank4.png',
                  created_at: '2025-06-22T09:29:49'
                }, {
                  id: 5,
                  name: '交通银行重庆分行',
                  contact_person: '陈经理',
                  phone: '023-********',
                  address: '重庆市南岸区南坪步行街789号',
                  latitude: 29.523456,
                  longitude: 106.560789,
                  icon: null,
                  created_at: '2025-06-22T09:29:49'
                }]; // 随机选择3个
                _shuffled = _mockBanks.sort(function () {
                  return 0.5 - Math.random();
                });
                _this8.bankList = _shuffled.slice(0, 3);
                uni.showToast({
                  title: '已刷新',
                  icon: 'success'
                });
              case 19:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[0, 12]]);
      }))();
    },
    viewBankDetail: function viewBankDetail(bank) {
      uni.showModal({
        title: bank.name,
        content: "\u8054\u7CFB\u4EBA\uFF1A".concat(bank.contact_person, "\n\u7535\u8BDD\uFF1A").concat(bank.phone, "\n\u5730\u5740\uFF1A").concat(bank.address),
        showCancel: false
      });
    },
    callPhone: function callPhone(phone) {
      if (!phone || phone === '电话号码') {
        uni.showToast({
          title: '电话号码无效',
          icon: 'none'
        });
        return;
      }
      uni.showModal({
        title: '拨打电话',
        content: "\u662F\u5426\u62E8\u6253 ".concat(phone, "\uFF1F"),
        confirmText: '拨打',
        cancelText: '取消',
        success: function success(res) {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: phone,
              fail: function fail(err) {
                console.error('拨打电话失败:', err);
                uni.showToast({
                  title: '拨打失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },
    openMap: function openMap(bank) {
      var _this9 = this;
      if (!bank.address || bank.address === '地址') {
        uni.showToast({
          title: '地址信息无效',
          icon: 'none'
        });
        return;
      }

      // 如果有经纬度信息，直接使用
      if (bank.latitude && bank.longitude) {
        var latitude = typeof bank.latitude === 'number' ? bank.latitude : parseFloat(bank.latitude);
        var longitude = typeof bank.longitude === 'number' ? bank.longitude : parseFloat(bank.longitude);
        uni.openLocation({
          latitude: latitude,
          longitude: longitude,
          name: bank.name,
          address: bank.address,
          fail: function fail(err) {
            console.error('打开地图失败:', err);
            _this9.fallbackToAddressSearch(bank);
          }
        });
      } else {
        // 没有经纬度信息，使用地址搜索
        this.fallbackToAddressSearch(bank);
      }
    },
    fallbackToAddressSearch: function fallbackToAddressSearch(bank) {
      // 备用方案：使用地址搜索
      uni.showModal({
        title: '查看位置',
        content: "\u5730\u5740\uFF1A".concat(bank.address, "\n\n\u662F\u5426\u5728\u5730\u56FE\u4E2D\u641C\u7D22\u8BE5\u5730\u5740\uFF1F"),
        confirmText: '打开地图',
        cancelText: '取消',
        success: function success(res) {
          if (res.confirm) {
            // 尝试使用默认的重庆坐标
            uni.openLocation({
              latitude: 29.563761,
              longitude: 106.550464,
              name: bank.name,
              address: bank.address,
              fail: function fail(err) {
                console.error('打开地图失败:', err);
                uni.showToast({
                  title: '无法打开地图',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },
    recordPageView: function recordPageView() {
      // 记录页面访问量
      _api.api.recordInteraction({
        action: 'view',
        item_type: 'page',
        item_id: 'index'
      }).catch(function (err) {
        console.error('记录访问失败:', err);
      });
    },
    formatDate: function formatDate(dateStr) {
      var date = new Date(dateStr);
      var now = new Date();
      var diff = now - date;
      var days = Math.floor(diff / (1000 * 60 * 60 * 24));
      if (days === 0) {
        return '今天';
      } else if (days === 1) {
        return '昨天';
      } else if (days < 7) {
        return "".concat(days, "\u5929\u524D");
      } else {
        return date.toLocaleDateString();
      }
    },
    onBannerClick: function onBannerClick(banner) {
      if (!banner.link_url) {
        return;
      }

      // 记录轮播图点击事件
      this.recordBannerClick(banner);

      // 判断链接类型并进行相应跳转
      if (banner.link_url.startsWith('http://') || banner.link_url.startsWith('https://')) {
        // 外部链接，复制到剪贴板并提示
        uni.setClipboardData({
          data: banner.link_url,
          success: function success() {
            uni.showToast({
              title: '链接已复制到剪贴板',
              icon: 'none'
            });
          }
        });
      } else if (banner.link_url.startsWith('/pages/')) {
        // 内部页面跳转
        uni.navigateTo({
          url: banner.link_url,
          fail: function fail(err) {
            console.error('页面跳转失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else {
        // 其他格式链接，尝试作为内部页面跳转
        uni.navigateTo({
          url: banner.link_url,
          fail: function fail(err) {
            console.error('页面跳转失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    },
    // 记录轮播图点击事件
    recordBannerClick: function recordBannerClick(banner) {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _context9.prev = 0;
                _context9.next = 3;
                return _api.api.recordView({
                  content_type: 'banner',
                  content_id: banner.id,
                  action: 'click'
                });
              case 3:
                _context9.next = 8;
                break;
              case 5:
                _context9.prev = 5;
                _context9.t0 = _context9["catch"](0);
                console.error('记录轮播图点击失败:', _context9.t0);
              case 8:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[0, 5]]);
      }))();
    },
    onBannerImageError: function onBannerImageError(banner, index) {
      console.error("\u8F6E\u64AD\u56FE".concat(index + 1, "\u52A0\u8F7D\u5931\u8D25:"), banner);

      // 替换为默认图片
      var defaultImages = ['/static/images/banner/cq-sanxia.jpg', '/static/images/banner/cq-hongyadong.jpg', '/static/images/banner/cq-jiefangbei.jpg'];

      // 更新该轮播图的图片URL
      if (this.bannerList[index]) {
        this.$set(this.bannerList[index], 'image', defaultImages[index % 3]);
        console.log("\u8F6E\u64AD\u56FE".concat(index + 1, "\u5DF2\u66FF\u6362\u4E3A\u9ED8\u8BA4\u56FE\u7247:"), defaultImages[index % 3]);
      }
    },
    onBannerImageLoad: function onBannerImageLoad(banner) {
      if (banner && banner.id) {
        console.log("\u8F6E\u64AD\u56FE".concat(banner.id, "\u52A0\u8F7D\u6210\u529F:"), banner.title);
      } else {
        console.log('轮播图加载成功，但数据异常:', banner);
      }
    },
    getCurrentTime: function getCurrentTime() {
      var now = new Date();
      var year = now.getFullYear();
      var month = String(now.getMonth() + 1).padStart(2, '0');
      var day = String(now.getDate()).padStart(2, '0');
      var hour = String(now.getHours()).padStart(2, '0');
      var minute = String(now.getMinutes()).padStart(2, '0');
      return "".concat(year, "\u5E74").concat(month, "\u6708").concat(day, "\u65E5 ").concat(hour, ":").concat(minute);
    },
    getVideoThumbnail: function getVideoThumbnail(item, index) {
      // 如果有真实的缩略图字段，使用真实的
      if (item && item.thumbnail) {
        return item.thumbnail;
      }
      // 如果有封面图字段
      if (item && item.cover_image) {
        return item.cover_image;
      }
      // 如果有视频地址且不为null，尝试生成缩略图
      if (item && item.video_url && item.video_url !== null) {
        // 对于示例URL，尝试生成缩略图URL
        if (item.video_url.includes('example.com')) {
          return item.video_url.replace('.mp4', '_thumb.jpg');
        }
        // 对于其他真实视频URL，可以根据视频平台的规则生成缩略图
        // 这里可以根据实际需求扩展
        return item.video_url.replace('.mp4', '_thumb.jpg');
      }
      // 返回空字符串，使用CSS渐变背景
      return '';
    },
    onVideoThumbnailError: function onVideoThumbnailError(item, index) {
      console.log("\u89C6\u9891".concat(index + 1, "\u7F29\u7565\u56FE\u52A0\u8F7D\u5931\u8D25:"), item);
      // 缩略图加载失败时，可以设置一个标记使用CSS背景
      this.$set(this.interpretationList[index], 'thumbnail_error', true);
    },
    getNewsCover: function getNewsCover(news) {
      // 如果已经标记为加载失败，返回空
      if (news && news.cover_error) {
        return '';
      }

      // 优先使用后端的cover_img字段
      if (news && news.cover_img && !news.cover_img.includes('example.com')) {
        return news.cover_img;
      }
      // 其次使用thumbnail字段
      if (news && news.thumbnail && !news.thumbnail.includes('example.com')) {
        return news.thumbnail;
      }
      // 再次使用cover_image字段
      if (news && news.cover_image && !news.cover_image.includes('example.com')) {
        return news.cover_image;
      }
      // 如果有image字段也可以使用
      if (news && news.image && !news.image.includes('example.com')) {
        return news.image;
      }
      // 返回空字符串使用占位符
      return '';
    },
    onNewsCoverError: function onNewsCoverError(news) {
      console.log('新闻封面加载失败:', news);
      // 封面加载失败时，设置标记使用占位符
      this.$set(news, 'cover_error', true);
    },
    formatViewCount: function formatViewCount(count) {
      if (!count || count === 0) return '0';
      if (count < 1000) return count.toString();
      if (count < 10000) return (count / 1000).toFixed(1) + 'k';
      return (count / 10000).toFixed(1) + 'w';
    },
    getNewsCategoryIcon: function getNewsCategoryIcon(category) {
      var iconMap = {
        '政策发布': '📋',
        '会议活动': '🏛️',
        '数据统计': '📊',
        '通知公告': '📢',
        '新闻动态': '📰',
        '行业资讯': '💼',
        '法规解读': '⚖️',
        '业务指导': '📖',
        '市场分析': '📈',
        '市场动态': '📈',
        '测试分类': '🧪',
        '政策解读': '📋',
        '金融创新': '💰',
        '跨境融资': '🌐',
        '监管政策': '⚖️'
      };
      return iconMap[category] || '📰';
    },
    getNewsCategoryType: function getNewsCategoryType(category) {
      var typeMap = {
        '政策发布': 'policy',
        '会议活动': 'meeting',
        '数据统计': 'data',
        '通知公告': 'notice',
        '新闻动态': 'news',
        '行业资讯': 'industry',
        '法规解读': 'law',
        '业务指导': 'guide',
        '市场分析': 'market',
        '市场动态': 'market',
        '测试分类': 'test',
        '政策解读': 'policy',
        '金融创新': 'finance',
        '跨境融资': 'finance',
        '监管政策': 'law'
      };
      return typeMap[category] || 'default';
    },
    getBankIcon: function getBankIcon(bank) {
      // 如果已经标记为加载失败，返回空
      if (bank && bank.icon_error) {
        return '';
      }

      // 优先使用后端的icon字段
      if (bank && bank.icon && !bank.icon.includes('example.com')) {
        return bank.icon;
      }

      // 返回空字符串使用占位符
      return '';
    },
    onBankIconError: function onBankIconError(bank) {
      console.log('银行图标加载失败:', bank);
      // 图标加载失败时，设置标记使用占位符
      this.$set(bank, 'icon_error', true);
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 45:
/*!***************************************************************************************************************************************!*\
  !*** F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?vue&type=style&index=0&id=********&lang=scss&scoped=true& ***!
  \***************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_********_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=********&lang=scss&scoped=true& */ 46);
/* harmony import */ var _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_********_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_********_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_********_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_********_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_LeStoreDownload_HBuilderX_4_36_2024112817_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_********_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 46:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/index/index.vue?vue&type=style&index=0&id=********&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[35,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map